import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
//添加系统参数
export const addXtcs = data => createAPI(BASE_URL+"/settingList/addXtcs", 'post',data)
//删除系统参数
export const deleteXtcs = data => createAPI(BASE_URL+"/settingList/deleteXtcs", 'post',data)
//修改系统参数
export const updateXtcs = data => createAPI(BASE_URL+"/settingList/updateXtcs", 'post',data)
//查询全部系统参数 带分页
export const getXtcsPage = data => createAPI(BASE_URL+"/settingList/getXtcsPage", 'get',data)

//获取参数值计量单位
export const getcszjldw = data => createAPI(BASE_URL+"/dmb/getcszjldw", 'get',data)