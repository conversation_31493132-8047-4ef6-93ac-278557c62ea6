import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1.添加载体接收传递
export const saveZtJscd = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/saveZtJscd", 'post',data)
//2.修改载体接收传递
export const updateZtJscd = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/updateZtJscd", 'post',data)
//3.删除载体传递接收
export const removeZtJscd = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/removeZtJscd", 'post',data)
//4.分页查询载体接收传递
export const selectZtJscdPage = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/selectZtJscdPage", 'get',data)
//5.根据记录id查询接收传递
export const getZtJscdByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/getZtJscdByJlid", 'get',data)
//根据原jlid查询载体清单
export const getZtqdListByYjlid = data => createAPI(BASE_URL+"/ztgl/ztqd/getZtqdListByYjlid", 'get',data)
//6.根据实例id查询接收传递
export const getZtJscdBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/getZtJscdBySlid", 'get',data)
export const getJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/getJlidBySlid", 'get',data)
export const getJlidBySlid2 = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_jscd/getJlidBySlid", 'get',data)
export const addZtJscdDj = data => createAPI(BASE_URL+"/ztgl-jscddj/addZtJscdDj", 'post',data)
export const selectZtJscdDj = data => createAPI(BASE_URL+"/ztgl-jscddj/selectZtJscdDj", 'get',data)