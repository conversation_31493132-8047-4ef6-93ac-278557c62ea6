webpackJsonp([8],{b5Lg:function(t,e){},kEDQ:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("Xxa5"),r=a.n(i),n=a("exGp"),l=a.n(n),o=a("XY/r"),s={name:"",components:{},props:{},data:function(){return{active:1,form:{id:"",reportOrganiaztion:"1",manufacturerName:"",engineerName:"",manufacturerPhone:"",replaceFlag:"2",equipmentSerialNumber:this.$route.query.equipmentSerialNumber,equipmentSerialNumberNew:"",maintenanceStatus:"",equipmentCode:this.$route.query.equipmentCode,reason:"",faultHandlingTime:"",assignee:""}}},computed:{},watch:{},methods:{init:function(){var t=this;return l()(r.a.mark(function e(){var a;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(o.f)(t.form);case 2:1e4==(a=e.sent).code&&a.data.id&&(t.form=a.data);case 4:case"end":return e.stop()}},e,t)}))()},xyb:function(){var t=this;return l()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t.form.maintenanceStatus=t.active,Object(o.j)(t.form).then(function(e){1e4==e.code&&(t.form.id=e.data)}),t.active++;case 3:case"end":return e.stop()}},e,t)}))()},syb:function(){this.active--},dcbutton:function(){var t=this;return l()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t.form.maintenanceStatus=t.active,Object(o.j)(t.form).then(function(e){1e4==e.code&&(t.form.id=e.data,Object(o.b)(t.form).then(function(e){t.dom_download(e,"故障处理信息.xls"),t.$message({message:"打印成功",type:"success"})}))});case 2:case"end":return e.stop()}},e,t)}))()},dom_download:function(t,e){var a=new Blob([t]),i=window.URL.createObjectURL(a),r=document.createElement("a");console.log("dom",r),r.style.display="none",r.href=i,r.setAttribute("download",e),document.body.appendChild(r),r.click()}},created:function(){},mounted:function(){this.init();var t=new Date,e=t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0");this.form.faultHandlingTime=e,this.form.equipmentCode=this.$route.query.equipmentCode,this.form.equipmentSerialNumber=this.$route.query.equipmentSerialNumber},beforeCreate:function(){},beforeMount:function(){},beforeUpdate:function(){},updated:function(){},beforeDestroy:function(){},destroyed:function(){},activated:function(){}},c={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"box"},[t._m(0),t._v(" "),1==t.form.replaceFlag?a("div",{staticStyle:{width:"70%",margin:"0 auto","margin-top":"50px"}},[a("el-steps",{attrs:{active:t.active,"finish-status":"success"}},[a("el-step",{attrs:{title:"步骤一"}}),t._v(" "),a("el-step",{attrs:{title:"步骤二"}}),t._v(" "),a("el-step",{attrs:{title:"步骤三"}}),t._v(" "),a("el-step",{attrs:{title:"步骤四"}}),t._v(" "),a("el-step",{attrs:{title:"步骤五"}}),t._v(" "),a("el-step",{attrs:{title:"步骤六"}}),t._v(" "),a("el-step",{attrs:{title:"步骤七"}})],1)],1):a("div",{staticStyle:{width:"70%",margin:"0 auto","margin-top":"50px"}},[a("el-steps",{attrs:{active:t.active,"finish-status":"success"}},[a("el-step",{attrs:{title:"步骤一"}}),t._v(" "),a("el-step",{attrs:{title:"步骤二"}}),t._v(" "),a("el-step",{attrs:{title:"步骤三"}}),t._v(" "),a("el-step",{attrs:{title:"步骤四"}}),t._v(" "),a("el-step",{attrs:{title:"步骤五"}})],1)],1),t._v(" "),1===t.active?a("div",{staticStyle:{width:"510px",margin:"0 auto","margin-top":"100px"}},[a("div",{staticClass:"bt-title"},[t._v("\n        项目经理向省分中心以及国家中心维护组报备\n      ")]),t._v(" "),a("div",{staticStyle:{display:"flex","margin-top":"20px"}},[a("div",{staticClass:"label-title"},[t._v("\n          报备组织：\n        ")]),t._v(" "),a("div",{staticStyle:{"margin-left":"10px"}},[a("el-radio",{attrs:{label:"1"},model:{value:t.form.reportOrganiaztion,callback:function(e){t.$set(t.form,"reportOrganiaztion",e)},expression:"form.reportOrganiaztion"}},[t._v("省中心")]),t._v(" "),a("el-radio",{attrs:{label:"2"},model:{value:t.form.reportOrganiaztion,callback:function(e){t.$set(t.form,"reportOrganiaztion",e)},expression:"form.reportOrganiaztion"}},[t._v("省移动")]),t._v(" "),a("el-radio",{attrs:{label:"3"},model:{value:t.form.reportOrganiaztion,callback:function(e){t.$set(t.form,"reportOrganiaztion",e)},expression:"form.reportOrganiaztion"}},[t._v("北京移动组")])],1)])]):t._e(),t._v(" "),2===t.active?a("div",{staticStyle:{width:"600px",margin:"0 auto","margin-top":"100px"}},[a("div",{staticClass:"bt-title",staticStyle:{position:"relative"}},[t._v("\n        协调厂家工程师到现场处理故障，项目经理作全程旁站陪同。\n        "),a("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[a("div",[a("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[a("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),t._v(" "),a("div",{staticClass:"tszt"},[t._v("提示")])]),t._v(" "),a("div",{staticClass:"smzt"},[t._v("\n              项目经理去省移动公司，在思安设备上取得故障处理授权（二维码）。对于时限要求高的故障（2小时以内），直接去机房，现场录入故障处理授权。\n            ")])]),t._v(" "),a("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",right:"0px",top:"7px"},attrs:{slot:"reference"},slot:"reference"})])],1),t._v(" "),a("div",{staticStyle:{display:"flex","margin-top":"20px","justify-content":"center"}},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"120px","label-position":"left"}},[a("el-form-item",{attrs:{label:"厂家姓名"}},[a("el-input",{model:{value:t.form.manufacturerName,callback:function(e){t.$set(t.form,"manufacturerName",e)},expression:"form.manufacturerName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"工程师姓名"}},[a("el-input",{model:{value:t.form.engineerName,callback:function(e){t.$set(t.form,"engineerName",e)},expression:"form.engineerName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"联系方式"}},[a("el-input",{model:{value:t.form.manufacturerPhone,callback:function(e){t.$set(t.form,"manufacturerPhone",e)},expression:"form.manufacturerPhone"}})],1)],1)],1)]):t._e(),t._v(" "),3===t.active?a("div",{staticStyle:{width:"718px",margin:"0 auto","margin-top":"100px"}},[t._m(1),t._v(" "),a("div",{staticStyle:{display:"flex","margin-top":"20px"}},[a("div",{staticClass:"label-title"},[t._v("\n          配件是否需要更换：\n        ")]),t._v(" "),a("div",{staticStyle:{"margin-left":"10px"}},[a("el-radio",{attrs:{label:"1"},model:{value:t.form.replaceFlag,callback:function(e){t.$set(t.form,"replaceFlag",e)},expression:"form.replaceFlag"}},[t._v("是")]),t._v(" "),a("el-radio",{attrs:{label:"2"},model:{value:t.form.replaceFlag,callback:function(e){t.$set(t.form,"replaceFlag",e)},expression:"form.replaceFlag"}},[t._v("否")])],1)])]):t._e(),t._v(" "),4===t.active&&1==t.form.replaceFlag?a("div",{staticStyle:{width:"718px",margin:"0 auto","margin-top":"100px"}},[a("div",{staticClass:"bt-title"},[t._v("\n        记录故障备件序列号和新配件序列号，更换故障配件，新配件试运行。\n      ")]),t._v(" "),a("div",{staticStyle:{display:"flex","margin-top":"20px","justify-content":"center"}},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"160px","label-position":"left"}},[a("el-form-item",{attrs:{label:"故障设备序列号"}},[a("el-input",{attrs:{disabled:""},model:{value:t.form.equipmentSerialNumber,callback:function(e){t.$set(t.form,"equipmentSerialNumber",e)},expression:"form.equipmentSerialNumber"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"新配件序列号"}},[a("el-input",{model:{value:t.form.equipmentSerialNumberNew,callback:function(e){t.$set(t.form,"equipmentSerialNumberNew",e)},expression:"form.equipmentSerialNumberNew"}})],1)],1)],1)]):t._e(),t._v(" "),5===t.active&&1==t.form.replaceFlag||4===t.active&&2==t.form.replaceFlag?a("div",{staticStyle:{width:"718px",margin:"0 auto","margin-top":"100px"}},[a("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[t._v("\n        国家中心维护组确认故障恢复，运行正常。\n      ")])]):t._e(),t._v(" "),6===t.active&&1==t.form.replaceFlag?a("div",{staticStyle:{width:"718px",margin:"0 auto","margin-top":"100px"}},[a("div",{staticClass:"bt-title"},[t._v("\n        厂家工程师将故障配件（除核心设备及配件）返厂。核心设备及配件带回省移动公司接口人处，进入待处理库，等待接口人的统一处理\n      ")])]):t._e(),t._v(" "),7===t.active&&1==t.form.replaceFlag||5===t.active&&2==t.form.replaceFlag?a("div",{staticStyle:{width:"718px",margin:"0 auto","margin-top":"100px"}},[a("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[t._v("\n        故障处理单\n      ")]),t._v(" "),a("div",{staticStyle:{display:"flex","margin-top":"20px","justify-content":"center"}},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"180px","label-position":"left"}},[a("el-form-item",{attrs:{label:"故障设备编号："}},[a("el-input",{attrs:{disabled:""},model:{value:t.form.equipmentCode,callback:function(e){t.$set(t.form,"equipmentCode",e)},expression:"form.equipmentCode"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"故障原因："}},[a("el-input",{model:{value:t.form.reason,callback:function(e){t.$set(t.form,"reason",e)},expression:"form.reason"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"故障处理时间："}},[a("el-input",{model:{value:t.form.faultHandlingTime,callback:function(e){t.$set(t.form,"faultHandlingTime",e)},expression:"form.faultHandlingTime"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"故障处理负责人："}},[a("el-input",{model:{value:t.form.assignee,callback:function(e){t.$set(t.form,"assignee",e)},expression:"form.assignee"}})],1)],1)],1)]):t._e(),t._v(" "),a("div",{staticStyle:{display:"flex",width:"500px","justify-content":"center",margin:"0 auto","margin-top":"60px"}},[1!=t.active?a("div",{staticClass:"buttonw btnc",on:{click:t.syb}},[t._v("上一步")]):t._e(),t._v(" "),7===t.active&&1==t.form.replaceFlag||5===t.active&&2==t.form.replaceFlag?t._e():a("div",{staticClass:"buttonw btnc1",on:{click:t.xyb}},[t._v("\n        下一步\n      ")]),t._v(" "),a("div",{staticClass:"buttonw btnc2",on:{click:t.dcbutton}},[t._v("打印故障处理单")])])])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"top-box"},[e("div",[e("img",{attrs:{src:a("hsSF"),alt:""}})]),this._v(" "),e("div",{staticClass:"top-title"},[this._v("故障处理流程")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"bt-title"},[this._v("\n        携带配件到现场,现场处理故障。"),e("br"),this._v("判断是否需要更换新配件，如果不需要更换配件。"),e("br"),this._v("例如重启设备或者重新插拔线缆接头等。处理后，试运行。\n      ")])}]};var m=a("VU/8")(s,c,!1,function(t){a("b5Lg")},"data-v-1f0a3926",null);e.default=m.exports}});
//# sourceMappingURL=8.5d19b62b87911335d67c.js.map