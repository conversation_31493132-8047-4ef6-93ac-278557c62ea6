webpackJsonp([7],{"/+KL":function(t,e){},nrZA:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=o("Xxa5"),a=o.n(n),r=o("exGp"),i=o.n(r),c=o("XY/r"),l=(o("gyMJ"),{name:"",components:{},props:{},data:function(){return{form:{jfbh:"",jfdd:"",sblx:"",xbbh:"",qysj:"",qydz:"",relocationCabinetCode:"",relocationCabinetName:"",relocationComputerRoomName:"",relocationInstitution:"",relocationLocation:"",area:""},flag:"",sfdc:!1}},computed:{},watch:{},methods:{queryEquipmentByCondition:function(){var t=this;return i()(a.a.mark(function e(){return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:console.log(t.$route.query.equipmentId,"this.$route.query.equipmentCode"),Object(c.h)({equipmentCode:t.$route.query.equipmentId}).then(function(e){console.log(e),t.form=e.data[0],t.form.relocationCabinetCode=t.$route.query.relocationCabinetCode,t.form.relocationCabinetName=t.$route.query.relocationCabinetName,t.form.relocationComputerRoomName=t.$route.query.relocationComputerRoomName,t.form.relocationInstitution=t.$route.query.relocationInstitution,t.form.relocationLocation=t.$route.query.relocationLocation,t.form.area=t.$route.query.area,t.flag=t.$route.query.flag});case 2:case"end":return e.stop()}},e,t)}))()},savetj:function(){var t=this;return i()(a.a.mark(function e(){return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}},e,t)}))()},dcbutton:function(){var t=this;return i()(a.a.mark(function e(){return a.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("1"!=t.flag){e.next=5;break}return t.$confirm("目前设备所在机柜与迁移机柜一致，是否确认提交？","提示",{cancelButtonClass:"btn-custom-cancel",confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(c.n)(t.form).then(function(e){1e4==e.code&&(t.$message.success("提交成功"),t.flag="1",Object(c.d)(t.form).then(function(e){t.dom_download(e,"设备迁移信息.xls")}))})}).catch(function(){}),e.abrupt("return");case 5:Object(c.n)(t.form).then(function(e){1e4==e.code&&(t.$message.success("提交成功"),t.flag="1",Object(c.d)(t.form).then(function(e){t.dom_download(e,"设备迁移信息.xls")}))});case 6:case"end":return e.stop()}},e,t)}))()},dom_download:function(t,e){var o=new Blob([t]),n=window.URL.createObjectURL(o),a=document.createElement("a");console.log("dom",a),a.style.display="none",a.href=n,a.setAttribute("download",e),document.body.appendChild(a),a.click()}},created:function(){},mounted:function(){this.queryEquipmentByCondition()},beforeCreate:function(){},beforeMount:function(){},beforeUpdate:function(){},updated:function(){},beforeDestroy:function(){},destroyed:function(){},activated:function(){}}),s={render:function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"box"},[t._m(0),t._v(" "),o("div",{staticStyle:{width:"60%",margin:"0 auto","margin-top":"20px"}},[o("el-form",{ref:"form",attrs:{model:t.form,"label-width":"140px","label-position":"left"}},[o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"机房编号"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.computerRoomCode,callback:function(e){t.$set(t.form,"computerRoomCode",e)},expression:"form.computerRoomCode"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"机房地点"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.computerRoomName,callback:function(e){t.$set(t.form,"computerRoomName",e)},expression:"form.computerRoomName"}})],1)],1),t._v(" "),o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"设备类型"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.csm,callback:function(e){t.$set(t.form,"csm",e)},expression:"form.csm"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"设备编号"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.equipmentCode,callback:function(e){t.$set(t.form,"equipmentCode",e)},expression:"form.equipmentCode"}})],1)],1),t._v(" "),o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"迁移所在地"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.relocationLocation,callback:function(e){t.$set(t.form,"relocationLocation",e)},expression:"form.relocationLocation"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"迁移所在机构"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.relocationInstitution,callback:function(e){t.$set(t.form,"relocationInstitution",e)},expression:"form.relocationInstitution"}})],1)],1),t._v(" "),o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"迁移机房"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.relocationComputerRoomName,callback:function(e){t.$set(t.form,"relocationComputerRoomName",e)},expression:"form.relocationComputerRoomName"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"迁移机柜"}},[o("el-input",{attrs:{disabled:""},model:{value:t.form.relocationCabinetName,callback:function(e){t.$set(t.form,"relocationCabinetName",e)},expression:"form.relocationCabinetName"}})],1)],1)])],1),t._v(" "),o("div",{staticStyle:{display:"flex",width:"160px",margin:"0 auto","margin-top":"20px"}},[o("div",{staticClass:"buttonw btnc2",on:{click:function(e){return t.dcbutton()}}},[t._v("提交并导出")])])])},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"top-box"},[e("div",[e("img",{attrs:{src:o("hsSF"),alt:""}})]),this._v(" "),e("div",{staticClass:"top-title"},[this._v("设备迁移信息")])])}]};var m=o("VU/8")(l,s,!1,function(t){o("/+KL")},"data-v-2fadf3c1",null);e.default=m.exports}});
//# sourceMappingURL=7.6d32dc6049ce91385b54.js.map