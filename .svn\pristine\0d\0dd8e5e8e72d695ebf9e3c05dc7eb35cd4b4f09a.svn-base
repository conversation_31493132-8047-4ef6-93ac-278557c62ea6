<!--  -->
<template>
  <div class="box">
    <div class="top-box">
      <div>
        <img src="./img/title.png" alt="" />
      </div>
      <div class="top-title">巡检流程</div>
    </div>

    <div style="width: 70%;margin: 0 auto;margin-top: 50px;">
      <el-steps :active="active" finish-status="success">
        <el-step title="1.机房环境"></el-step>
        <el-step title="2.机柜"></el-step>
        <el-step title="3.设备"></el-step>
        <el-step title="4.线缆"></el-step>
        <!-- <el-step title="巡查异常情况明细表"></el-step> -->
      </el-steps>
    </div>
    <div style="width: 640px;margin: 0 auto;margin-top: 100px;" v-if="active === 1">
      <div class="bt-title" style="text-align: center;">
        1.机房环境
      </div>
      <div style="display: flex;justify-content: center;margin-top: 20px;">
        <el-form ref="form" :model="form" label-width="260px" label-position="left">
          <!-- <el-form-item label="机房温度是否正常" style="position: relative;">
            <el-popover placement="right" width="200" trigger="hover">
              <div>
                <div style="display:flex;margin-bottom:10px">
                  <i
                    class="el-icon-info"
                    style="color:#409eef;    position: relative;
    top: 2px;"
                  ></i>
                  <div class="tszt">提示</div>
                </div>
                <div class="smzt">
                  传感器自动获取
                </div>
              </div>
              <i
                class="el-icon-info"
                style="color:#409eef;position: absolute; top: 14px;
    left: -90px;"
                slot="reference"
              ></i>
            </el-popover>
            <el-radio-group v-model="form.temperature">
              <el-radio
                v-for="item in sflist"
                :label="item.label"
                :key="item.label"
                >{{ item.value }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="机房湿度是否正常" style="position: relative;">
            <el-popover placement="right" width="200" trigger="hover">
              <div>
                <div style="display:flex;margin-bottom:10px">
                  <i
                    class="el-icon-info"
                    style="color:#409eef;    position: relative;
    top: 2px;"
                  ></i>
                  <div class="tszt">提示</div>
                </div>
                <div class="smzt">
                  传感器自动获取
                </div>
              </div>
              <i
                class="el-icon-info"
                style="color:#409eef;position: absolute; top: 14px;
    left: -90px;"
                slot="reference"
              ></i>
            </el-popover>
            <el-radio-group v-model="form.humidity">
              <el-radio
                v-for="item in sflist"
                :label="item.label"
                :key="item.label"
                >{{ item.value }}</el-radio
              >
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="机房清洁度是否正常">
            <el-radio-group v-model="form.cleanliness">
              <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="机房空调是否正常">
            <el-radio-group v-model="form.airConditioning">
              <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="机房动力环境系统是否正常">
            <el-radio-group v-model="form.powerEnvironmentSystem">
              <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div style="width: 720px;margin: 0 auto;margin-top: 50px;" v-if="active === 2">
      <div class="bt-title" style="text-align: center;">
        2.机柜
      </div>
      <span >{{ msg }}</span>
      <el-form class="inputcss" ref="form" :model="form" label-width="100px" label-position="left"
        style="margin-top: 20px;">
        <!-- <div style="display: flex;">
          <el-form-item label="机房名称">
            <el-input v-model="form.jfmc"></el-input>
          </el-form-item>
          <el-form-item label="机房编号" style="margin-left: 20px;">
            <el-input v-model="form.jfbh"></el-input>
          </el-form-item>
        </div> -->
        <div style="display: flex;">
          <el-form-item label="机柜名称">
            <el-input v-model="form.jgmc"></el-input>
          </el-form-item>
          <el-form-item label="机柜编号" style="margin-left: 20px;">
            <el-input v-model="form.jgbh"></el-input>
          </el-form-item>
          <div class="cxbtn" @click="queryCabinetByCondition">查询</div>
        </div>
      </el-form>
      <div style="height: 270px;overflow-y: auto;">
        <div style="display: flex;align-items: center;margin-top: 20px;border-bottom: 1px solid rgba(225,225,225,1);"
          v-for="item in jglist">
          <div style="font-family: SourceHanSansSC-Bold;
font-size: 20px;
color: #003396;
font-weight: 700;
margin-right: 42px;width: 120px;">
            {{ item.cabinetName }}
          </div>
          <div>
            <el-form ref="form" :model="form" label-width="220px" label-position="left">
              <el-form-item label="机柜温度是否正常" style="position: relative;">
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;  position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      传感器自动获取
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; top: 14px; left: -60px;" slot="reference"></i>
                </el-popover>
                <el-radio-group v-model="item.temperature">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="机柜湿度是否正常" style="position: relative;">
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef; position: relative; top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      传感器自动获取
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; top: 14px; left: -60px;" slot="reference"></i>
                </el-popover>
                <el-radio-group v-model="item.dust">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="机柜门是否常闭">
                <el-popover placement="right" width="200" trigger="hover">
                  <div>
                    <div style="display:flex;margin-bottom:10px">
                      <i class="el-icon-info" style="color:#409eef;  position: relative;top: 2px;"></i>
                      <div class="tszt">提示</div>
                    </div>
                    <div class="smzt">
                      传感器自动获取
                    </div>
                  </div>
                  <i class="el-icon-info" style="color:#409eef;position: absolute; top: 14px; left: -60px;" slot="reference"></i>
                </el-popover>
                <el-radio-group v-model="item.close">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="机柜电力是否正常">
                <el-radio-group v-model="item.humidity">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="机柜内是否有灰尘">
                <el-radio-group v-model="item.cleanliness">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 1600px;margin: 0 auto;margin-top: 50px;" v-if="active === 3">
      <div class="bt-title" style="text-align: center;">
        3.设备
      </div>
      <el-form class="inputcss" ref="form" :model="form" label-width="100px" label-position="left"
        style="margin-top: 20px;">
        <!-- <div style="display: flex;">
          <el-form-item label="机房名称">
            <el-input v-model="form.jfmc"></el-input>
          </el-form-item>
          <el-form-item label="机房编号" style="margin-left: 20px;">
            <el-input v-model="form.jfbh"></el-input>
          </el-form-item>
        </div> -->
        <div style="display: flex;">
          <el-form-item label="机柜名称">
            <el-input v-model="form.jgmc"></el-input>
          </el-form-item>
          <el-form-item label="机柜编号" style="margin-left: 20px;">
            <el-input v-model="form.jgbh"></el-input>
          </el-form-item>
          <el-form-item label="设备名称" style="margin-left: 20px;">
            <el-input v-model="form.sbmc"></el-input>
          </el-form-item>
          <el-form-item label="设备编号" style="margin-left: 20px;">
            <el-input v-model="form.sbbh"></el-input>
          </el-form-item>
          <div class="cxbtn" @click="queryEquipmentByCondition">查询</div>
        </div>
        <!-- <div style="display: flex;">

        </div> -->
      </el-form>
      <div style="height: 386px;overflow-y: auto;">
        <div style="display: flex;align-items: center;margin-top: 20px;border: 1px solid #c0c4cc;padding-left: 20px;"
          v-for="item in sblist">
          <div style="font-family: SourceHanSansSC-Bold;
font-size: 20px;
color: #003396;
font-weight: 700;
margin-right: 42px;
width: 180px;
text-align: center;">
            {{ item.equipmentName }}
          </div>
          <div class="sb-box3">
            <el-form ref="form" :model="form" label-width="220px" label-position="left">
              <div style="display: flex;justify-content: space-between;
    width: 1330px;">
                <el-form-item label="设备是否在线">
                  <el-radio-group v-model="item.equipmentOnline">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备电力是否正常">
                  <el-radio-group v-model="item.equipmentPower">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备运行声音是否正常">
                  <el-radio-group v-model="item.equipmentRunningSound">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div style="display: flex;justify-content: space-between;
    width: 1330px;">
                <el-form-item label="设备是否有告警">
                  <el-radio-group v-model="item.equipmentWarm">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备是否有松动">
                  <el-radio-group v-model="item.equipmentLoosen">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备是否有宕机">
                  <el-radio-group v-model="item.equipmentDowntime">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div style="display: flex;justify-content: space-between;
    width: 1330px;">
                <el-form-item label="服务器硬盘是否正常">
                  <el-radio-group v-model="item.equipmentHardDisk">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备光模块是否齐全">
                  <el-radio-group v-model="item.equipmentOhticalModule">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备电源模块是否正常">
                  <el-radio-group v-model="item.equipmentPowerModule">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div style="display: flex;width: 1330px;justify-content: space-between;">
                <el-form-item label="设备风扇是否正常">
                  <el-radio-group v-model="item.equipmentFan">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备温度是否正常">
                  <el-radio-group v-model="item.equipmentTemperature">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备端口是否正常">
                  <el-radio-group v-model="item.equipmentPort">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div style="display: flex;
    width: 1330px;justify-content: space-between;">
                <el-form-item label="设备光纤是否正常">
                  <el-radio-group v-model="item.equipmentOpticalFiber">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备网线是否正常" style="margin-left: 17px;">
                  <el-radio-group v-model="item.equipmentNetworkCable">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="设备标签是否正常" style="margin-left: 17px;">
                  <el-radio-group v-model="item.equipmentLabel">
                    <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 720px;margin: 0 auto;margin-top: 50px;" v-if="active === 4">
      <div class="bt-title" style="text-align: center;">
        4.线缆
      </div>
      <div style="height: 410px;overflow-y: auto;">
        <div style="display: flex;align-items: center;margin-top: 20px;border-bottom: 1px solid rgba(225,225,225,1);"
          v-for="item in lxlist">
          <div style="font-family: SourceHanSansSC-Bold;
font-size: 20px;
color: #003396;
font-weight: 700;
margin-right: 42px;">
            {{ item.equipmentName }}
          </div>
          <div>
            <el-form ref="form" :model="form" label-width="220px" label-position="left">
              <el-form-item label="线缆有无破损断裂">
                <el-radio-group v-model="item.cableDamaged">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="线缆扎带是否正常">
                <el-radio-group v-model="item.cableRibbon">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="线缆接头是否有松动">
                <el-radio-group v-model="item.cableJoint">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="线缆标签是否正常">
                <el-radio-group v-model="item.cableLabel">
                  <el-radio v-for="item in sflist" :label="item.label" :key="item.label" :class="item.className">{{ item.value }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 1120px;margin: 0 auto;margin-top: 50px;" v-if="active === 5">
      <div class="bt-title" style="text-align: center;">
        巡检异常情况明细表
      </div>
      <div style="display: flex;justify-content: space-between;margin-top: 30px">
        <div class="fbtbox">
          <div class="labels">巡检机房名称</div>
          <div class="values">NPJ122000111000000</div>
        </div>
        <div class="fbtbox">
          <div class="labels">巡检时间</div>
          <div class="values">2024-10-12 11：00：00</div>
        </div>
        <div class="fbtbox">
          <div class="labels">巡检人员</div>
          <div class="values">张三</div>
        </div>
      </div>
      <div style="width: 100%;margin-top: 20px;">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="tableHeaderCellStyle"
          :cell-style="tableCellStyle" max-height="800px">
          <el-table-column prop="sbmc" label="设备名称" align="center">
          </el-table-column>
          <el-table-column prop="xh" label="型号" align="center">
          </el-table-column>
          <el-table-column prop="jgwz" label="机柜位置" align="center">
          </el-table-column>
          <el-table-column prop="xlh" label="序列号" align="center">
          </el-table-column>
          <el-table-column prop="gzqkms" label="故障情况描述" align="center">
          </el-table-column>
          <el-table-column prop="clff" label="处理方法" align="center">
          </el-table-column>
          <el-table-column prop="hfsj" label="回复时间" align="center">
          </el-table-column>
        </el-table>
        <div style="margin-top: 15px">
          <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
            :pager-count="5" :current-pageNo="page" :pageNo-sizes="[5, 10, 20, 30]" :pageNo-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <div style="display: flex;width: 500px;justify-content: center;
margin: 0 auto;margin-top: 60px;">
      <div class="buttonw btnc" @click="syb" v-if="active != 1 && active != 5">
        上一步
      </div>
      <div class="buttonw btnc1" @click="xyb" v-if="active != 4 || abnormalData.sblist.length">
        下一步
      </div>
      <div class="buttonw btnc1" @click="save" v-if="active == 4 && !abnormalData.sblist.length">
        提交并导出
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

import { savaJcpfjlBatch } from "../../../api/zczp";
import {
  getInit,
  saveInspectionComputerRoom,
  queryCabinetByCondition,
  queryEquipmentByCondition,
  saveInspectionCabinet,
  saveInspectionEquipment,
  exportInspectionForm
} from "../../../api/jfxj";

export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [
        {
          sbmc: "设备名称1",
          xh: "型号1",
          jgwz: "机柜位置1",
          xlh: "序列号1",
          gzqkms: "故障情况描述1",
          clff: "处理方法1",
          hfsj: "回复时间1"
        },
        {
          sbmc: "设备名称2",
          xh: "型号2",
          jgwz: "机柜位置2",
          xlh: "序列号2",
          gzqkms: "故障情况描述2",
          clff: "处理方法2",
          hfsj: "回复时间2"
        },
        {
          sbmc: "设备名称3",
          xh: "型号3",
          jgwz: "机柜位置3",
          xlh: "序列号3",
          gzqkms: "故障情况描述3",
          clff: "处理方法3",
          hfsj: "回复时间3"
        }
      ],
      active: 1,
      form: {
        jfmc: "",
        jfbh: "",
        jgmc: "",
        jgbh: "",
        sbmc: "",
        sbbh: "",
        close: "1",
        dust: "1",
        temperature: "1",
        humidity: "1",
        cleanliness: "1",
        airConditioning: "1",
        powerEnvironmentSystem: "1",
        id: "",
        computerRoomId: "",
        computerRoomCode: "",
        computerRoomName: "",
        inspectionStartTime: "",
        inspectionEndTime: "",

      },
      msg: "",
      sflist: [
        {
          label: "1",
          value: "正常",
          className: "normal"
        },
        {
          label: "2",
          value: "不正常",
          className: "abnormal"
        }
      ],
      jglist: [],
      sblist: [],
      lxlist: []
    };
  },
  //监听属性 类似于data概念
  computed: {
    abnormalData() {
      const abnormalItems = {
        form: {},
        jglist: [],
        sblist: [],
        lxlist: []
      };

      // 检查 form 状态
      if (this.form.temperature === "2") {
        abnormalItems.form.temperature = "机房温度不正常";
      }
      if (this.form.dust === "2") {
        abnormalItems.form.dust = "机房湿度不正常";
      }
      if (this.form.cleanliness === "2") {
        abnormalItems.form.cleanliness = "机房清洁度不正常";
      }
      if (this.form.airConditioning === "2") {
        abnormalItems.form.airConditioning = "机房空调不正常";
      }
      if (this.form.powerEnvironmentSystem === "2") {
        abnormalItems.form.powerEnvironmentSystem = "动力环境系统不正常";
      }
      if (this.form.close === "2") {
        abnormalItems.form.close = "机柜门关闭";
      }

      // 检查 jglist
      this.jglist.forEach(item => {
        if (
          item.temperature === "2" ||
          item.humidity === "2" ||
          item.cleanliness === "2"||
          item.close === "2"||
          item.dust === "2"

        ) {
          abnormalItems.jglist.push(item);
        }
        // if () {
        //   abnormalItems.jglist.push(item);
        // }
        // if () {
        //   abnormalItems.jglist.push(item);
        // }
      });

      // 检查 sblist
      this.sblist.forEach(item => {
        if (
          item.equipmentOnline === "2" ||
          item.equipmentPower === "2" ||
          item.equipmentNetwork === "2" ||
          item.equipmentRunningSound === "2" ||
          item.equipmentWarm === "2" ||
          item.equipmentLoosen === "2" ||
          item.equipmentDowntime === "2" ||
          item.equipmentHardDisk === "2" ||
          item.equipmentOhticalModule === "2" ||
          item.equipmentPowerModule === "2" ||
          item.equipmentFan === "2" ||
          item.equipmentTemperature === "2" ||
          item.equipmentPort === "2" ||
          item.equipmentOpticalFiber === "2" ||
          item.equipmentNetworkCable === "2" ||
          item.equipmentLabel === "2"||
          item.close === "2"||
          item.dust === "2"
        ) {
          abnormalItems.sblist.push(item);
        }
        // 其他检查...
      });

      // 检查 lxlist
      this.lxlist.forEach(item => {
        if (
          item.cableDamaged === "2" ||
          item.cableRibbon === "2" ||
          item.cableJoint === "2" ||
          item.cableLabel === "2"
        ) {
          abnormalItems.lxlist.push(item);
        }
      });

      return abnormalItems;
    }
  },
  // computed: {
  //   hasAbnormal() {
  //     // 检查 form 中的状态
  //     const formAbnormal =
  //       this.form.temperature === "0" ||
  //       this.form.humidity === "0" ||
  //       this.form.cleanliness === "0" ||
  //       this.form.airConditioning === "0" ||
  //       this.form.powerEnvironmentSystem === "0";

  //     // 检查 jglist
  //     const checkJGList = this.jglist.some(item => item.temperature === "0" || item.humidity === "0" || item.cleanliness === "0");

  //     // 检查 sblist
  //     const checkSBList = this.sblist.some(item =>
  //       item.equipmentOnline === "0" ||
  //       item.sbsfyjg === "0" ||
  //       item.equipmentLoosen === "0" ||
  //       item.equipmentDowntime === "0" ||
  //       item.equipmentHardDisk === "0" ||
  //       item.equipmentOhticalModule === "0" ||
  //       item.equipmentPowerModule === "0" ||
  //       item.equipmentFan === "0" ||
  //       item.equipmentTemperature === "0" ||
  //       item.equipmentPort === "0" ||
  //       item.equipmentOpticalFiber === "0" ||
  //       item.equipmentNetworkCable === "0" ||
  //       item.equipmentLabel === "0"
  //     );

  //     // 检查 lxlist
  //     const checkLXList = this.lxlist.some(item =>
  //       item.lxywpsdl === "0" ||
  //       item.lxjdsfzc === "0" ||
  //       item.lxjtsfysd === "0" ||
  //       item.lxbqsfzc === "0"
  //     );

  //     // 返回结果
  //     return formAbnormal || checkJGList || checkSBList || checkLXList;
  //   }
  // },
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    handleCurrentChange(val) {
      this.page = val;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    tableCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;";
    },
    tableHeaderCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;";
    },
    async init() {
      // alert(666)
      // this.queryCabinetByCondition();
      let data = await getInit();
      if (data.code == 10000) {
        if (data.data.temperature) {
          this.form = data.data;
        } else {
          this.form.temperature = "1";
          this.form.humidity = "1";
          this.form.cleanliness = "1";
          this.form.airConditioning = "1";
          this.form.powerEnvironmentSystem = "1";
          this.form.close = "1";
          this.form.dust = "1";
        }
        // this.$message.success("提交成功");
      } else {
        // this.$message.error("提交失败" + data.message);
      }
    },
    async xyb() {
      // console.log(this.abnormalData);

      if (this.active == 1) {
        this.form.scanCode = this.$route.query.scanCode;
        let data = await saveInspectionComputerRoom(this.form);
        if (data.code == 10000) {
          this.form.id = data.data;
          this.queryCabinetByCondition();
          this.queryEquipmentByCondition();
          this.queryEquipmentByConditionlx();
          // this.$message.success("提交成功" + this.form.id);
        } else {
          // this.$message.error("提交失败" + data.message);
        }
      }
      if (this.active == 2) {
        this.abnormalData.jglist.map(item => {
          item.computerRoomInspectionId = this.form.id;
        });
        saveInspectionCabinet({
          scanCode: this.$route.query.scanCode,
          inspectionCabinetList: this.abnormalData.jglist,
          computerRoomInspectionId: this.form.id
        }).then(res => {});
      }
      if (this.active == 3) {
        this.abnormalData.sblist.map(item => {
          item.computerRoomInspectionId = this.form.id;
        });
        saveInspectionEquipment({
          scanCode: this.$route.query.scanCode,
          inspectionEquipmentList: this.abnormalData.sblist,
          computerRoomInspectionId: this.form.id,
          equipmentMainType: "1"
        }).then(res => {});
      }
      if (this.active == 4) {
        this.abnormalData.lxlist.map(item => {
          item.computerRoomInspectionId = this.form.id;
        });
        saveInspectionEquipment({
          scanCode: this.$route.query.scanCode,
          inspectionEquipmentList: this.abnormalData.lxlist,
          computerRoomInspectionId: this.form.id,
          equipmentMainType: "2"
        }).then(res => {});
        this.$router.push({
          path: "/xjlclb",
          query: {
            id: this.form.id,
            scanCode: this.$route.query.scanCode
          }
        });
      } else {
        this.active++;
      }
    },
    async save() {
      saveInspectionEquipment({
        inspectionEquipmentList: this.abnormalData.lxlist,
        computerRoomInspectionId: this.form.id,
        scanCode: this.$route.query.scanCode,
        equipmentMainType: "2"
      }).then(res => {
        if (res.code == 10000) {
          this.$message({
            message: "提交成功",
            type: "success"
          });
        }
      });
      exportInspectionForm({
        computerRoomInspectionId: this.form.id
      }).then(res => {
        if (res.code == 10000) {
          this.$message({
            message: '导出成功',
            type: 'success'
          });
        }
        else{
          this.$message({
            message: res.message,
            type: 'error'
          });
        }
      });
      // this.abnormalData.lxlist.map(item => {
      //     item.computerRoomInspectionId = this.form.id;
      //   });
      //   saveInspectionEquipment({
      //     inspectionCabinetList: this.abnormalData.lxlist,
      //     computerRoomInspectionId: this.form.id
      //   }).then(res => {});
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      // console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
    async queryCabinetByCondition() {
      queryCabinetByCondition({
        cabinetCode: this.form.jgbh,
        cabinetName: this.form.jgmc,
        computerRoomInspectionId: this.form.id
      }).then(res => {
        // console.log(res);
        if (res.code == 10000) {
          this.jglist = res.data;
          this.msg = '警告：'+res.message;
        }
      });
    },
    //设备查询
    async queryEquipmentByCondition() {
      queryEquipmentByCondition({
        equipmentMainType: "1",
        cabinetCode: this.form.jgbh,
        cabinetName: this.form.jgmc,
        equipmentCode: this.form.sbbh,
        equipmentName: this.form.sbmc,
        computerRoomInspectionId: this.form.id
      }).then(res => {
        if (res.code == 10000) {
          this.sblist = res.data;
        }
      });
    },
    //线缆查询
    async queryEquipmentByConditionlx() {
      queryEquipmentByCondition({
        equipmentMainType: "2",
        // cabinetCode: this.form.jgbh,
        // cabinetName: this.form.jgmc,
        // equipmentCode: this.form.sbbh,
        // equipmentName: this.form.sbmc,
        computerRoomInspectionId: this.form.id
      }).then(res => {
        // console.log(res);
        if (res.code == 10000) {
          this.lxlist = res.data;
        }
      });
    },
    syb() {
      this.active--;
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.init();
  },
  //生命周期 - 创建之前
  beforeCreate() {},
  //生命周期 - 挂载之前
  beforeMount() {},
  //生命周期 - 更新之前
  beforeUpdate() {},
  //生命周期 - 更新之后
  updated() {},
  //生命周期 - 销毁之前
  beforeDestroy() {},
  //生命周期 - 销毁完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {}
};
</script>
<style scoped>
.box {
  width: 1580px;
  margin: 0 auto;
}
.top-box {
  width: 100%;
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 20px;
}
.top-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
  margin-left: 10px;
}
.bt-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}
.label-title {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
}
.buttonw {
  /* width: 72px;
  height: 32px; */
  padding: 0px 20px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.btnc {
  background-color: #3ecbfe;
  margin-right: 20px;
}
.btnc1 {
  background-color: #3e9efe;
}
.btnc2 {
  background-color: #20bdd1;
  margin-left: 20px;
}
.tszt {
  font-family: KaiTi;
  font-weight: 700;
}
.cxbtn {
  width: 72px;
  height: 32px;
  background: #3e9efe;
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0.07px;
  font-weight: 400;
  text-align: center;
  line-height: 32px;
  margin-left: 20px;
  margin-top: 14px;
}
.smzt {
  font-size: 20px;
}
/deep/ .el-step__icon {
  width: 36px;
  height: 36px;
}
/deep/ .el-step.is-horizontal .el-step__line {
  top: 18px;
  left: 45px;
  right: 12px;
}
/deep/ .el-step__head.is-process {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__head.is-wait {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__title.is-wait {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}
/deep/ .el-step__title.is-process {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}
/deep/ .el-step__icon.is-text {
  border: 2px solid;
  border-color: #0077ff;
}
/deep/ .el-step__head.is-success .is-text {
  background-color: #0077ff;
}
/deep/ .el-step__head.is-success {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__title.is-success {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}
.inputcss /deep/ .el-input__inner {
  width: 210px !important;
  height: 32px;
  border-radius: 4px;
}
.inputcss /deep/ .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
}
/deep/ .el-radio__inner {
  width: 20px;
  height: 20px;
  border-radius: 2px !important;
}

/deep/ .el-radio__input.is-checked .el-radio__inner::after {
  content: "";
  width: 8px;
  height: 3px;
  border: 1px solid #0077ff;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 5px;
  left: 4px;
  transform: rotate(-45deg);
  border-radius: 0px;
  background: none;
}
/deep/ .el-radio__input.is-checked .el-radio__inner {
  background: #fff;
}
/deep/ .el-form-item {
  margin: 10px 0;
}
.values {
  font-family: SourceHanSansSC-Normal;
  font-size: 14px;
  color: #333333;
  font-weight: 400;
  margin-left: 24px;
}
.labels {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
}
.fbtbox {
  display: flex;
  align-items: center;
  width: 350px;
}
/deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover:nth-child(even)
  > td {
  background-color: #dce8fb !important;
}
/deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover:nth-child(odd)
  > td {
  background-color: #dce8fb !important;
}
/deep/ .el-table__body tr:nth-child(even) {
  background-color: #dce8fb; /* 偶数行（斑马线）的默认背景色 */
}
/deep/ .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}
/deep/ .el-radio__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}
.sb-box3 /deep/ .el-form-item {
  border: 1px solid #c0c4cc;
  padding: 10px;
}
/* 正常选项的样式 */
/* 默认状态下的文本颜色（未选中时） */
/deep/ .el-radio__label {
  color: #000; /* 默认黑色文本 */
}

/* 正常选项的样式（选中时） */
/deep/ .el-radio.normal .el-radio__input.is-checked .el-radio__inner {
  border-color: #409eff; /* 蓝色边框 */
  background-color: #409eff; /* 蓝色背景 */
}

/deep/ .el-radio.normal .el-radio__input.is-checked .el-radio__inner::after {
  border-color: #fefefe;
}



/* 不正常选项的样式（选中时） */
/deep/ .el-radio.abnormal .el-radio__input.is-checked .el-radio__inner {
  border-color: #ff4d4f; /* 红色边框 */
  background-color: #ff4d4f; /* 红色背景 */
}

/deep/ .el-radio.abnormal .el-radio__input.is-checked .el-radio__inner::after {
  border-color: #fefefe;
}

/deep/ .el-radio.is-checked.abnormal .el-radio__label{
  color: #ff4d4f;
}


</style>
