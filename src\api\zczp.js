import {createAPI,createDown, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

//查询全部单位自查大项
export const getAllDwzcdx = data => createAPI(BASE_URL+"/zczpDwjcdx/getAllDwzcdx", 'get',data)
//根据大项id查询单位自查小项
export const getxxListByDxid = data => createAPI(BASE_URL+"/zczpDwjcxx/getxxListByDxid", 'get',data)
//根据大项id查询单位自查小项
export const getDwjcnrByXxid = data => createAPI(BASE_URL+"/zczpDwjcnr/getDwjcnrByXxid", 'get',data)

//添加自查自评任务
export const addJcrw = data => createAPI(BASE_URL+"/zczpJcrw/addJcrw", 'post',data)
//添加自查自评任务
export const updateJcrw = data => createAPI(BASE_URL+"/zczpJcrw/updateJcrw", 'post',data)

//查询继续自查自评记录带分页
export const selectJxZczpPage = data => createAPI(BASE_URL+"/zczpJcrw/selectJxZczpPage", 'get',data)

//根据任务id查询检查任务
export const getJcrwByRwid = data => createAPI(BASE_URL+"/zczpJcrw/getJcrwByRwid", 'get',data)

//查询继续自查自评历史记录带分页
export const selectZczpLsPage = data => createAPI(BASE_URL+"/zczpJcrw/selectZczpLsPage", 'get',data)
//根据任务id查询自查评分记录
export const getDwzcpfjlByRwid = data => createAPI(BASE_URL+"/zczpDwjcpfjl/getDwzcpfjlByRwid", 'get',data)
//批量添加单位自查评分记录
export const savaJcpfjlBatch = data => createAPI(BASE_URL+"/zczpDwjcpfjl/savaJcpfjlBatch", 'post',data)
//根据任务id删除自查评分记录
export const deletePfjlByRwid = data => createAPI(BASE_URL+"/zczpDwjcpfjl/deletePfjlByRwid", 'post',data)
//查询全部保密组织机构
export const getAllBmZzjgByDwid = data => createAPI(BASE_URL+"/jggl/glxx/getAllBmZzjgByDwid", 'get',data)
//添加自查自评组织结构信息
export const addZczpZzjgxx = data => createAPI(BASE_URL+"/zczp/zzjgxx/addZczpZzjgxx", 'post',data)

//根据自查项id查询人员自查项
export const getRyjczcx = data => createAPI(BASE_URL+"/zczp/ryjczcx/getRyjczcx", 'get',data)
//根据自查项id查询人员自查内容
export const getRyjcnr = data => createAPI(BASE_URL+"/zczpRyjcnr/getRyjcnr", 'get',data)
//批量添加人员自查评分记录
export const addRyjcpfjl = data => createAPI(BASE_URL+"/zczp/ryjcpfjl/addRyjcpfjl", 'post',data)
//根据任务id删除人员自查评分记录
export const deleteRyjcpfjl = data => createAPI(BASE_URL+"/zczp/ryjcpfjl/deleteRyjcpfjl", 'post',data)
//根据任务id查询人员自查评分记录
export const ryjcpfjlgetRyzcpfjlByRwid = data => createAPI(BASE_URL+"/zczp/ryjcpfjlgetRyzcpfjlByRwid", 'get',data)
//根据任务id查询人员自查评分记录
export const selectZczpZzjgxx = data => createAPI(BASE_URL+"/zczp/zzjgxx/selectZczpZzjgxx", 'post',data)
//根据任务id查询人员自查评分记录
export const selectZczpZzjgxxPage = data => createAPI(BASE_URL+"/zczp/zzjgxx/selectZczpZzjgxxPage", 'get',data)
//根据任务id查询人员自查评分记录
export const deleteZczpZzjgxx = data => createAPI(BASE_URL+"/zczp/zzjgxx/deleteZczpZzjgxx", 'post',data)
//查询内设机构评分记录
export const selectZczpBmjcpfjl = data => createAPI(BASE_URL+"/zczp/bmjcpfjl/selectZczpBmjcpfjl", 'post',data)
//查询内设机构自查项
export const selectAllZczpBmjczcx = data => createAPI(BASE_URL+"/zczp/bmjczcx/selectAllZczpBmjczcx", 'get',data)
//查询内设机构自查内容
export const selectAllZczpBmjcnr = data => createAPI(BASE_URL+"/zczp/bmjcnr/selectAllZczpBmjcnr", 'get',data)
//批量添加部门评分记录
export const addZczpBmjcpfjlList = data => createAPI(BASE_URL+"/zczp/bmjcpfjl/addZczpBmjcpfjlList", 'post',data)
//修改部门评分记录登记状态
export const updateZczpZzjgxxDjzt = data => createAPI(BASE_URL+"/zczp/zzjgxx/updateZczpZzjgxxDjzt", 'post',data)
//删除部门评分记录
export const deleteZczpBmjcpfjlByRwidAndJgid = data => createAPI(BASE_URL+"/zczp/bmjcpfjl/deleteZczpBmjcpfjlByRwidAndJgid", 'post',data)
// export const ryjcpfjlgetRyzcpfjlByRwid = data => createAPI(BASE_URL+"/zczp/ryjcpfjlgetRyzcpfjlByRwid", 'get',data)

//查询当前单位全部涉密人员
export const getSmryList = data => createAPI(BASE_URL+"/rygl/yhxx/getSmryList", 'get',data)
//添加自查自评人员信息
export const addZczpRyxx = data => createAPI(BASE_URL+"/zczp/ryxx/addZczpRyxx", 'post',data)
//删除自查自评人员信息
export const deleteZczpRyxx = data => createAPI(BASE_URL+"/zczp/ryxx/deleteZczpRyxx", 'post',data)
//查询自查自评人员信息分页
export const selectZczpRyxxPage = data => createAPI(BASE_URL+"/zczp/ryxx/selectZczpRyxxPage", 'get',data)

//查询自查自评人员信息分页
export const updateDjztByRyid = data => createAPI(BASE_URL+"/zczp/ryxx/updateDjztByRyid", 'post',data)
//查询自查自评人员信息分页
export const getLsRyzcpfjl = data => createAPI(BASE_URL+"/zczp/ryjcpfjl/getLsRyzcpfjl", 'get',data)
//查询全部人员自查项
export const getAllRyjczcx = data => createAPI(BASE_URL+"/zczp/ryjczcx/getAllRyjczcx", 'get',data)
//查询全部人员自查项内容
export const getAllRyjcnr = data => createAPI(BASE_URL+"/zczpRyjcnr/getAllRyjcnr", 'get',data)

//获取全部小项
export const getAllDwzcxx = data => createAPI(BASE_URL+"/zczpDwjcxx/getAllDwzcxx", 'get',data)
//获取全部内容
export const getAllDwzcnr = data => createAPI(BASE_URL+"/zczpDwjcnr/getAllDwzcnr", 'get',data)
//获取全部内容
export const getDwzcnrByDxid = data => createAPI(BASE_URL+"/zczpDwjcnr/getDwzcnrByDxid", 'get',data)

//获取全部内容
export const selectAllZczpRyxx = data => createAPI(BASE_URL+"/zczp/ryxx/selectAllZczpRyxx", 'get',data)

//下载
export const exportZczpjgZip = data => createDown(BASE_URL+"/zczpJcrw/exportZczpjgZip", 'get',data)