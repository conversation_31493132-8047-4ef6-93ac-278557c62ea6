import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

//日常工作
//添加无授权人员计入涉密场所审查
export const addWsqjr = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/addWsqjr", 'post',data)
//修改无授权人员进入涉密场所审查
export const updateWsqjr = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/updateWsqjr", 'post',data)
//删除无授权人员进入涉密场所审查
export const deleteWsqjr = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/deleteWsqjr", 'post',data)
//查看无授权人员进入涉密场所审查-分页
export const selectWsqjr = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/selectWsqjr", 'get',data)
//根据jlid查看无授权人员进入涉密场所审查
export const selectWsqjrByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/selectWsqjrByJlid", 'get',data)
//根据slid查询无授权人员进入审批
export const selectWsqjrBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/selectWsqjrBySlid", 'get',data)
//根据slid查询无授权人员进入审批的jlid
export const selectWsqjrJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/csgl-wsqjr/selectWsqjrJlidBySlid", 'get',data)
//清单
//内部
//添加无授权进入人员内部清单
export const addWsqjrrynbqd = data => createAPI(BASE_URL+"/csgl_wsqjrrynbqd/addWsqjrrynbqd", 'post',data)
//修改无授权进入人员内部清单
export const updateWsqjrrynbqd = data => createAPI(BASE_URL+"/csgl_wsqjrrynbqd/updateWsqjrrynbqd", 'post',data)
//删除无授权进入人员内部清单
export const deleteWsqjrrynbqd = data => createAPI(BASE_URL+"/csgl_wsqjrrynbqd/deleteWsqjrrynbqd", 'post',data)
//根据sqid查看无授权进入人员内部清单
export const selectWsqjrrynbqdBySqid = data => createAPI(BASE_URL+"/csgl_wsqjrrynbqd/selectWsqjrrynbqdBySqid", 'get',data)
//外部
//添加无授权进入人员外部清单
export const addWsqjrrywbqd = data => createAPI(BASE_URL+"/csgl_wsqjrrywbqd/addWsqjrrywbqd", 'post',data)
//修改无授权进入人员外部清单
export const updateWsqjrrywbqd = data => createAPI(BASE_URL+"/csgl_wsqjrrywbqd/updateWsqjrrywbqd", 'post',data)
//删除无授权进入人员外部清单
export const deleteWsqjrrywbqd = data => createAPI(BASE_URL+"/csgl_wsqjrrywbqd/deleteWsqjrrywbqd", 'post',data)
//根据sqid查看无授权进入人员外部清单
export const selectWsqjrrywbqdBySqid = data => createAPI(BASE_URL+"/csgl_wsqjrrywbqd/selectWsqjrrywbqdBySqid", 'get',data)
//1.添加未授权进入人员清单
export const addWsqjrqd = data => createAPI(BASE_URL+"/csgl_wsqjrqd/addWsqjrqd", 'post',data)
//2.删除未授权进入人员清单
export const deleteWsqjrqd = data => createAPI(BASE_URL+"/csgl_wsqjrqd/deleteWsqjrqd", 'post',data)
//3.查询未授权进入人员清单
export const selectWsqjrqd = data => createAPI(BASE_URL+"/csgl_wsqjrqd/selectWsqjrqd", 'get',data)
//3.查询未授权进入人员清单
export const deleteAllWsqjrqd = data => createAPI(BASE_URL+"/csgl_wsqjrqd/deleteAllWsqjrqd", 'post',data)
//导入添加
export const addUploadWsqjr = data => createAPI(BASE_URL+"/csgl_wsqjrqd/addUploadWsqjr", 'post',data)