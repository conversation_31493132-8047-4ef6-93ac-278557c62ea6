import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

// 通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
// 根据原jlid查询设备清单
export const getSbqdListByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/getSbqdListByYjlid", 'get',data)
// 设备清单批量添加
export const savaSbqdBatch = data => createAPI(BASE_URL+"/sbgl/sbqd/savaSbqdBatch", 'post',data)
// 根据原jlid删除原jlid下的设备清单
export const deleteSbqdByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/deleteSbqdByYjlid", 'post',data)
//日常工作
//1.添加设备管理信息导入
export const addSbglXxdr = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/addSbglXxdr", 'post',data)
//2.修改设备管理信息导入
export const updateSbglXxdr = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/updateSbglXxdr", 'post',data)
// 查询设备管理信息导入-分页
export const selectSbglXxdrPage = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/selectSbglXxdrPage", 'get',data)
// 删除设备信息导入
export const deleteSbglXxdr = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/deleteSbglXxdr", 'post',data)
// 5.根据jlid查询设备信息导入
export const getXxdrByjlid = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getXxdrByjlid", 'get',data)
// 根据slid查询设备信息导入
export const getXxdrByslid = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getXxdrByslid", 'get',data)
//7.通过slid获取设备信息导入的jlid
export const getXxdrJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getXxdrJlidBySlid", 'get',data)

//登记表
//添加信息导入登记表
export const addSbXxdrDj = data => createAPI(BASE_URL+"/sbgl_xxdrdj/addSbXxdrDj", 'post',data)
//设备信息导入登记表-分页
export const getSbXxdrdjPage = data => createAPI(BASE_URL+"/sbgl_xxdrdj/getSbXxdrdjPage", 'get',data)
//根据jlid查询信息导入
export const getSbXxdrdjByJlid = data => createAPI(BASE_URL+"/sbgl_xxdrdj/getSbXxdrdjByJlid", 'get',data)


//查询全部保密编号(U盘 红盘 )
export const getBmbhByJzmc = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getBmbhByJzmc", 'get',data)
//根据介质类型查询保密编号（光盘、纸介质）
export const getBmbhByJzlx = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getBmbhByJzlx", 'get',data)
//查询导盒保密编号
export const getDdhBmbh = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getDdhBmbh", 'get',data)
//查询涉密中间机编号
export const getSmjsjBmbh = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getSmjsjBmbh", 'get',data)