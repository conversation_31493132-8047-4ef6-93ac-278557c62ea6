<template>
  <div id="app" style="border-radius: 30px">
    <el-container style="height: 100%">
      <el-header v-show="showHeaderMenu" id="app-header" style="height: 80px">
        <div style="position: relative">
          <div class="left">
            <img
              src="./assets/logo/LOGO01.png"
              class="logo01"
              @click="toZyqk()"
            />
            <div
              v-show="showAsideMenu"
              style="
                font-size: 24px;
                flex: 1;
                height: 80px;
                text-align: right;
                line-height: 120px;
                background-color: ;
                cursor: pointer;
                color: white;
                box-sizing: border-box;
              "
            >
              <i
                v-if="buttonShowAsideMenu"
                class="el-icon-s-fold"
                @click="buttonShowAsideMenu = false"
              ></i>
              <i
                v-else
                class="el-icon-s-unfold"
                @click="buttonShowAsideMenu = true"
              ></i>
            </div>
          </div>
          <div
            style="
              width: calc(100% - 270px - 0px);
              height: 120px;
              background: ;
              float: left;
              white-space: nowrap;
              overflow: hidden;
            "
          >
            <!-- <hsoft_button_img2
              :width="'120px'"
              :imgType="'ztqk'"
              :title="'总体情况'"
              :toPath="'/ztqksy'"
              :componentId="1"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'dbgz'"
              :title="'待办工作'"
              v-if="this.dwjy"
              :toPath="'/dbgzsy'"
              :componentId="2"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'yjgz'"
              :title="'迎检工作'"
              :toPath="'/yjgzsy'"
              :componentId="3"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'zczp'"
              :title="'自查自评'"
              :toPath="'/zczpls'"
              :componentId="4"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'tzgl'"
              :title="'台账管理'"
              :toPath="'/tzglsy'"
              :componentId="5"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'rcgz'"
              :title="'日常工作'"
              v-if="this.dwjy"
              :toPath="'/rcgztabs'"
              :componentId="6"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'bggl'"
              :title="'报告管理'"
              v-if="this.dwjy"
              :toPath="'/bgglsy'"
              :componentId="7"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'sjrz'"
              :title="'审计日志'"
              v-if="this.dwjy"
              :toPath="'/sjrzsy'"
              :componentId="8"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'wdgz'"
              :title="'我的工作'"
              :toPath="'/wdgz'"
              :componentId="9"
              v-if="this.dwjy"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2>
            <hsoft_button_img2
              :width="'120px'"
              :imgType="'bmai'"
              :title="'保密AI'"
              v-if="this.dwjy"
              :toPath="'/bmai'"
              :componentId="10"
              :componentSelectedId="componentSelectedId"
              @childSelected="childSelected"
              :titleFontSize="'16px'"
            >
            </hsoft_button_img2> -->
          </div>
          <div class="div-top-right">
            <hsoft_enum_window @quitClicked="quitClicked"></hsoft_enum_window>
          </div>
          <div style="width: 100%; clear: both"></div>
        </div>
      </el-header>
      <el-container style="height: calc(100% - 120px)">
        <el-aside v-show="showAsideMenu && buttonShowAsideMenu" width="280px">
          <hsoft_enum
            :showRoutePathList="showRoutePathList"
            @getNewMenuList="getNewMenuList"
          ></hsoft_enum>
        </el-aside>
        <el-main>
          <div class="main-out-div">
            <div
              v-show="showAsideMenu"
              style="
                border-bottom: 1px dotted #ebeef5;
                margin-bottom: 5px;
                padding-bottom: 5px;
              "
            >
              <hsoft_tags_view
                :resetFlagTags="resetFlagTags"
                :newMenuList="newMenuList"
              ></hsoft_tags_view>
            </div>
            <router-view></router-view>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
!(function (t) {
  function e() {
    var e = this || self;
    (e.globalThis = e), delete t.prototype._T_;
  }
  "object" != typeof globalThis &&
    (this
      ? e()
      : (t.defineProperty(t.prototype, "_T_", {
          configurable: !0,
          get: e,
        }),
        _T_));
})(Object);
import hsoft_enum from "./components/hsoft-enum/hsoft-enum.vue";
import hsoft_enum_window from "./components/hsoft-enum/hsoft-enum-window.vue";
import hsoft_button_img from "./components/hsoft-button/hsoft-button-img.vue";
import hsoft_button_img2 from "./components/hsoft-button/hsoft-button-img2.vue";
import hsoft_tags_view from "./components/hsoft-tags-view/hsoft-tags-view.vue";

// import { initZczpIdsObj } from '../utils/windowLocation'

// import { dataMigration } from '../utils/dataMigrationUtil'

import { getWarningDatas } from "../api/jfxj";

export default {
  name: "demo2",
  data() {
    return {
      // 需要显示的菜单，由父组件传递过来
      // showRoutePathList: ['/gwbg', '/lglz'],
      showRoutePathList: [],
      // 组件ID，这里为子组件回抛来的ID，用来通知子组件更新自己的选中状态的
      componentSelectedId: 0,
      // 顶部显示
      showHeaderMenu: false,
      // 左侧导航隐藏
      showAsideMenu: false,
      // 按钮控制左侧导航栏显隐（优先级高于路由菜单显隐配置）
      buttonShowAsideMenu: true,
      // 鼠标状态
      isDown: false,
      //监听坐标
      baseX: 0,
      baseY: 0,
      // 顶部可拖拽区域背景色(可选色)
      appTopBackgroundArr: ["rgba(0, 73, 207, 0.9)", "rgba(0, 73, 207, 1)"],
      // 顶部可拖拽区域背景色(可选色)
      appTopBackground: "",
      // 左侧菜单组件回抛的当前菜单list，用于发给tags组件进行非左侧菜单关闭操作
      newMenuList: [],
      /** 组件重置标记 **/
      // tags 重置标记
      resetFlagTags: 0,
      dwjy: true,
      // 定时器相关
      warningTimer: null,
      warningVisible: false,
    };
  },
  components: {
    hsoft_enum,
    hsoft_enum_window,
    hsoft_button_img,
    hsoft_button_img2,
    hsoft_tags_view,
  },
  methods: {
    // 子组件回抛的退出事件
    quitClicked() {
      // 退出系统
    },
    // 子组件回抛的事件，用以进行组件选中状态的变更
    childSelected(componentId) {
      // console.log("菜单逻辑 childClicked", componentId)
      this.componentSelectedId = componentId;
    },
    // 点击左上角logo区域返回总体情况首页
    toZyqk() {
      this.$router.push("/ztqksy");
    },
    //
    xtsz() {},
    toIndex() {},
    back() {
      this.$router.go(-1);
    },
    // 左侧菜单组件回抛的当前操作菜单list，用以发给tags组件进行非左侧菜单tag关闭操作
    getNewMenuList(list) {
      // console.log('菜单逻辑 左侧菜单回抛的菜单集合', list)
      this.newMenuList = list;
    },
    // 开启控制台
    toggleDevTools() {
      const { remote } = require("electron");
      const webContents = remote.getCurrentWebContents();
      webContents.on("before-input-event", (event, input) => {
        // console.log('before-input-event', event, input)
        if (input.code == "F12") {
          webContents.openDevTools();
        }
      });
    },
    // 确认是否需要数据迁移
    exeDataMigrationConfig() {},

    // 启动警告定时器
    startWarningTimer() {
      this.warningTimer = setInterval(() => {
        this.showWarningNotification();
      }, 7200000); // 每5秒执行一次
    },

    // 停止警告定时器
    stopWarningTimer() {
      if (this.warningTimer) {
        clearInterval(this.warningTimer);
        this.warningTimer = null;
      }
    },

    generateAlertMessages(data) {
      return data.map((item) => {
        return `机柜 ${item.cabinetName} 异常：首次开柜时间 ${item.alertStartTime}，最后开柜时间 ${item.alertFinishTime}`;
      });
    },

    // 显示警告通知
    async showWarningNotification() {
      // alert(1)
      // if (!this.warningVisible) {
      this.warningVisible = true;

      try {
        const response = await getWarningDatas();
        if (response.code == 10000) {
          const alertMessages = this.generateAlertMessages(response.data);
          // 在前端显示（示例）
          // 或者合并为一个字符串显示
          const combinedMessage = alertMessages.join("\n");
          // console.log(combinedMessage);
          this.$notify({
            title: "系统警告",
            message: combinedMessage,
            type: "warning",
            duration: 7200000, // 设置为0表示不自动关闭
            // position: 'top-right',
            showClose: true,
            onClose: () => {
              this.warningVisible = false;
            },
          });
        }
      } catch (error) {
        // this.pageloading = false;
        // console.error("开柜门失败：", error);
        // this.$message.error("开柜门失败，请稍后重试");
      }

      // }
    },
  },
  mounted() {
    // 启动警告定时器
    this.startWarningTimer();

    const PubSub = require("pubsub-js");
    PubSub.subscribe("dataNext", (msg, data) => {
      if (data == "next") {
        this.dwjy = false;
      }
    });
    PubSub.subscribe("dataFh", (msg, data) => {
      if (data == "fh") {
        this.dwjy = true;
      }
    });

    // this.dwjy = true
    let anpd = localStorage.getItem("dwjy");
    // console.log(anpd);
    // if (anpd == 1) {
    //   this.dwjy = false
    // }
    // else {
    //   this.dwjy = true
    // }
    this.componentSelectedId = 1;
    const _this = this;
    // // 顶部可拖拽区域默认色
    // _this.appTopBackground = _this.appTopBackgroundArr[0]
    /**
     * 窗口拖拽移动三大事件
     */
    let appTop = document.getElementById("app-header");
    appTop.addEventListener("mousedown", function (e) {
      _this.isDown = true;
      _this.baseX = e.x;
      _this.baseY = e.y;
      // console.log(_this.baseX, _this.baseY)
      _this.appTopBackground = _this.appTopBackgroundArr[1];
    });
    appTop.addEventListener("mousemove", function (e) {
      if (_this.isDown) {
        const x = e.screenX - _this.baseX;
        const y = e.screenY - _this.baseY;
        _this.$electron.ipcRenderer.send("move-application", {
          posX: x,
          posY: y,
        });
      }
    });
    appTop.addEventListener("mouseup", function () {
      _this.isDown = false;
      _this.appTopBackground = _this.appTopBackgroundArr[0];
    });
    // alert('process.env.NODE_ENV', process.env.NODE_ENV)
    // console.log('window', window)
    // window.addEventListener(
    //   'keydown',
    //   (e) => {
    //     const { altKey, ctrlKey, keyCode } = e
    //     //  alt + ctrl + (Command | Windows) + l
    //     console.log('keys', altKey, ctrlKey, keyCode)
    //     if (altKey && ctrlKey && keyCode === 76) {
    //       //获取当前窗体
    //       const currentWindow = require('electron').remote.getCurrentWindow()
    //       // 开启控制台
    //       currentWindow && currentWindow.toggleDevTools()
    //       e.preventDefault()
    //     }
    //   },
    //   false
    // )
    // 开启控制台
    this.toggleDevTools();
    // 确认是否需要数据迁移
    // this.exeDataMigrationConfig()
  },
  beforeDestroy() {
    // 清理定时器
    this.stopWarningTimer();
  },
  watch: {
    $route(to, from) {
      // console.log('app检测到变化:')
      // console.log(from)
      // console.log(to)
      // console.log(to.meta);
      this.showAsideMenu = to.meta.showAsideMenu;
      // 将左侧显隐同时抛到vuex中，方便aside检测菜单是否需要重置
      if (to.meta.menuList) {
        this.$store.default.commit("changeElAsideMenuList", to.meta.menuList);
      }
      this.showHeaderMenu = to.meta.showHeaderMenu;
      // console.log('app检测到变化end')
    },
    componentSelectedId(newVal, oldVal) {
      // console.log('componentSelectedId changed', newVal)
      /**
       * 组件ID改变了，通知需要重置数据的组件重置自己的数据
       * **/
      // 重置 hsoft_tags_view 组件
      this.resetFlagTags += 1;
    },
  },
};
</script>

<style>
::-webkit-scrollbar {
  display: none;
}

* {
  padding: 0;
  margin: 0;
}

input {
  background-color: transparent;
}

body {
  height: 100%;
  /* background: blue; */
}

html {
  height: 100%;
  /* background: red; */
}

@font-face {
  font-family: pingfangziti;
  src: url("../renderer/assets/font/苹方字体.ttf");
}

@font-face {
  font-family: SourceHanSansSCziti;
  src: url("../renderer/assets/font/SourceHanSansSC-Normal-2.otf");
}

#app {
  /* font-family: Hiragino Sans GB; */
  font-family: "pingfangziti";
  letter-spacing: 0;
  font-weight: 400;
  height: 100%;
  /* background: yellow; */
  border-radius: 20px;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  /* background: rgba(0, 73, 207, 0.301); */
}

::-webkit-scrollbar-track-piece {
  background-color: rgba(255, 255, 255, 0.2);
  -webkit-border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  height: 5px;
  background-color: rgba(64, 158, 225, 0.9);
  -webkit-border-radius: 6px;
  /* background-image: url(./assets/background/bg.png); */
}

/* .el-container {
  height: 100%;
} */

.el-header {
  height: 8%;
  /* background: red; */
  /* background-image: url(./assets/background/header_bg.png); */
  /* background-image: url(./assets/background/head.png); */
  background-color: #145ccc;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.el-container .el-header {
  padding: 0 0 0 0;
}

.el-header .left {
  width: 270px;
  /* width: 300px; */
  height: 80px;
  float: left;
  display: flex;
  align-items: center;
  /* background: red; */
  box-sizing: border-box;
}

.el-header .left .logo {
  /* width: 84px; */
  width: 72px;
  margin-left: 10px;
  /* float: left; */
  /* margin-top: 15px; */
}

.el-header .left .logo2 {
  width: 100px;
  float: left;
  /* margin-top: 15px; */
}

.el-header .left .logo01 {
  width: 184px;
  margin-left: 10px;
  cursor: pointer;
}

.el-header .left .title {
  /* float: left; */
  padding-left: 10px;
  flex: 1;
  /* font-size: 36px; */
  font-size: 28px;
  font-weight: bolder;
  font-family: Alimama ShuHeiTi;
  color: #ffffff;
  letter-spacing: 2.8px;
  line-height: 80px;
  font-weight: 700;
}

/***/
.el-aside {
  background: #3874d5;
  /* background-image: url(./assets/background/left_bg.png); */
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.el-main {
  height: 100%;
  /* background: #f5f5f5; */
  background: #ecf3fa;
  /* padding: 0px; */
  background-image: url(./assets/background/bgnew.jpg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.el-main .main-out-div {
  /* background: red; */
  /* height: calc(100% - 32px - 10px - 1px); */
  height: calc(100% - 10px - 1px);
  overflow: hidden;
  /* background: #ffffff; */
}

/**表格上方标题样式**/
.table-out-title {
  color: #e6a23c;
  font-weight: 700;
  position: relative;
}

.table-out-title div {
  padding-left: 10px;
  margin: 10px;
}

.table-out-title::before {
  content: "";
  background: #e6a23c;
  width: 4px;
  height: 75%;
  position: absolute;
  top: 5%;
}

/**notify保留换行符**/
.el-notification {
  white-space: pre-wrap;
}

/**右上角菜单区域样式**/
.div-top-right {
  position: absolute;
  right: 0;
  top: 0;
  width: 210px;
  height: 80px;
  /* float: left;
  text-align: right; */
  font-size: 14px;
  font-weight: bolder;
  display: flex;
}

.drfs {
  width: 250px;
}
</style>

<style scoped>
/***/
:deep(.el-menu) {
  border-right: solid 1px rgba(255, 255, 255, 0);
}

:deep(.el-submenu) {
  /* background: red; */
  padding: 5px;
}

:deep(.el-submenu__title) {
  /* background: blue; */
}

:deep(.el-table--enable-row-transition) .el-table__body td.el-table__cell {
  font-size: 12px;
}

:deep(.el-dialog__title) {
  font-size: 16px;
}

:deep(.el-dialog__title)::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 22px;
  width: 5px;
  height: 20px;
  border-radius: 2px;
  background: #409eef;
}

:deep(.el-dialog__body) {
  padding: 0 20px;
}

:deep(.el-dialog__footer) {
  padding: 0 20px 20px;
}

::v-deep .el-dialog__body .el-form > div .el-form-item__label {
  border: 0;
}
</style>
