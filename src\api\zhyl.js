import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//涉密计算机
export const getCurSmjsj = data => createAPI(BASE_URL+"/sbgl/smjsj/getCurSmjsj", 'get',data)
//非涉密计算机
export const getCurFmjsj = data => createAPI(BASE_URL+"/sbgl/fmjsj/getCurFmjsj", 'get',data)
//涉密移动存储介质
export const getCurYdccjz = data => createAPI(BASE_URL+"/sbgl/ydccjz/getCurYdccjz", 'get',data)
//涉密办公自动化
export const getCurSmxxsb = data => createAPI(BASE_URL+"/sbgl/smxxsb/getCurSmxxsb", 'get',data)
//非涉密办公自动化
export const getCurFmxxsb = data => createAPI(BASE_URL+"/sbgl/fmxxsb/getCurFmxxsb", 'get',data)
//涉密网络设备
export const getCurSmwlsb = data => createAPI(BASE_URL+"/sbgl/smwlsb/getCurSmwlsb", 'get',data)
//非涉密网络设备
export const getCurFmwlsb = data => createAPI(BASE_URL+"/sbgl/fmwlsb/getCurFmwlsb", 'get',data)
//涉密安全产品
export const getCurAqfhcp = data => createAPI(BASE_URL+"/sbgl/xxsb/getCurAqfhcp", 'get',data)
//涉密载体
export const getCurZt = data => createAPI(BASE_URL+"/ztgl/zt/getCurZt", 'get',data)
//涉密人员
export const getCurYhxx = data => createAPI(BASE_URL+"/rygl/yhxx/getCurYhxx", 'get',data)
