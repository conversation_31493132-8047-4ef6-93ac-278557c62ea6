import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//等级岗位变更创建实例表单提交
export const submitDjgwbg = data => createAPI(BASE_URL+"/rygl-djgwbg/submitDjgwbg", 'post',data)
//等级岗位变更查询带分页
export const selectDjgwbgPage = data => createAPI(BASE_URL+"/rygl-djgwbg/selectDjgwbgPage", 'get',data)
//根据rwid获取等级岗位变更信息详情
export const getDjgwbgInfo = data => createAPI(BASE_URL+"/rygl-djgwbg/getDjgwbgInfo", 'get',data)
//根据lcslid获取在岗复审信息详情
export const getDjgwbgInfoByLcsllid = data => createAPI(BASE_URL+"/rygl-djgwbg/getDjgwbgInfoByLcsllid", 'get',data)
//删除等级岗位变更记录
export const removeDjgwbg = data => createAPI(BASE_URL+"/rygl-djgwbg/removeDjgwbg", 'post',data)
//根据任务id修改在岗复审信息
export const updateDjgwbg = data => createAPI(BASE_URL+"/rygl-djgwbg/updateDjgwbg", 'post',data)
//判断是否为最后一个环节
export const verifySfjshj = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/verifySfjshj", 'post',data)
//查询全部涉密岗位变更记录带分页
export const getMjbgPage = data => createAPI(BASE_URL+"/rygl-djgwbg/getMjbgPage", 'get',data)
//查询全部离岗离职变更记录带分页
export const getLzlgPage = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLzlg/getLzlgPage", 'get',data)
//查询全部涉密岗位历史变更记录带分页
export const getMjbgLsPage = data => createAPI(BASE_URL+"/lstz/rygl/djgwbg/getMjbgPage", 'get',data)
//查询全部离岗离职历史变更记录带分页
export const getLzlgLsPage = data => createAPI(BASE_URL+"/lstz-rygl-lzlg/getLzlgPage", 'get',data)