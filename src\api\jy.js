import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//校验涉密计算机
export const smjsjverify = data => createAPI(BASE_URL+"/sbgl/smjsj/verify", 'get',data)
//校验非涉密计算机
export const fmjsjverify = data => createAPI(BASE_URL+"/sbgl/fmjsj/verify", 'get',data)
//校验涉密移动存储介质
export const ydccjzverify = data => createAPI(BASE_URL+"/sbgl/ydccjz/verify", 'get',data)
//校验涉密办公自动化
export const smxxsbverify = data => createAPI(BASE_URL+"/sbgl/smxxsb/verify", 'get',data)
//校验非涉密办公自动化
export const fmxxsbverify = data => createAPI(BASE_URL+"/sbgl/fmxxsb/verify", 'get',data)
//校验涉密网络设备
export const smwlsbverify = data => createAPI(BASE_URL+"/sbgl/smwlsb/verify", 'get',data)
//校验非涉密网络设备
export const fmwlsbverify = data => createAPI(BASE_URL+"/sbgl/fmwlsb/verify", 'get',data)
//校验安全产品
export const xxsbverify = data => createAPI(BASE_URL+"/sbgl/xxsb/verify", 'get',data)
//校验载体
export const ztverify = data => createAPI(BASE_URL+"/ztgl/zt/verify", 'get',data)
//校验人员
export const yhxxverify = data => createAPI(BASE_URL+"/rygl/yhxx/verify", 'get',data)
//校验岗位
export const gwdjverify = data => createAPI(BASE_URL+"/rygl/gwdj/verify", 'get',data)
//校验涉密台账KEY
export const smKeyverify = data => createAPI(BASE_URL+"/sbgl/key/verify", 'get',data)
