import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1.添加设备携带外出
export const addSbxdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/addSbxdwc", 'post',data)
//2.查询设备携带外出带分页
export const selectSbxdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/selectSbxdwc", 'get',data)
//3.修改设备携带外出
export const updateSbxdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/updateSbxdwc", 'post',data)
//4.删除设备携带外出
export const deleteSbxdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/deleteSbxdwc", 'post',data)
//5.根据jlid查询设备携带外出
export const getSbxdwcByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/getSbxdwcByJlid", 'get',data)
//6.根据slid查询设备携带外出
export const getSbxdwcBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/getSbxdwcBySlid", 'get',data)
//7.通过slid获取设备携带外出的jlid
export const getSbxdwcJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_sbxdwc/getSbxdwcJlidBySlid", 'get',data)
//1.添加设备携带外出登记表
export const addSbxdwcdj = data => createAPI(BASE_URL+"/sbgl_sbxdwcdj/addSbxdwcdj", 'post',data)
//3.查看设备携带外出登记分页
export const getSbxdwcdjPage = data => createAPI(BASE_URL+"/sbgl_sbxdwcdj/getSbxdwcdjPage", 'get',data)
//5.根据记录id查询设备携带外出登记
export const getSbxdwcdjByJlid = data => createAPI(BASE_URL+"/sbgl_sbxdwcdj/getSbxdwcdjByJlid", 'get',data)
//7.根据slid修改设备携带外出登记
export const updateSbxdwcdjBySlid = data => createAPI(BASE_URL+"/sbgl_sbxdwcdj/updateSbxdwcdjBySlid", 'post',data)

