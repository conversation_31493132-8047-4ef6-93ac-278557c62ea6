/**
 * 数据比对工具类
*/

/**
 * 数据比对方法
 * 返回结果为一个包含两个对象的数组，没有改动将返回空数组
 * 返回有改动的旧数据和新数据
*/
export const dataComparison = (oldObj, newObj) => {
  let resArr = [{},{}]
  // let oldKeys = Object.keys(oldObj)
  let newKeys = Object.keys(newObj)
  newKeys.forEach(newKey => {
    // 当新对象里的字段旧对象里没有时判定为有改动
    if(!oldObj[newKey]) {
      resArr[1][newKey] = newObj[newKey]
    } else if(oldObj[newKey] == newObj[newKey]) {
      // 不做处理
    } else {
      resArr[0][newKey] = oldObj[newKey]
      resArr[1][newKey] = newObj[newKey]
    }
  })
  return resArr
}