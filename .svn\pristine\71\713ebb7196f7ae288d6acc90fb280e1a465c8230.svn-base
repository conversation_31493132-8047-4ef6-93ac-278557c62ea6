<template>
  <div v-show="isShow" class="out" :style="{ width: width }">
    <!-- <div class="article" :style="{'--articleWidth':articleWidth, '--background':articleBackground}" @click="goPath()"> -->
    <div
      class="article"
      :style="{ '--articleWidth': articleWidth }"
      @click="goPath()"
    >
      <!-- '--background':articleBackground -->
      <div class="img-div">
        <img v-if="imgType == 'ztqk'" src="../../assets/icons/head01.png" />
        <img v-if="imgType == 'dbgz'" src="../../assets/icons/head02.png" />
        <img v-if="imgType == 'yjgz'" src="../../assets/icons/head03.png" />
        <img v-if="imgType == 'zczp'" src="../../assets/icons/head04.png" />
        <img v-if="imgType == 'tzgl'" src="../../assets/icons/head05.png" />
        <img v-if="imgType == 'bggl'" src="../../assets/icons/head06.png" />
        <img v-if="imgType == 'rcgz'" src="../../assets/icons/lstz_d.png" />
        <img v-if="imgType == 'sjrz'" src="../../assets/icons/head07.png" />
        <img v-if="imgType == 'wdgz'" src="../../assets/icons/wdgz_d.png" />
        <img
          style="width: 60px; height: 60px"
          v-if="imgType == 'bmai'"
          src="../../assets/icons/chat.png"
        />
        <!-- <img v-if="imgType == 'xggj'" src="../../assets/icons/head07.png" />
        <img v-if="imgType == 'xtsz'" src="../../assets/icons/head08.png" />
        <img v-if="imgType == 'quit'" src="../../assets/icons/head09.png" /> -->
      </div>
      <div
        class="title"
        :style="{ 'font-size': titleFontSize, color: titleFontColor }"
      >
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 背景色 #0660FF #000660FF
      articleBackground: "#000660FF",
      /**
       * 图标及按钮组合div的宽度
       * 通过计算获得
       * out div下所有元素均分out div宽度
       * css自定义属性
       * （尚未实现事件自动回抛，src路径问题，这里开发中）
       */
      articleWidth: "100%",
      // item元素集合（尚未实现事件自动回抛，src路径问题，这里开发中）
      itemList: [
        { src: "../../assets/icons/header_icon9.png", title: "系统设置" },
        { src: "../../assets/icons/header_icon9.png", title: "退出" },
      ],
      // 组件显隐
      isShow: true,
    };
  },
  props: {
    // 组件ID，用以进行选中状态背景色变更的
    componentSelectedId: {
      type: Number,
      require: true,
    },
    // 组件ID，用以进行选中状态背景色变更的
    componentId: {
      type: Number,
      require: true,
    },
    // 点击事件触发的路由跳转
    toPath: {
      type: String,
      default: undefined,
    },
    // 组件宽度
    width: {
      type: String,
      default: "100%",
    },
    /**
     * 图片logo类型
     * 这种写法是为了解决打包后无法找到图片的问题，应该还有其他解决办法
     */
    imgType: {
      type: String,
      default: "",
    },
    // 组件显示文本内容
    title: {
      type: String,
      default: "",
    },
    // 文本内容文字大小，默认12px
    titleFontSize: {
      type: String,
      default: "16px",
    },
    // 文本内容文字颜色，默认 white
    titleFontColor: {
      type: String,
      default: "white",
    },
  },
  methods: {
    goPath() {
      if (this.toPath == "/ztqksy") {
        let dwmc = localStorage.getItem("dwmc");
        let fs = localStorage.getItem("fs");
        let bmid = localStorage.getItem("bmid");
        this.$router.push({
          path: this.toPath,
          query: {
            dwmc: dwmc,
            fs: fs,
            bmid: bmid,
          },
        });
        // 回抛组件自己的ID给父组件，用以通知组件更新自己的选中状态
        this.$emit("childSelected", this.componentId);
        return;
      }
      if (this.toPath != "/ztqksy") {
        // console.log('菜单逻辑 goPath this.toPath', this.toPath)
        if (this.toPath !== undefined && this.toPath != "") {
          // console.log('菜单逻辑 准备调转路由', this.toPath)
          this.$router.push(this.toPath);
          // 回抛组件自己的ID给父组件，用以通知组件更新自己的选中状态
          this.$emit("childSelected", this.componentId);
          return;
        }
      }

      // 路由检测
      this.$notify({
        title: "提示",
        message: "功能页面开发中",
        type: "warning",
        offset: 100,
      });
    },
  },
  mounted() {},
  watch: {
    componentSelectedId(newVal, oldVal) {
      // console.log('菜单逻辑 componentSelectedId', this.componentSelectedId, newVal, this.componentSelectedId == newVal)
      // if (this.componentId == newVal) {
      //   this.articleBackground = '#0660FF'
      // } else {
      //   this.articleBackground = '#000660FF'
      // }
      if (this.componentId == newVal) {
        this.articleBackground =
          "linear-gradient(180deg, rgba(255,255,255,0.00) 31%, rgba(255,255,255,0.15) 99%)";
      } else {
        this.articleBackground = "#000660FF";
      }
    },
    $route: {
      handler(to, from) {
        // console.log('菜单逻辑 imgs2 route', to.path, from.path)
        // 如果当前路由是组件自己的路由，则回抛我被选中的事件
        if (to.path == this.toPath) {
          this.$emit("childSelected", this.componentId);
        }
      },
      deep: true,
    },
    // 监听vuex状态机
    "$store.state.Counter": {
      handler(newVal, oldVal) {
        // console.log('监测到vuex状态机顶部菜单显隐变化', newVal, newVal.showHeaderMenuList)
        if (
          newVal &&
          newVal.showHeaderMenuList &&
          Object.keys(newVal.showHeaderMenuList).length > 0
        ) {
          if (newVal.showHeaderMenuList.indexOf(this.imgType) != -1) {
            this.isShow = true;
          } else {
            this.isShow = false;
          }
          // console.log('监测到vuex状态机顶部菜单显隐变化', newVal.showHeaderMenuList, this.imgType, newVal.showHeaderMenuList.indexOf(this.imgType), this.isShow)
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped>
.out {
  /* background: yellowgreen; */
  height: 100%;
  /* padding: 5px; */
  box-sizing: border-box;
  display: inline-block;
}
.out .article {
  height: 100%;
  text-align: center;
  background: var(--background);
  /* width: 50%; */
  width: var(--articleWidth);
  float: left;
  padding-top: 10%;
  box-sizing: border-box;
}
.out .article:hover {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 31%,
    rgba(255, 255, 255, 0.15) 99%
  );
  cursor: pointer;
}
.out .article .img-div {
  /* background: pink; */
  height: 50%;
  vertical-align: top;
  box-sizing: border-box;
  /* float: left; */
  /* width: 40%; */
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: horizontal;
  -webkit-box-pack: center;
  -webkit-box-align: center;
}
.out .article .img-div img {
  height: 66px;
  position: relative;
  top: 0%;
  left: 0%;
}
.out .article .title {
  height: 50%;
  /* background: red; */
  vertical-align: top;
  box-sizing: border-box;
  font-size: 20px;
  /* float: left; */
  /* width: 60%; */
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: horizontal;
  -webkit-box-pack: center;
  -webkit-box-align: center;
}
</style>