<template>
  <div class="content">
    <div ref="charts" style="width: calc(100vw * 0.487); height: calc(100vh * 0.51)"></div>
  </div>
</template>


<script>
import * as echarts from "echarts";
import { getDwCount } from "../../../../api/dpzs";
import axios from "axios";
import dxal from "../../../assets/mapJson/232700.js"
export default {
  data() {
    return {
      dtList: [],
    };
  },
  created() { },
  mounted() {
    this.getQxMap();
  },
  methods: {
    async getQxMap() {
      let params = {
        citycode: "232700",
        // citycode: "232700",
        // citycode: "231200",
        // citycode: "230800",
      };
      let data = await getDwCount(params);
      this.dtList = data.map((item) => {
        item.value = item.count;
        return item;
      });
      console.log(this.dtList);
      this.$nextTick(() => {
        this.initCharts();
      });
    },
    initCharts() {
      const charts = echarts.init(this.$refs["charts"]);

      // var uploadedDataURL = 
      //   "https://www.isqqw.com/asset/get/areas_v3/city/232700_full.json"; //大兴安岭地图
      //   // "https://www.isqqw.com/asset/get/areas_v3/city/231000_full.json"; //牡丹江地图
      //   // "https://www.isqqw.com/asset/get/areas_v3/city/231200_full.json"; //绥化地图
      //   // "https://www.isqqw.com/asset/get/areas_v3/city/231200_full.json"; //佳木斯地图
      // 在这里处理响应数据，并将其用于注册地图
      echarts.registerMap("uploadedDataURL", dxal);

      const option = {
        // backgroundColor: '#020933',

        tooltip: {
          show: true,
          trigger: "item",
          formatter: function (params) {
            return params.name + " : " + params.value;
          },
        },
        visualMap: {
          min: this.dtList[0].value,
          max: this.dtList[this.dtList.length - 1].value,
          // right: "15%",
          right: "10%", //大兴安岭
          text: ["高", "低"],
          textStyle: {
            color: "#fff",
          },
          realtime: false,
          calculable: true,
          inRange: {
            color: [
              "#052570",
              "#063B98",
              "#1760E4",
              "#0793FA",
              "#00BDFF",
              "#07DDF5",
            ],
          },
        },
        series: [
          {
            name: "绥化地图全览",
            type: "map",
            map: "uploadedDataURL",
            roam: false, //是否允许缩放
            zoom: 1.25, //默认显示级别
            label: {
              normal: {
                show: true,
                color: "#fff",
                fontSize: 12,
              },
              emphasis: {
                show: true,
                color: "#fff",
                fontSize: 12,
              },
            },
            emphasis: {
              itemStyle: {
                areaColor: "#70EAF4", // 高亮时候地图显示的颜色
                borderWidth: 1, // 高亮时的边框宽度
              },
              label: {
                fontSize: 12, // 选中地图文字字号和字体颜色
                color: "#fff",
              },
            },
            itemStyle: {
              normal: {
                areaColor: "#3894ec",
                borderColor: "#3fdaff",
                borderWidth: 2,
                shadowColor: "rgba(63, 218, 255, 0.5)",
                shadowBlur: 30,
              },
              emphasis: {
                //交互时效果
                areaColor: "#2b91b7",
                color: "#000",
                label: {
                  show: true,
                },
              },
            },
            // data: [
            //   { name: "庆安县", value: 11 },
            //   { name: "绥棱县", value: 14 },
            //   { name: "海伦市", value: 31 },
            //   { name: "明水县", value: 6 },
            //   { name: "青冈县", value: 44 },
            //   { name: "望奎县", value: 49 },
            //   { name: "北林区", value: 20 },
            //   { name: "安达市", value: 4 },
            //   { name: "兰西县", value: 5 },
            //   { name: "肇东市", value: 21 },
            // ],
            data: this.dtList,
          },
        ],
      };
      // 地图注册，第一个参数的名字必须和option.geo.map一致
      // echarts.registerMap("china",zhongguo)

      charts.setOption(option);

    },
  },
};
</script>
