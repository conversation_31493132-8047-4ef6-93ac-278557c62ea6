{"version": 3, "sources": ["webpack:///src/renderer/view/ztqk/sbxhxx.vue", "webpack:///./src/renderer/view/ztqk/sbxhxx.vue?b631", "webpack:///./src/renderer/view/ztqk/sbxhxx.vue"], "names": ["sbxhxx", "name", "components", "props", "data", "form", "jfbh", "jfdd", "sblx", "sbbh", "sbxh", "sbpp", "sbxlh", "xhsj", "zrr", "czr", "flag", "sfdc", "computed", "watch", "methods", "queryEquipmentByCondition", "_this", "this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "console", "log", "$route", "query", "equipmentId", "Object", "jfxj", "equipmentCode", "then", "res", "relocationCabinetName", "relocationComputerRoomName", "relocationInstitution", "relocationLocation", "area", "now", "Date", "formattedDate", "getFullYear", "String", "getMonth", "padStart", "getDate", "stop", "dc<PERSON><PERSON>", "_this2", "_callee2", "_context2", "$confirm", "cancelButtonClass", "confirmButtonText", "cancelButtonText", "type", "code", "$message", "success", "dom_download", "catch", "abrupt", "content", "fileName", "blob", "Blob", "url", "window", "URL", "createObjectURL", "dom", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "created", "mounted", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "activated", "ztqk_sbxhxx", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "_m", "_v", "staticStyle", "width", "margin", "margin-top", "ref", "attrs", "model", "label-width", "label-position", "label", "disabled", "value", "callback", "$$v", "$set", "expression", "on", "$event", "staticRenderFns", "src", "__webpack_require__", "alt", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "sLA0EAA,GACAC,KAAA,GAEAC,cACAC,SACAC,KALA,WAOA,OACAC,MACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,MAAA,GACAC,KAAA,GACAC,IAAA,GACAC,IAAA,IAEAC,KAAA,GACAC,MAAA,IAIAC,YAEAC,SAEAC,SACAC,0BADA,WACA,IAAAC,EAAAC,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,OAAAH,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACAC,QAAAC,IACAZ,EAAAa,OAAAC,MAAAC,YACA,mCAGAC,OAAAC,EAAA,EAAAD,EACAE,cAAAlB,EAAAa,OAAAC,MAAAC,cACAI,KAAA,SAAAC,GACAT,QAAAC,IAAAQ,GACApB,EAAAjB,KAAAqC,EAAAtC,KAAA,GACAkB,EAAAjB,KAAAsC,sBAAArB,EAAAa,OAAAC,MAAAO,sBACArB,EAAAjB,KAAAuC,2BAAAtB,EAAAa,OAAAC,MAAAQ,2BACAtB,EAAAjB,KAAAwC,sBAAAvB,EAAAa,OAAAC,MAAAS,sBACAvB,EAAAjB,KAAAyC,mBAAAxB,EAAAa,OAAAC,MAAAU,mBACAxB,EAAAjB,KAAA0C,KAAAzB,EAAAa,OAAAC,MAAAW,KAEA,IAAAC,EAAA,IAAAC,KAQAC,EALAF,EAAAG,cAKA,IAJAC,OAAAJ,EAAAK,WAAA,GAAAC,SAAA,OAIA,IAHAF,OAAAJ,EAAAO,WAAAD,SAAA,OAIAhC,EAAAjB,KAAAQ,KAAAqC,IA1BA,wBAAApB,EAAA0B,SAAA5B,EAAAN,KAAAE,IAgCAiC,SAjCA,WAiCA,IAAAC,EAAAnC,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAgC,IAAA,OAAAlC,EAAAC,EAAAG,KAAA,SAAA+B,GAAA,cAAAA,EAAA7B,KAAA6B,EAAA5B,MAAA,UACA,KAAA0B,EAAA1C,KADA,CAAA4C,EAAA5B,KAAA,eAGA0B,EAAAG,SAAA,sBACAC,kBAAA,oBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAxB,KAAA,WACAH,OAAAC,EAAA,EAAAD,CAAAoB,EAAArD,MAAAoC,KAAA,SAAAC,GACA,KAAAA,EAAAwB,OACAR,EAAAS,SAAAC,QAAA,QACAV,EAAA1C,KAAA,IACAsB,OAAAC,EAAA,EAAAD,CAAAoB,EAAArD,MAAAoC,KAAA,SAAAC,GACAgB,EAAAW,aAAA3B,EAAA,qBAIA4B,MAAA,cAnBAV,EAAAW,OAAA,iBAwBAjC,OAAAC,EAAA,EAAAD,CAAAoB,EAAArD,MAAAoC,KAAA,SAAAC,GACA,KAAAA,EAAAwB,OACAR,EAAAS,SAAAC,QAAA,QACAV,EAAA1C,KAAA,IACAsB,OAAAC,EAAA,EAAAD,CAAAoB,EAAArD,MAAAoC,KAAA,SAAAC,GACAgB,EAAAW,aAAA3B,EAAA,mBA7BA,wBAAAkB,EAAAJ,SAAAG,EAAAD,KAAAlC,IAsCA6C,aAvEA,SAuEAG,EAAAC,GACA,IAAAC,EAAA,IAAAC,MAAAH,IAEAI,EAAAC,OAAAC,IAAAC,gBAAAL,GACAM,EAAAC,SAAAC,cAAA,KACAjD,QAAAC,IAAA,MAAA8C,GACAA,EAAAG,MAAAC,QAAA,OACAJ,EAAAK,KAAAT,EACAI,EAAAM,aAAA,WAAAb,GACAQ,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,UAIAC,QAlHA,aAoHAC,QApHA,WAqHApE,KAAAF,6BAGAuE,aAxHA,aA0HAC,YA1HA,aA4HAC,aA5HA,aA8HAC,QA9HA,aAgIAC,cAhIA,aAkIAC,UAlIA,aAoIAC,UApIA,cCvEeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA9E,KAAa+E,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,QAAkBL,EAAAM,GAAA,GAAAN,EAAAO,GAAA,KAAAJ,EAAA,OAAkCK,aAAaC,MAAA,MAAAC,OAAA,SAAAC,aAAA,UAAqDR,EAAA,WAAgBS,IAAA,OAAAC,OAAkBC,MAAAd,EAAAhG,KAAA+G,cAAA,QAAAC,iBAAA,UAAgEb,EAAA,OAAYK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,iBAAAoH,SAAA,SAAAC,GAA2DrB,EAAAsB,KAAAtB,EAAAhG,KAAA,mBAAAqH,IAA4CE,WAAA,4BAAqC,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,iBAAAoH,SAAA,SAAAC,GAA2DrB,EAAAsB,KAAAtB,EAAAhG,KAAA,mBAAAqH,IAA4CE,WAAA,4BAAqC,OAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAgCK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,IAAAoH,SAAA,SAAAC,GAA8CrB,EAAAsB,KAAAtB,EAAAhG,KAAA,MAAAqH,IAA+BE,WAAA,eAAwB,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,cAAAoH,SAAA,SAAAC,GAAwDrB,EAAAsB,KAAAtB,EAAAhG,KAAA,gBAAAqH,IAAyCE,WAAA,yBAAkC,OAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAgCK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,eAAAoH,SAAA,SAAAC,GAAyDrB,EAAAsB,KAAAtB,EAAAhG,KAAA,iBAAAqH,IAA0CE,WAAA,0BAAmC,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,KAAAoH,SAAA,SAAAC,GAA+CrB,EAAAsB,KAAAtB,EAAAhG,KAAA,OAAAqH,IAAgCE,WAAA,gBAAyB,OAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAgCK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,WAAiBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAhG,KAAA,sBAAAoH,SAAA,SAAAC,GAAgErB,EAAAsB,KAAAtB,EAAAhG,KAAA,wBAAAqH,IAAiDE,WAAA,iCAA0C,aAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAsCK,aAAazB,QAAA,OAAA0B,MAAA,QAAAC,OAAA,SAAAC,aAAA,UAAwER,EAAA,OAAYE,YAAA,gBAAAmB,IAAgCpC,MAAA,SAAAqC,GAAyB,OAAAzB,EAAA5C,eAAwB4C,EAAAO,GAAA,gBAE72EmB,iBADjB,WAAoC,IAAazB,EAAb/E,KAAagF,eAA0BC,EAAvCjF,KAAuCkF,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAAA,EAAA,OAAsBU,OAAOc,IAAMC,EAAQ,QAAiBC,IAAA,QAAlK3G,KAA8KqF,GAAA,KAAAJ,EAAA,OAA0BE,YAAA,cAAxMnF,KAAgOqF,GAAA,iBCEpQ,IAcAuB,EAdyBF,EAAQ,OAcjCG,CACEpI,EACAmG,GATF,EAVA,SAAAkC,GACEJ,EAAQ,SAaV,kBAEA,MAUeK,EAAA,QAAAH,EAAiB", "file": "js/4.060802b70bb234ba69cf.js", "sourcesContent": ["<!--  -->\n<template>\n  <div class=\"box\">\n    <div class=\"top-box\">\n      <div>\n        <img src=\"./img/title.png\" alt=\"\" />\n      </div>\n      <div class=\"top-title\">设备销毁信息</div>\n    </div>\n    <div style=\"width: 60%;margin: 0 auto;margin-top: 20px;\">\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        label-width=\"120px\"\n        label-position=\"left\"\n      >\n        <div style=\"display: flex;\">\n          <el-form-item label=\"机房编号\" class=\"mg20\">\n            <el-input v-model=\"form.computerRoomCode\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"机房地点\">\n            <el-input v-model=\"form.computerRoomName\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <div style=\"display: flex;\">\n          <el-form-item label=\"设备类型\" class=\"mg20\">\n            <el-input v-model=\"form.csm\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"设备编号\">\n            <el-input v-model=\"form.equipmentCode\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <div style=\"display: flex;\">\n          <el-form-item label=\"设备型号\" class=\"mg20\">\n            <el-input v-model=\"form.equipmentModel\" disabled></el-input>\n          </el-form-item>\n          <!-- <el-form-item label=\"设备品牌\">\n            <el-input v-model=\"form.sbpp\"></el-input>\n          </el-form-item> -->\n          <el-form-item label=\"销毁时间\">\n            <el-input v-model=\"form.xhsj\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <div style=\"display: flex;\">\n          <el-form-item label=\"设备序列号\" class=\"mg20\">\n            <el-input v-model=\"form.equipmentSerialNumber\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <!-- <div style=\"display: flex;\">\n          <el-form-item label=\"责任人\" class=\"mg20\">\n            <el-input v-model=\"form.zrr\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"操作人\">\n            <el-input v-model=\"form.czr\"></el-input>\n          </el-form-item>\n        </div> -->\n      </el-form>\n    </div>\n    <div style=\"display: flex;width: 160px;margin: 0 auto;margin-top: 20px;\">\n      <!-- <div class=\"buttonw btnc1\"  @click=\"savetj()\">提交</div> -->\n      <div class=\"buttonw btnc2\"  @click=\"dcbutton()\">提交并导出</div>\n    </div>\n  </div>\n</template>\n\n<script>\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n//例如：import 《组件名称》 from '《组件路径》';\nimport {\n  queryEquipmentByCondition,\n  saveDestructionEquipment,\n  exportDestructionEquipment,\n  saveMigrateEquipment, exportMigrateEquipment\n} from \"../../../api/jfxj\";\nexport default {\n  name: \"\",\n  //import引入的组件需要注入到对象中才能使用\n  components: {},\n  props: {},\n  data() {\n    //这里存放数据\n    return {\n      form: {\n        jfbh: \"\",\n        jfdd: \"\",\n        sblx: \"\",\n        sbbh: \"\",\n        sbxh: \"\",\n        sbpp: \"\",\n        sbxlh: \"\",\n        xhsj: \"\",\n        zrr: \"\",\n        czr: \"\"\n      },\n      flag: \"\",\n      sfdc: false,\n    };\n  },\n  //监听属性 类似于data概念\n  computed: {},\n  //监控data中的数据变化\n  watch: {},\n  //方法集合\n  methods: {\n    async queryEquipmentByCondition() {\n      console.log(\n        this.$route.query.equipmentId,\n        \"this.$route.query.equipmentCode\"\n      );\n\n      queryEquipmentByCondition({\n        equipmentCode: this.$route.query.equipmentId\n      }).then(res => {\n        console.log(res);\n        this.form = res.data[0];\n        this.form.relocationCabinetName = this.$route.query.relocationCabinetName;\n        this.form.relocationComputerRoomName = this.$route.query.relocationComputerRoomName;\n        this.form.relocationInstitution = this.$route.query.relocationInstitution;\n        this.form.relocationLocation = this.$route.query.relocationLocation;\n        this.form.area = this.$route.query.area;\n        // 创建一个新的Date对象，获取当前日期和时间\n        var now = new Date();\n\n        // 获取年、月、日\n        var year = now.getFullYear();\n        var month = String(now.getMonth() + 1).padStart(2, \"0\"); // 月份从0开始，需要加1，并且格式化为两位数\n        var day = String(now.getDate()).padStart(2, \"0\"); // 格式化为两位数\n\n        // 组合成年-月-日的格式\n        var formattedDate = year + \"-\" + month + \"-\" + day;\n        this.form.xhsj = formattedDate;\n      });\n    },\n    // async savetj() {\n\n    // },\n    async dcbutton() {\n      if (this.flag == '1') {\n        // 回抛退出事件给父组件，用以退出系统\n        this.$confirm('设备已销毁，是否确认提交？', '提示', {\n          cancelButtonClass: \"btn-custom-cancel\",\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n          // center: true\n        }).then(() => {\n          saveDestructionEquipment(this.form).then(res => {\n            if (res.code == 10000) {\n              this.$message.success(\"提交成功\");\n              this.flag = '1'\n              exportDestructionEquipment(this.form).then(res => {\n                this.dom_download(res, \"设备销毁信息\" + \".xls\");\n              });\n            }\n          });\n        }).catch(() => {\n        })\n        return\n      }\n      else {\n        saveDestructionEquipment(this.form).then(res => {\n          if (res.code == 10000) {\n            this.$message.success(\"提交成功\");\n            this.flag = '1'\n            exportDestructionEquipment(this.form).then(res => {\n              this.dom_download(res, \"设备销毁信息\" + \".xls\");\n            });\n          }\n        });\n      }\n\n\n    },\n    //处理下载流\n    dom_download(content, fileName) {\n      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\n      //console.log(blob)\n      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象\n      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download\n      console.log(\"dom\", dom);\n      dom.style.display = 'none'\n      dom.href = url\n      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\n      document.body.appendChild(dom)\n      dom.click()\n    },\n  },\n  //生命周期 - 创建完成（可以访问当前this实例）\n  created() {},\n  //生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.queryEquipmentByCondition();\n  },\n  //生命周期 - 创建之前\n  beforeCreate() {},\n  //生命周期 - 挂载之前\n  beforeMount() {},\n  //生命周期 - 更新之前\n  beforeUpdate() {},\n  //生命周期 - 更新之后\n  updated() {},\n  //生命周期 - 销毁之前\n  beforeDestroy() {},\n  //生命周期 - 销毁完成\n  destroyed() {},\n  //如果页面有keep-alive缓存功能，这个函数会触发\n  activated() {}\n};\n</script>\n<style scoped>\n.box {\n  width: 1580px;\n  margin: 0 auto;\n}\n.top-box {\n  width: 100%;\n  display: flex;\n  border-bottom: 1px solid #e5e5e5;\n  margin-top: 20px;\n}\n.top-title {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n  margin-left: 10px;\n}\n.mg20 {\n  margin-right: 20px;\n}\n.buttonw {\n  cursor: pointer;\n  width: 123px;\n  height: 32px;\n  text-align: center;\n  line-height: 32px;\n  color: #fff;\n  border-radius: 4px;\n}\n.btnc1 {\n  background-color: #3e9efe;\n}\n.btnc2 {\n  background-color: #3ecafe;\n  margin-left: 20px;\n}\n/deep/ .el-form-item__content {\n  width: 350px !important;\n}\n/deep/ .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-form--label-left .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-input.is-disabled .el-input__inner{\n  font-size: 18px;\n}\n</style>\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/ztqk/sbxhxx.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"box\"},[_vm._m(0),_vm._v(\" \"),_c('div',{staticStyle:{\"width\":\"60%\",\"margin\":\"0 auto\",\"margin-top\":\"20px\"}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"label-width\":\"120px\",\"label-position\":\"left\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"机房编号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.computerRoomCode),callback:function ($$v) {_vm.$set(_vm.form, \"computerRoomCode\", $$v)},expression:\"form.computerRoomCode\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机房地点\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.computerRoomName),callback:function ($$v) {_vm.$set(_vm.form, \"computerRoomName\", $$v)},expression:\"form.computerRoomName\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"设备类型\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.csm),callback:function ($$v) {_vm.$set(_vm.form, \"csm\", $$v)},expression:\"form.csm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备编号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.equipmentCode),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentCode\", $$v)},expression:\"form.equipmentCode\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"设备型号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.equipmentModel),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentModel\", $$v)},expression:\"form.equipmentModel\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"销毁时间\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.xhsj),callback:function ($$v) {_vm.$set(_vm.form, \"xhsj\", $$v)},expression:\"form.xhsj\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"设备序列号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.equipmentSerialNumber),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentSerialNumber\", $$v)},expression:\"form.equipmentSerialNumber\"}})],1)],1)])],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"160px\",\"margin\":\"0 auto\",\"margin-top\":\"20px\"}},[_c('div',{staticClass:\"buttonw btnc2\",on:{\"click\":function($event){return _vm.dcbutton()}}},[_vm._v(\"提交并导出\")])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"top-box\"},[_c('div',[_c('img',{attrs:{\"src\":require(\"./img/title.png\"),\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"top-title\"},[_vm._v(\"设备销毁信息\")])])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-c4afe38a\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/ztqk/sbxhxx.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-c4afe38a\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./sbxhxx.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./sbxhxx.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./sbxhxx.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-c4afe38a\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./sbxhxx.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-c4afe38a\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/sbxhxx.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}