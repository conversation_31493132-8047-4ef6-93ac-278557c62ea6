<template>
  <div class="out" :style="{ 'width': width }">
    <div class="article" :style="{ '--articleWidth': articleWidth }" @click="goPath()">
      <div class="img-div">
        <img v-if="imgType == 'minimize'" src="../../assets/icons/zxh.png" />
        <!-- <img v-if="imgType == 'xtsz'" src="../../assets/icons/header_icon8.png" />
        <img v-if="imgType == 'quit'" src="../../assets/icons/header_icon9.png" /> -->
      </div>
      <div class="title" :style="{ 'font-size': titleFontSize, 'color': titleFontColor }">
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      /**
       * 图标及按钮组合div的宽度
       * 通过计算获得
       * out div下所有元素均分out div宽度
       * css自定义属性
       * （尚未实现事件自动回抛，src路径问题，这里开发中）
      */
      articleWidth: '100%',
      // item元素集合（尚未实现事件自动回抛，src路径问题，这里开发中）
      itemList: [
        { src: '../../assets/icons/header_icon9.png', title: '系统设置' },
        { src: '../../assets/icons/header_icon9.png', title: '退出' }
      ]
    }
  },
  props: {
    // 点击事件触发的路由跳转
    toPath: {
      type: String,
      default: undefined
    },
    // 组件宽度
    width: {
      type: String,
      default: '100%'
    },
    /**
     * 图片logo类型
     * 这种写法是为了解决打包后无法找到图片的问题，应该还有其他解决办法
    */
    imgType: {
      type: String,
      default: ''
    },
    // 组件显示文本内容
    title: {
      type: String,
      default: ''
    },
    // 文本内容文字大小，默认12px
    titleFontSize: {
      type: String,
      default: '14px'
    },
    // 文本内容文字颜色，默认 white
    titleFontColor: {
      type: String,
      default: 'white'
    }
  },
  methods: {
    goPath() {
      if (this.toPath !== undefined && this.toPath != '') {
        if(this.imgType == 'minimize') {
          console.log('最小化')
          this.$electron.ipcRenderer.send("hide-window")
          return
        }
        if (this.imgType == 'quit') {
          // 回抛退出事件给父组件，用以退出系统
          this.$confirm('是否执行此操作，点击将退出系统？', '是否退出系统？', {
            cancelButtonClass: "btn-custom-cancel",
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            // center: true
          }).then(() => {
            this.$emit('quitClicked', true)
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '取消退出系统'
            })
          })
          return
        }
        this.$router.push(this.toPath)
        return
      }
      // 路由检测
      this.$notify({
        title: '提示',
        message: '功能页面开发中',
        type: 'warning',
        offset: 100
      })
    }
  },
  mounted() {
  }
}
</script>

<style scoped>
.out {
  /* background: yellowgreen; */
  height: 100%;
  padding: 5% ;
  box-sizing: border-box;
  display: inline-block;
}

.article {
  height: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  /* background: green; */
  /* width: 50%; */
  width: var(--articleWidth);
  float: left;
}

.article:hover {
  /* background: #00000059; */
  cursor: pointer;
}

.article .img-div {
  /* background: yellow; */
  height: 24px;
  display: flex;
  align-items: center;
  vertical-align: top;
  box-sizing: border-box;
  /* float: left;
  width: 40%; */
  /* display: -webkit-box; */
  -webkit-box-orient: horizontal;
  -webkit-box-pack: center;
  -webkit-box-align: center;
}

.article .img-div img {
  /* height: 50%;
  position: relative;
  top: 50%;
  left: 0%; */
  /* margin-left: 2px; */
}

.article .title {
  /* height: 100%; */
  /* background: red; */
  vertical-align: top;
  display: flex;
  align-items: center;
  /* padding-top: 9%; */
  box-sizing: border-box;
  /* font-size: 16px;
  float: left;
  width: 60%; */
  /* display: -webkit-box; */
  -webkit-box-orient: horizontal;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  padding-left: 5px;
}

.btn-custom-cancel {
  float: right !important;
  margin-left: 10px !important;
}
</style>