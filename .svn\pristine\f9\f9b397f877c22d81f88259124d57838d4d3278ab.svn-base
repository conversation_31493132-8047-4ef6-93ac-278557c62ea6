{"version": 3, "sources": ["webpack:///src/renderer/view/ztqk/xjlc.vue", "webpack:///./src/renderer/view/ztqk/xjlc.vue?3320", "webpack:///./src/renderer/view/ztqk/xjlc.vue"], "names": ["xjlc", "name", "components", "props", "data", "_form", "page", "pageSize", "total", "tableData", "sbmc", "xh", "jgwz", "xlh", "gzqkms", "clff", "hfsj", "active", "form", "jfmc", "jfbh", "jgmc", "jgbh", "sbbh", "temperature", "humidity", "cleanliness", "airConditioning", "powerEnvironmentSystem", "id", "computerRoomId", "computerRoomCode", "computerRoomName", "inspectionStartTime", "inspectionEndTime", "defineProperty_default", "sflist", "label", "value", "jglist", "sblist", "lxlist", "computed", "abnormalData", "abnormalItems", "this", "for<PERSON>ach", "item", "push", "equipmentOnline", "equipmentPower", "equipmentNetwork", "equipmentRunningSound", "equipmentWarm", "equipmentLoosen", "equipmentDowntime", "equipmentHardDisk", "equipmentOhticalModule", "equipmentPowerModule", "equipmentFan", "equipmentTemperature", "equipmentPort", "equipmentOpticalFiber", "equipmentNetworkCable", "equipmentLabel", "cableDamaged", "cableRibbon", "cableJoint", "cableLabel", "watch", "methods", "handleCurrentChange", "val", "handleSizeChange", "tableCellStyle", "tableHeaderCellStyle", "init", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "jfxj", "sent", "code", "console", "log", "stop", "xyb", "_this2", "_callee2", "_context2", "scanCode", "$route", "query", "queryCabinetByCondition", "queryEquipmentByCondition", "queryEquipmentByConditionlx", "map", "computerRoomInspectionId", "inspectionCabinetList", "then", "res", "inspectionEquipmentList", "$router", "path", "save", "_this3", "_callee3", "_context3", "$message", "message", "type", "dom_download", "content", "fileName", "blob", "Blob", "url", "window", "URL", "createObjectURL", "dom", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "_this4", "_callee4", "_context4", "cabinetCode", "<PERSON><PERSON><PERSON>", "_this5", "_callee5", "_context5", "equipmentMainType", "equipmentCode", "equipmentName", "_this6", "_callee6", "_context6", "syb", "created", "mounted", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "activated", "ztqk_xjlc", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "_m", "_v", "staticStyle", "width", "margin", "margin-top", "attrs", "finish-status", "title", "text-align", "justify-content", "ref", "model", "label-width", "label-position", "position", "placement", "trigger", "margin-bottom", "color", "top", "left", "slot", "callback", "$$v", "$set", "expression", "_l", "key", "_s", "_e", "margin-left", "on", "height", "overflow-y", "align-items", "border-bottom", "font-family", "font-size", "font-weight", "margin-right", "refInFor", "border", "padding-left", "header-cell-style", "cell-style", "max-height", "prop", "align", "background", "pager-count", "current-pageNo", "pageNo-sizes", "pageNo-size", "layout", "current-change", "size-change", "length", "staticRenderFns", "src", "__webpack_require__", "alt", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "kMAkoBAA,GACAC,KAAA,GAEAC,cACAC,SACAC,KALA,WAKA,IAAAC,EAEA,OACAC,KAAA,EACAC,SAAA,GACAC,MAAA,EACAC,YAEAC,KAAA,QACAC,GAAA,MACAC,KAAA,QACAC,IAAA,OACAC,OAAA,UACAC,KAAA,QACAC,KAAA,UAGAN,KAAA,QACAC,GAAA,MACAC,KAAA,QACAC,IAAA,OACAC,OAAA,UACAC,KAAA,QACAC,KAAA,UAGAN,KAAA,QACAC,GAAA,MACAC,KAAA,QACAC,IAAA,OACAC,OAAA,UACAC,KAAA,QACAC,KAAA,UAGAC,OAAA,EACAC,MAAAb,GACAc,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAZ,KAAA,GACAa,KAAA,GACAC,YAAA,IACAC,SAAA,IACAC,YAAA,IACAC,gBAAA,IACAC,uBAAA,IACAC,GAAA,GACAC,eAAA,GACAC,iBAAA,GACAC,iBAAA,GACAC,oBAAA,GACAC,kBAAA,IAjBAC,IAAA9B,EAAA,cAkBA,KAlBA8B,IAAA9B,EAAA,WAmBA,KAnBA8B,IAAA9B,EAAA,cAoBA,KApBA8B,IAAA9B,EAAA,kBAqBA,KArBA8B,IAAA9B,EAAA,yBAsBA,KAtBAA,GAwBA+B,SAEAC,MAAA,IACAC,MAAA,OAGAD,MAAA,IACAC,MAAA,QAGAC,UACAC,UACAC,YAIAC,UACAC,aADA,WAEA,IAAAC,GACA1B,QACAqB,UACAC,UACAC,WA0EA,MAtEA,MAAAI,KAAA3B,KAAAM,cACAoB,EAAA1B,KAAAM,YAAA,WAEA,MAAAqB,KAAA3B,KAAAO,WACAmB,EAAA1B,KAAAO,SAAA,WAEA,MAAAoB,KAAA3B,KAAAQ,cACAkB,EAAA1B,KAAAQ,YAAA,YAEA,MAAAmB,KAAA3B,KAAAS,kBACAiB,EAAA1B,KAAAS,gBAAA,WAEA,MAAAkB,KAAA3B,KAAAU,yBACAgB,EAAA1B,KAAAU,uBAAA,aAIAiB,KAAAN,OAAAO,QAAA,SAAAC,GAEA,MAAAA,EAAAvB,aACA,MAAAuB,EAAAtB,UACA,MAAAsB,EAAArB,aAEAkB,EAAAL,OAAAS,KAAAD,KAWAF,KAAAL,OAAAM,QAAA,SAAAC,GAEA,MAAAA,EAAAE,iBACA,MAAAF,EAAAG,gBACA,MAAAH,EAAAI,kBACA,MAAAJ,EAAAK,uBACA,MAAAL,EAAAM,eACA,MAAAN,EAAAO,iBACA,MAAAP,EAAAQ,mBACA,MAAAR,EAAAS,mBACA,MAAAT,EAAAU,wBACA,MAAAV,EAAAW,sBACA,MAAAX,EAAAY,cACA,MAAAZ,EAAAa,sBACA,MAAAb,EAAAc,eACA,MAAAd,EAAAe,uBACA,MAAAf,EAAAgB,uBACA,MAAAhB,EAAAiB,gBAEApB,EAAAJ,OAAAQ,KAAAD,KAMAF,KAAAJ,OAAAK,QAAA,SAAAC,GAEA,MAAAA,EAAAkB,cACA,MAAAlB,EAAAmB,aACA,MAAAnB,EAAAoB,YACA,MAAApB,EAAAqB,YAEAxB,EAAAH,OAAAO,KAAAD,KAIAH,IA8CAyB,SAEAC,SACAC,oBADA,SACAC,GACA3B,KAAAvC,KAAAkE,GAEAC,iBAJA,SAIAD,GACA3B,KAAAtC,SAAAiE,GAEAE,eAPA,WAQA,8FAEAC,qBAVA,WAWA,kHAEAC,KAbA,WAaA,IAAAC,EAAAhC,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAA9E,EAAA,OAAA2E,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EAGAC,OAAAC,EAAA,EAAAD,GAHA,OAIA,MADAnF,EAHAgF,EAAAK,MAIAC,OACAtF,OAAAoB,aACAmE,QAAAC,IAAA,MAEAf,EAAA3D,KAAAd,SAEAuF,QAAAC,IAAA,MAEAf,EAAA3D,KAAAM,YAAA,IACAqD,EAAA3D,KAAAO,SAAA,IACAoD,EAAA3D,KAAAQ,YAAA,IACAmD,EAAA3D,KAAAS,gBAAA,IACAkD,EAAA3D,KAAAU,uBAAA,MAhBA,wBAAAwD,EAAAS,SAAAX,EAAAL,KAAAC,IAuBAgB,IApCA,WAoCA,IAAAC,EAAAlD,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAe,IAAA,IAAA5F,EAAA,OAAA2E,EAAAC,EAAAG,KAAA,SAAAc,GAAA,cAAAA,EAAAZ,KAAAY,EAAAX,MAAA,UACAK,QAAAC,IAAAG,EAAApD,cAEA,GAAAoD,EAAA9E,OAHA,CAAAgF,EAAAX,KAAA,eAIAS,EAAA7E,KAAAgF,SAAAH,EAAAI,OAAAC,MAAAF,SAJAD,EAAAX,KAAA,EAKAC,OAAAC,EAAA,EAAAD,CAAAQ,EAAA7E,MALA,OAMA,MADAd,EALA6F,EAAAR,MAMAC,OACAK,EAAA7E,KAAAW,GAAAzB,OACA2F,EAAAM,0BACAN,EAAAO,4BACAP,EAAAQ,+BAVA,OAgBA,GAAAR,EAAA9E,SACA8E,EAAApD,aAAAJ,OAAAiE,IAAA,SAAAzD,GACAA,EAAA0D,yBAAAV,EAAA7E,KAAAW,KAEA0D,OAAAC,EAAA,EAAAD,EACAW,SAAAH,EAAAI,OAAAC,MAAAF,SACAQ,sBAAAX,EAAApD,aAAAJ,OACAkE,yBAAAV,EAAA7E,KAAAW,KACA8E,KAAA,SAAAC,OAEA,GAAAb,EAAA9E,SACA8E,EAAApD,aAAAH,OAAAgE,IAAA,SAAAzD,GACAA,EAAA0D,yBAAAV,EAAA7E,KAAAW,KAEA0D,OAAAC,EAAA,EAAAD,EACAW,SAAAH,EAAAI,OAAAC,MAAAF,SACAW,wBAAAd,EAAApD,aAAAH,OACAiE,yBAAAV,EAAA7E,KAAAW,KACA8E,KAAA,SAAAC,OAEA,GAAAb,EAAA9E,QACA8E,EAAApD,aAAAF,OAAA+D,IAAA,SAAAzD,GACAA,EAAA0D,yBAAAV,EAAA7E,KAAAW,KAEA0D,OAAAC,EAAA,EAAAD,EACAW,SAAAH,EAAAI,OAAAC,MAAAF,SACAW,wBAAAd,EAAApD,aAAAF,OACAgE,yBAAAV,EAAA7E,KAAAW,KACA8E,KAAA,SAAAC,MACAb,EAAAe,QAAA9D,MACA+D,KAAA,UACAX,OACAvE,GAAAkE,EAAA7E,KAAAW,GACAqE,SAAAH,EAAAI,OAAAC,MAAAF,aAIAH,EAAA9E,SArDA,yBAAAgF,EAAAJ,SAAAG,EAAAD,KAAAjB,IAwDAkC,KA5FA,WA4FA,IAAAC,EAAApE,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAiC,IAAA,OAAAnC,EAAAC,EAAAG,KAAA,SAAAgC,GAAA,cAAAA,EAAA9B,KAAA8B,EAAA7B,MAAA,OACAC,OAAAC,EAAA,EAAAD,EACAsB,wBAAAI,EAAAtE,aAAAF,OACAgE,yBAAAQ,EAAA/F,KAAAW,GACAqE,SAAAe,EAAAd,OAAAC,MAAAF,WACAS,KAAA,SAAAC,GACA,KAAAA,EAAAlB,MACAuB,EAAAG,UACAC,QAAA,OACAC,KAAA,cAIA/B,OAAAC,EAAA,EAAAD,EACAkB,yBAAAQ,EAAA/F,KAAAW,KACA8E,KAAA,SAAAC,GACAK,EAAAM,aAAAX,EAAA,gBAhBA,wBAAAO,EAAAtB,SAAAqB,EAAAD,KAAAnC,IA2BAyC,aAvHA,SAuHAC,EAAAC,GACA,IAAAC,EAAA,IAAAC,MAAAH,IAEAI,EAAAC,OAAAC,IAAAC,gBAAAL,GACAM,EAAAC,SAAAC,cAAA,KACAvC,QAAAC,IAAA,MAAAoC,GACAA,EAAAG,MAAAC,QAAA,OACAJ,EAAAK,KAAAT,EACAI,EAAAM,aAAA,WAAAb,GACAQ,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,SAEApC,wBAnIA,WAmIA,IAAAqC,EAAA7F,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAA0D,IAAA,OAAA5D,EAAAC,EAAAG,KAAA,SAAAyD,GAAA,cAAAA,EAAAvD,KAAAuD,EAAAtD,MAAA,OACAC,OAAAC,EAAA,EAAAD,EACAsD,YAAAH,EAAAxH,KAAAI,KACAwH,YAAAJ,EAAAxH,KAAAG,KACAoF,yBAAAiC,EAAAxH,KAAAW,KACA8E,KAAA,SAAAC,GACAjB,QAAAC,IAAAgB,GACA,KAAAA,EAAAlB,OACAgD,EAAAnG,OAAAqE,EAAAxG,QARA,wBAAAwI,EAAA/C,SAAA8C,EAAAD,KAAA5D,IAaAwB,0BAhJA,WAgJA,IAAAyC,EAAAlG,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAA+D,IAAA,OAAAjE,EAAAC,EAAAG,KAAA,SAAA8D,GAAA,cAAAA,EAAA5D,KAAA4D,EAAA3D,MAAA,OACAC,OAAAC,EAAA,EAAAD,EACA2D,kBAAA,IACAL,YAAAE,EAAA7H,KAAAI,KACAwH,YAAAC,EAAA7H,KAAAG,KACA8H,cAAAJ,EAAA7H,KAAAK,KACA6H,cAAAL,EAAA7H,KAAAR,KACA+F,yBAAAsC,EAAA7H,KAAAW,KACA8E,KAAA,SAAAC,GACA,KAAAA,EAAAlB,OACAqD,EAAAvG,OAAAoE,EAAAxG,QAVA,wBAAA6I,EAAApD,SAAAmD,EAAAD,KAAAjE,IAeAyB,4BA/JA,WA+JA,IAAA8C,EAAAxG,KAAA,OAAAiC,IAAAC,EAAAC,EAAAC,KAAA,SAAAqE,IAAA,OAAAvE,EAAAC,EAAAG,KAAA,SAAAoE,GAAA,cAAAA,EAAAlE,KAAAkE,EAAAjE,MAAA,OACAC,OAAAC,EAAA,EAAAD,EACA2D,kBAAA,IAKAzC,yBAAA4C,EAAAnI,KAAAW,KACA8E,KAAA,SAAAC,GAEA,KAAAA,EAAAlB,OACA2D,EAAA5G,OAAAmE,EAAAxG,QAXA,wBAAAmJ,EAAA1D,SAAAyD,EAAAD,KAAAvE,IAeA0E,IA9KA,WA+KA3G,KAAA5B,WAIAwI,QApYA,aAsYAC,QAtYA,WAuYA7G,KAAA+B,QAGA+E,aA1YA,aA4YAC,YA5YA,aA8YAC,aA9YA,aAgZAC,QAhZA,aAkZAC,cAlZA,aAoZAC,UApZA,aAsZAC,UAtZA,cC/nBeC,GADEC,OAFjB,WAA0B,IAAAC,EAAAvH,KAAawH,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,QAAkBL,EAAAM,GAAA,GAAAN,EAAAO,GAAA,KAAAJ,EAAA,OAAkCK,aAAaC,MAAA,MAAAC,OAAA,SAAAC,aAAA,UAAqDR,EAAA,YAAiBS,OAAO/J,OAAAmJ,EAAAnJ,OAAAgK,gBAAA,aAA+CV,EAAA,WAAgBS,OAAOE,MAAA,YAAkBd,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,UAAgBd,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,UAAgBd,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,WAAgB,OAAAd,EAAAO,GAAA,SAAAP,EAAAnJ,OAAAsJ,EAAA,OAAmDK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,WAAAG,aAAoCO,aAAA,YAAuBf,EAAAO,GAAA,8BAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA6DK,aAAaxC,QAAA,OAAAgD,kBAAA,SAAAL,aAAA,UAAiER,EAAA,WAAgBc,IAAA,OAAAL,OAAkBM,MAAAlB,EAAAlJ,KAAAqK,cAAA,QAAAC,iBAAA,UAAgEjB,EAAA,gBAAqBK,aAAaa,SAAA,YAAsBT,OAAQ3I,MAAA,cAAoBkI,EAAA,cAAmBS,OAAOU,UAAA,QAAAb,MAAA,MAAAc,QAAA,WAAqDpB,EAAA,OAAAA,EAAA,OAAsBK,aAAaxC,QAAA,OAAAwD,gBAAA,UAAyCrB,EAAA,KAAUE,YAAA,eAAAG,aAAwCiB,MAAA,UAAAJ,SAAA,WAAAK,IAAA,SAAqD1B,EAAAO,GAAA,KAAAJ,EAAA,OAAwBE,YAAA,SAAmBL,EAAAO,GAAA,UAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAyCE,YAAA,SAAmBL,EAAAO,GAAA,qDAAAP,EAAAO,GAAA,KAAAJ,EAAA,KAAkFE,YAAA,eAAAG,aAAwCiB,MAAA,UAAAJ,SAAA,WAAAK,IAAA,OAAAC,KAAA,SAAoEf,OAAQgB,KAAA,aAAmBA,KAAA,gBAAkB5B,EAAAO,GAAA,KAAAJ,EAAA,kBAAqCe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,YAAA+K,SAAA,SAAAC,GAAsD9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,cAAAgL,IAAuCE,WAAA,qBAAgChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCK,aAAaa,SAAA,YAAsBT,OAAQ3I,MAAA,cAAoBkI,EAAA,cAAmBS,OAAOU,UAAA,QAAAb,MAAA,MAAAc,QAAA,WAAqDpB,EAAA,OAAAA,EAAA,OAAsBK,aAAaxC,QAAA,OAAAwD,gBAAA,UAAyCrB,EAAA,KAAUE,YAAA,eAAAG,aAAwCiB,MAAA,UAAAJ,SAAA,WAAAK,IAAA,SAAqD1B,EAAAO,GAAA,KAAAJ,EAAA,OAAwBE,YAAA,SAAmBL,EAAAO,GAAA,UAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAyCE,YAAA,SAAmBL,EAAAO,GAAA,qDAAAP,EAAAO,GAAA,KAAAJ,EAAA,KAAkFE,YAAA,eAAAG,aAAwCiB,MAAA,UAAAJ,SAAA,WAAAK,IAAA,OAAAC,KAAA,SAAoEf,OAAQgB,KAAA,aAAmBA,KAAA,gBAAkB5B,EAAAO,GAAA,KAAAJ,EAAA,kBAAqCe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,SAAA+K,SAAA,SAAAC,GAAmD9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,WAAAgL,IAAoCE,WAAA,kBAA6BhC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,eAAqBkI,EAAA,kBAAuBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,YAAA+K,SAAA,SAAAC,GAAsD9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,cAAAgL,IAAuCE,WAAA,qBAAgChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,gBAAA+K,SAAA,SAAAC,GAA0D9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,kBAAAgL,IAA2CE,WAAA,yBAAoChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,kBAAwBkI,EAAA,kBAAuBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,uBAAA+K,SAAA,SAAAC,GAAiE9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,yBAAAgL,IAAkDE,WAAA,gCAA2ChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,iBAAA8H,EAAAoC,KAAApC,EAAAO,GAAA,SAAAP,EAAAnJ,OAAAsJ,EAAA,OAAqEK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,UAAuDR,EAAA,OAAYE,YAAA,WAAAG,aAAoCO,aAAA,YAAuBf,EAAAO,GAAA,4BAAAP,EAAAO,GAAA,KAAAJ,EAAA,WAA+Dc,IAAA,OAAAZ,YAAA,WAAAG,aAA+CG,aAAA,QAAoBC,OAAQM,MAAAlB,EAAAlJ,KAAAqK,cAAA,QAAAC,iBAAA,UAAgEjB,EAAA,OAAYK,aAAaxC,QAAA,UAAkBmC,EAAA,gBAAqBS,OAAO3I,MAAA,UAAgBkI,EAAA,YAAiBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,KAAA+K,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,OAAAgL,IAAgCE,WAAA,gBAAyB,GAAAhC,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCK,aAAa6B,cAAA,QAAqBzB,OAAQ3I,MAAA,UAAgBkI,EAAA,YAAiBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,KAAA+K,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,OAAAgL,IAAgCE,WAAA,gBAAyB,GAAAhC,EAAAO,GAAA,KAAAJ,EAAA,OAA4BE,YAAA,QAAAiC,IAAwBjE,MAAA2B,EAAA/D,2BAAqC+D,EAAAO,GAAA,cAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA6CK,aAAa+B,OAAA,QAAAC,aAAA,SAAsCxC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,OAAiBK,aAAaxC,QAAA,OAAAyE,cAAA,SAAA9B,aAAA,OAAA+B,gBAAA,mCAA6GvC,EAAA,OAAYK,aAAamC,cAAA,uBAAAC,YAAA,OAAAnB,MAAA,UAAAoB,cAAA,MAAAC,eAAA,OAAArC,MAAA,WAAqIT,EAAAO,GAAA,iBAAAP,EAAAmC,GAAAxJ,EAAA+F,aAAA,kBAAAsB,EAAAO,GAAA,KAAAJ,EAAA,OAAAA,EAAA,WAAyGc,IAAA,OAAA8B,UAAA,EAAAnC,OAAgCM,MAAAlB,EAAAlJ,KAAAqK,cAAA,QAAAC,iBAAA,UAAgEjB,EAAA,gBAAqBS,OAAO3I,MAAA,aAAmBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,YAAAkJ,SAAA,SAAAC,GAAkD9B,EAAA+B,KAAApJ,EAAA,cAAAmJ,IAAmCE,WAAA,qBAAgChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,SAAAkJ,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAApJ,EAAA,WAAAmJ,IAAgCE,WAAA,kBAA6BhC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,YAAAkJ,SAAA,SAAAC,GAAkD9B,EAAA+B,KAAApJ,EAAA,cAAAmJ,IAAmCE,WAAA,qBAAgChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,mBAAmB,OAAA8H,EAAAoC,KAAApC,EAAAO,GAAA,SAAAP,EAAAnJ,OAAAsJ,EAAA,OAA2DK,aAAaC,MAAA,SAAAC,OAAA,SAAAC,aAAA,UAAwDR,EAAA,OAAYE,YAAA,WAAAG,aAAoCO,aAAA,YAAuBf,EAAAO,GAAA,4BAAAP,EAAAO,GAAA,KAAAJ,EAAA,WAA+Dc,IAAA,OAAAZ,YAAA,WAAAG,aAA+CG,aAAA,QAAoBC,OAAQM,MAAAlB,EAAAlJ,KAAAqK,cAAA,QAAAC,iBAAA,UAAgEjB,EAAA,OAAYK,aAAaxC,QAAA,UAAkBmC,EAAA,gBAAqBS,OAAO3I,MAAA,UAAgBkI,EAAA,YAAiBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,KAAA+K,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,OAAAgL,IAAgCE,WAAA,gBAAyB,GAAAhC,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCK,aAAa6B,cAAA,QAAqBzB,OAAQ3I,MAAA,UAAgBkI,EAAA,YAAiBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,KAAA+K,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,OAAAgL,IAAgCE,WAAA,gBAAyB,GAAAhC,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCK,aAAa6B,cAAA,QAAqBzB,OAAQ3I,MAAA,UAAgBkI,EAAA,YAAiBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,KAAA+K,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,OAAAgL,IAAgCE,WAAA,gBAAyB,GAAAhC,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCK,aAAa6B,cAAA,QAAqBzB,OAAQ3I,MAAA,UAAgBkI,EAAA,YAAiBe,OAAOhJ,MAAA8H,EAAAlJ,KAAA,KAAA+K,SAAA,SAAAC,GAA+C9B,EAAA+B,KAAA/B,EAAAlJ,KAAA,OAAAgL,IAAgCE,WAAA,gBAAyB,GAAAhC,EAAAO,GAAA,KAAAJ,EAAA,OAA4BE,YAAA,QAAAiC,IAAwBjE,MAAA2B,EAAA9D,6BAAuC8D,EAAAO,GAAA,cAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA6CK,aAAa+B,OAAA,QAAAC,aAAA,SAAsCxC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,OAAiBK,aAAaxC,QAAA,OAAAyE,cAAA,SAAA9B,aAAA,OAAAqC,OAAA,oBAAAC,eAAA,UAAgH9C,EAAA,OAAYK,aAAamC,cAAA,uBAAAC,YAAA,OAAAnB,MAAA,UAAAoB,cAAA,MAAAC,eAAA,OAAArC,MAAA,QAAAM,aAAA,YAA2Jf,EAAAO,GAAA,iBAAAP,EAAAmC,GAAAxJ,EAAAqG,eAAA,kBAAAgB,EAAAO,GAAA,KAAAJ,EAAA,OAA6FE,YAAA,YAAsBF,EAAA,WAAgBc,IAAA,OAAA8B,UAAA,EAAAnC,OAAgCM,MAAAlB,EAAAlJ,KAAAqK,cAAA,QAAAC,iBAAA,UAAgEjB,EAAA,OAAYK,aAAaxC,QAAA,OAAAgD,kBAAA,gBAAAP,MAAA,YAAqEN,EAAA,gBAAqBS,OAAO3I,MAAA,YAAkBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,gBAAAkJ,SAAA,SAAAC,GAAsD9B,EAAA+B,KAAApJ,EAAA,kBAAAmJ,IAAuCE,WAAA,yBAAoChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,eAAAkJ,SAAA,SAAAC,GAAqD9B,EAAA+B,KAAApJ,EAAA,iBAAAmJ,IAAsCE,WAAA,wBAAmChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,gBAAsBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,sBAAAkJ,SAAA,SAAAC,GAA4D9B,EAAA+B,KAAApJ,EAAA,wBAAAmJ,IAA6CE,WAAA,+BAA0ChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,WAAA8H,EAAAO,GAAA,KAAAJ,EAAA,OAAmCK,aAAaxC,QAAA,OAAAgD,kBAAA,gBAAAP,MAAA,YAAqEN,EAAA,gBAAqBS,OAAO3I,MAAA,aAAmBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,cAAAkJ,SAAA,SAAAC,GAAoD9B,EAAA+B,KAAApJ,EAAA,gBAAAmJ,IAAqCE,WAAA,uBAAkChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,aAAmBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,gBAAAkJ,SAAA,SAAAC,GAAsD9B,EAAA+B,KAAApJ,EAAA,kBAAAmJ,IAAuCE,WAAA,yBAAoChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,aAAmBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,kBAAAkJ,SAAA,SAAAC,GAAwD9B,EAAA+B,KAAApJ,EAAA,oBAAAmJ,IAAyCE,WAAA,2BAAsChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,WAAA8H,EAAAO,GAAA,KAAAJ,EAAA,OAAmCK,aAAaxC,QAAA,OAAAgD,kBAAA,gBAAAP,MAAA,YAAqEN,EAAA,gBAAqBS,OAAO3I,MAAA,eAAqBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,kBAAAkJ,SAAA,SAAAC,GAAwD9B,EAAA+B,KAAApJ,EAAA,oBAAAmJ,IAAyCE,WAAA,2BAAsChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,eAAqBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,uBAAAkJ,SAAA,SAAAC,GAA6D9B,EAAA+B,KAAApJ,EAAA,yBAAAmJ,IAA8CE,WAAA,gCAA2ChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,gBAAsBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,qBAAAkJ,SAAA,SAAAC,GAA2D9B,EAAA+B,KAAApJ,EAAA,uBAAAmJ,IAA4CE,WAAA,8BAAyChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,WAAA8H,EAAAO,GAAA,KAAAJ,EAAA,OAAmCK,aAAaxC,QAAA,OAAAyC,MAAA,SAAAO,kBAAA,mBAAqEb,EAAA,gBAAqBS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,aAAAkJ,SAAA,SAAAC,GAAmD9B,EAAA+B,KAAApJ,EAAA,eAAAmJ,IAAoCE,WAAA,sBAAiChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,qBAAAkJ,SAAA,SAAAC,GAA2D9B,EAAA+B,KAAApJ,EAAA,uBAAAmJ,IAA4CE,WAAA,8BAAyChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,cAAAkJ,SAAA,SAAAC,GAAoD9B,EAAA+B,KAAApJ,EAAA,gBAAAmJ,IAAqCE,WAAA,uBAAkChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,WAAA8H,EAAAO,GAAA,KAAAJ,EAAA,OAAmCK,aAAaxC,QAAA,OAAAyC,MAAA,SAAAO,kBAAA,mBAAqEb,EAAA,gBAAqBS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,sBAAAkJ,SAAA,SAAAC,GAA4D9B,EAAA+B,KAAApJ,EAAA,wBAAAmJ,IAA6CE,WAAA,+BAA0ChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCK,aAAa6B,cAAA,QAAqBzB,OAAQ3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,sBAAAkJ,SAAA,SAAAC,GAA4D9B,EAAA+B,KAAApJ,EAAA,wBAAAmJ,IAA6CE,WAAA,+BAA0ChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCK,aAAa6B,cAAA,QAAqBzB,OAAQ3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,eAAAkJ,SAAA,SAAAC,GAAqD9B,EAAA+B,KAAApJ,EAAA,iBAAAmJ,IAAsCE,WAAA,wBAAmChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,qBAAqB,OAAA8H,EAAAoC,KAAApC,EAAAO,GAAA,SAAAP,EAAAnJ,OAAAsJ,EAAA,OAA2DK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,UAAuDR,EAAA,OAAYE,YAAA,WAAAG,aAAoCO,aAAA,YAAuBf,EAAAO,GAAA,4BAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA2DK,aAAa+B,OAAA,QAAAC,aAAA,SAAsCxC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,OAAiBK,aAAaxC,QAAA,OAAAyE,cAAA,SAAA9B,aAAA,OAAA+B,gBAAA,mCAA6GvC,EAAA,OAAYK,aAAamC,cAAA,uBAAAC,YAAA,OAAAnB,MAAA,UAAAoB,cAAA,MAAAC,eAAA,UAAqH9C,EAAAO,GAAA,iBAAAP,EAAAmC,GAAAxJ,EAAAqG,eAAA,kBAAAgB,EAAAO,GAAA,KAAAJ,EAAA,OAAAA,EAAA,WAA2Gc,IAAA,OAAA8B,UAAA,EAAAnC,OAAgCM,MAAAlB,EAAAlJ,KAAAqK,cAAA,QAAAC,iBAAA,UAAgEjB,EAAA,gBAAqBS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,aAAAkJ,SAAA,SAAAC,GAAmD9B,EAAA+B,KAAApJ,EAAA,eAAAmJ,IAAoCE,WAAA,sBAAiChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,YAAAkJ,SAAA,SAAAC,GAAkD9B,EAAA+B,KAAApJ,EAAA,cAAAmJ,IAAmCE,WAAA,qBAAgChC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,eAAqBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,WAAAkJ,SAAA,SAAAC,GAAiD9B,EAAA+B,KAAApJ,EAAA,aAAAmJ,IAAkCE,WAAA,oBAA+BhC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,OAAA8H,EAAAO,GAAA,KAAAJ,EAAA,gBAAwCS,OAAO3I,MAAA,cAAoBkI,EAAA,kBAAuBe,OAAOhJ,MAAAS,EAAA,WAAAkJ,SAAA,SAAAC,GAAiD9B,EAAA+B,KAAApJ,EAAA,aAAAmJ,IAAkCE,WAAA,oBAA+BhC,EAAAiC,GAAAjC,EAAA,gBAAArH,GAAoC,OAAAwH,EAAA,YAAsB+B,IAAAvJ,EAAAV,MAAA2I,OAAsB3I,MAAAU,EAAAV,SAAoB+H,EAAAO,GAAAP,EAAAmC,GAAAxJ,EAAAT,YAA+B,mBAAmB,KAAA8H,EAAAoC,KAAApC,EAAAO,GAAA,SAAAP,EAAAnJ,OAAAsJ,EAAA,OAAyDK,aAAaC,MAAA,SAAAC,OAAA,SAAAC,aAAA,UAAwDR,EAAA,OAAYE,YAAA,WAAAG,aAAoCO,aAAA,YAAuBf,EAAAO,GAAA,iCAAAP,EAAAO,GAAA,KAAAP,EAAAM,GAAA,GAAAN,EAAAO,GAAA,KAAAJ,EAAA,OAAsFK,aAAaC,MAAA,OAAAE,aAAA,UAAoCR,EAAA,YAAiBK,aAAaC,MAAA,QAAeG,OAAQ5K,KAAAgK,EAAA3J,UAAA6M,oBAAAlD,EAAAzF,qBAAA4I,aAAAnD,EAAA1F,eAAA8I,aAAA,WAAwHjD,EAAA,mBAAwBS,OAAOyC,KAAA,OAAApL,MAAA,OAAAqL,MAAA,YAA+CtD,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCS,OAAOyC,KAAA,KAAApL,MAAA,KAAAqL,MAAA,YAA2CtD,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCS,OAAOyC,KAAA,OAAApL,MAAA,OAAAqL,MAAA,YAA+CtD,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCS,OAAOyC,KAAA,MAAApL,MAAA,MAAAqL,MAAA,YAA6CtD,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCS,OAAOyC,KAAA,SAAApL,MAAA,SAAAqL,MAAA,YAAmDtD,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCS,OAAOyC,KAAA,OAAApL,MAAA,OAAAqL,MAAA,YAA+CtD,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCS,OAAOyC,KAAA,OAAApL,MAAA,OAAAqL,MAAA,aAA+C,GAAAtD,EAAAO,GAAA,KAAAJ,EAAA,OAA4BK,aAAaG,aAAA,UAAqBR,EAAA,iBAAsBS,OAAO2C,WAAA,GAAAC,cAAA,EAAAC,iBAAAzD,EAAA9J,KAAAwN,gBAAA,YAAAC,cAAA3D,EAAA7J,SAAAyN,OAAA,0CAAAxN,MAAA4J,EAAA5J,OAAyLkM,IAAKuB,iBAAA7D,EAAA7F,oBAAA2J,cAAA9D,EAAA3F,qBAA6E,SAAA2F,EAAAoC,KAAApC,EAAAO,GAAA,KAAAJ,EAAA,OAA2CK,aAAaxC,QAAA,OAAAyC,MAAA,QAAAO,kBAAA,SAAAN,OAAA,SAAAC,aAAA,UAAmG,GAAAX,EAAAnJ,QAAA,GAAAmJ,EAAAnJ,OAAAsJ,EAAA,OAAiDE,YAAA,eAAAiC,IAA+BjE,MAAA2B,EAAAZ,OAAiBY,EAAAO,GAAA,2BAAAP,EAAAoC,KAAApC,EAAAO,GAAA,QAAAP,EAAAnJ,QAAAmJ,EAAAzH,aAAAH,OAAA2L,OAAA5D,EAAA,OAAuHE,YAAA,gBAAAiC,IAAgCjE,MAAA2B,EAAAtE,OAAiBsE,EAAAO,GAAA,2BAAAP,EAAAoC,KAAApC,EAAAO,GAAA,QAAAP,EAAAnJ,QAAAmJ,EAAAzH,aAAAH,OAAA2L,OAA0K/D,EAAAoC,KAA1KjC,EAAA,OAAwHE,YAAA,gBAAAiC,IAAgCjE,MAAA2B,EAAApD,QAAkBoD,EAAAO,GAAA,kCAE39kByD,iBADjB,WAAoC,IAAa/D,EAAbxH,KAAayH,eAA0BC,EAAvC1H,KAAuC2H,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAAA,EAAA,OAAsBS,OAAOqD,IAAMC,EAAQ,QAAiBC,IAAA,QAAlK1L,KAA8K8H,GAAA,KAAAJ,EAAA,OAA0BE,YAAA,cAAxM5H,KAAgO8H,GAAA,aAAqB,WAAc,IAAAP,EAAAvH,KAAawH,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBK,aAAaxC,QAAA,OAAAgD,kBAAA,gBAAAL,aAAA,UAAwER,EAAA,OAAYE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,WAAqBL,EAAAO,GAAA,YAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA2CE,YAAA,WAAqBL,EAAAO,GAAA,0BAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAyDE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,WAAqBL,EAAAO,GAAA,UAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAyCE,YAAA,WAAqBL,EAAAO,GAAA,2BAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA0DE,YAAA,WAAqBF,EAAA,OAAYE,YAAA,WAAqBL,EAAAO,GAAA,UAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAyCE,YAAA,WAAqBL,EAAAO,GAAA,eCEz6B,IAcA6D,EAdyBF,EAAQ,OAcjCG,CACEzO,EACAkK,GATF,EAVA,SAAAwE,GACEJ,EAAQ,SAaV,kBAEA,MAUeK,EAAA,QAAAH,EAAiB", "file": "js/9.f145ec3b2d3c47d84f67.js", "sourcesContent": ["<!--  -->\n<template>\n  <div class=\"box\">\n    <div class=\"top-box\">\n      <div>\n        <img src=\"./img/title.png\" alt=\"\" />\n      </div>\n      <div class=\"top-title\">巡检流程</div>\n    </div>\n\n    <div style=\"width: 70%;margin: 0 auto;margin-top: 50px;\">\n      <el-steps :active=\"active\" finish-status=\"success\">\n        <el-step title=\"1.机房环境\"></el-step>\n        <el-step title=\"2.机柜\"></el-step>\n        <el-step title=\"3.设备\"></el-step>\n        <el-step title=\"4.缆线\"></el-step>\n        <!-- <el-step title=\"巡查异常情况明细表\"></el-step> -->\n      </el-steps>\n    </div>\n    <div\n      style=\"width: 640px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"active === 1\"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        1.机房环境\n      </div>\n      <div style=\"display: flex;justify-content: center;margin-top: 20px;\">\n        <el-form\n          ref=\"form\"\n          :model=\"form\"\n          label-width=\"260px\"\n          label-position=\"left\"\n        >\n          <el-form-item label=\"机房温度是否正常\" style=\"position: relative;\">\n            <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\n              <div>\n                <div style=\"display:flex;margin-bottom:10px\">\n                  <i\n                    class=\"el-icon-info\"\n                    style=\"color:#409eef;    position: relative;\n    top: 2px;\"\n                  ></i>\n                  <div class=\"tszt\">提示</div>\n                </div>\n                <div class=\"smzt\">\n                  传感器自动获取\n                </div>\n              </div>\n              <i\n                class=\"el-icon-info\"\n                style=\"color:#409eef;position: absolute; top: 14px;\n    left: -90px;\"\n                slot=\"reference\"\n              ></i>\n            </el-popover>\n            <el-radio-group v-model=\"form.temperature\">\n              <el-radio\n                v-for=\"item in sflist\"\n                :label=\"item.label\"\n                :key=\"item.label\"\n                >{{ item.value }}</el-radio\n              >\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"机房湿度是否正常\" style=\"position: relative;\">\n            <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\n              <div>\n                <div style=\"display:flex;margin-bottom:10px\">\n                  <i\n                    class=\"el-icon-info\"\n                    style=\"color:#409eef;    position: relative;\n    top: 2px;\"\n                  ></i>\n                  <div class=\"tszt\">提示</div>\n                </div>\n                <div class=\"smzt\">\n                  传感器自动获取\n                </div>\n              </div>\n              <i\n                class=\"el-icon-info\"\n                style=\"color:#409eef;position: absolute; top: 14px;\n    left: -90px;\"\n                slot=\"reference\"\n              ></i>\n            </el-popover>\n            <el-radio-group v-model=\"form.humidity\">\n              <el-radio\n                v-for=\"item in sflist\"\n                :label=\"item.label\"\n                :key=\"item.label\"\n                >{{ item.value }}</el-radio\n              >\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"机房清洁度是否正常\">\n            <el-radio-group v-model=\"form.cleanliness\">\n              <el-radio\n                v-for=\"item in sflist\"\n                :label=\"item.label\"\n                :key=\"item.label\"\n                >{{ item.value }}</el-radio\n              >\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"机房空调是否正常\">\n            <el-radio-group v-model=\"form.airConditioning\">\n              <el-radio\n                v-for=\"item in sflist\"\n                :label=\"item.label\"\n                :key=\"item.label\"\n                >{{ item.value }}</el-radio\n              >\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"机房动力环境系统是否正常\">\n            <el-radio-group v-model=\"form.powerEnvironmentSystem\">\n              <el-radio\n                v-for=\"item in sflist\"\n                :label=\"item.label\"\n                :key=\"item.label\"\n                >{{ item.value }}</el-radio\n              >\n            </el-radio-group>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n    <div\n      style=\"width: 720px;margin: 0 auto;margin-top: 50px;\"\n      v-if=\"active === 2\"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        2.机柜\n      </div>\n      <el-form\n        class=\"inputcss\"\n        ref=\"form\"\n        :model=\"form\"\n        label-width=\"100px\"\n        label-position=\"left\"\n        style=\"margin-top: 20px;\"\n      >\n        <!-- <div style=\"display: flex;\">\n          <el-form-item label=\"机房名称\">\n            <el-input v-model=\"form.jfmc\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"机房编号\" style=\"margin-left: 20px;\">\n            <el-input v-model=\"form.jfbh\"></el-input>\n          </el-form-item>\n        </div> -->\n        <div style=\"display: flex;\">\n          <el-form-item label=\"机柜名称\">\n            <el-input v-model=\"form.jgmc\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"机柜编号\" style=\"margin-left: 20px;\">\n            <el-input v-model=\"form.jgbh\"></el-input>\n          </el-form-item>\n          <div class=\"cxbtn\" @click=\"queryCabinetByCondition\">查询</div>\n        </div>\n      </el-form>\n      <div style=\"height: 270px;overflow-y: auto;\">\n        <div\n          style=\"display: flex;align-items: center;margin-top: 20px;border-bottom: 1px solid rgba(225,225,225,1);\"\n          v-for=\"item in jglist\"\n        >\n          <div\n            style=\"font-family: SourceHanSansSC-Bold;\nfont-size: 20px;\ncolor: #003396;\nfont-weight: 700;\nmargin-right: 42px;width: 120px;\"\n          >\n            {{ item.cabinetName }}\n          </div>\n          <div>\n            <el-form\n              ref=\"form\"\n              :model=\"form\"\n              label-width=\"220px\"\n              label-position=\"left\"\n            >\n              <el-form-item label=\"机柜门是否常闭\">\n                <el-radio-group v-model=\"item.temperature\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n              <el-form-item label=\"机柜电力是否正常\">\n                <el-radio-group v-model=\"item.humidity\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n              <el-form-item label=\"机柜内是否有灰尘\">\n                <el-radio-group v-model=\"item.cleanliness\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n            </el-form>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div\n      style=\"width: 1600px;margin: 0 auto;margin-top: 50px;\"\n      v-if=\"active === 3\"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        3.设备\n      </div>\n      <el-form\n        class=\"inputcss\"\n        ref=\"form\"\n        :model=\"form\"\n        label-width=\"100px\"\n        label-position=\"left\"\n        style=\"margin-top: 20px;\"\n      >\n        <!-- <div style=\"display: flex;\">\n          <el-form-item label=\"机房名称\">\n            <el-input v-model=\"form.jfmc\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"机房编号\" style=\"margin-left: 20px;\">\n            <el-input v-model=\"form.jfbh\"></el-input>\n          </el-form-item>\n        </div> -->\n        <div style=\"display: flex;\">\n          <el-form-item label=\"机柜名称\">\n            <el-input v-model=\"form.jgmc\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"机柜编号\" style=\"margin-left: 20px;\">\n            <el-input v-model=\"form.jgbh\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"设备名称\" style=\"margin-left: 20px;\">\n            <el-input v-model=\"form.sbmc\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"设备编号\" style=\"margin-left: 20px;\">\n            <el-input v-model=\"form.sbbh\"></el-input>\n          </el-form-item>\n          <div class=\"cxbtn\" @click=\"queryEquipmentByCondition\">查询</div>\n        </div>\n        <!-- <div style=\"display: flex;\">\n\n        </div> -->\n      </el-form>\n      <div style=\"height: 386px;overflow-y: auto;\">\n        <div\n          style=\"display: flex;align-items: center;margin-top: 20px;border: 1px solid #c0c4cc;padding-left: 20px;\"\n          v-for=\"item in sblist\"\n        >\n          <div\n            style=\"font-family: SourceHanSansSC-Bold;\nfont-size: 20px;\ncolor: #003396;\nfont-weight: 700;\nmargin-right: 42px;\nwidth: 180px;\ntext-align: center;\"\n          >\n            {{ item.equipmentName }}\n          </div>\n          <div class=\"sb-box3\">\n            <el-form\n              ref=\"form\"\n              :model=\"form\"\n              label-width=\"220px\"\n              label-position=\"left\"\n            >\n              <div\n                style=\"display: flex;justify-content: space-between;\n    width: 1330px;\"\n              >\n                <el-form-item label=\"设备是否在线\">\n                  <el-radio-group v-model=\"item.equipmentOnline\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备电力是否正常\">\n                  <el-radio-group v-model=\"item.equipmentPower\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备运行声音是否正常\">\n                  <el-radio-group v-model=\"item.equipmentRunningSound\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n              </div>\n              <div\n                style=\"display: flex;justify-content: space-between;\n    width: 1330px;\"\n              >\n                <el-form-item label=\"设备是否有告警\">\n                  <el-radio-group v-model=\"item.equipmentWarm\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备是否有松动\">\n                  <el-radio-group v-model=\"item.equipmentLoosen\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备是否有宕机\">\n                  <el-radio-group v-model=\"item.equipmentDowntime\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n              </div>\n              <div\n                style=\"display: flex;justify-content: space-between;\n    width: 1330px;\"\n              >\n                <el-form-item label=\"服务器硬盘是否正常\">\n                  <el-radio-group v-model=\"item.equipmentHardDisk\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备光模块是否齐全\">\n                  <el-radio-group v-model=\"item.equipmentOhticalModule\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备电源模块是否正常\">\n                  <el-radio-group v-model=\"item.equipmentPowerModule\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n              </div>\n              <div\n                style=\"display: flex;width: 1330px;justify-content: space-between;\"\n              >\n                <el-form-item label=\"设备风扇是否正常\">\n                  <el-radio-group v-model=\"item.equipmentFan\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备温度是否正常\">\n                  <el-radio-group v-model=\"item.equipmentTemperature\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item label=\"设备端口是否正常\">\n                  <el-radio-group v-model=\"item.equipmentPort\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n              </div>\n              <div\n                style=\"display: flex;\n    width: 1330px;justify-content: space-between;\"\n              >\n                <el-form-item label=\"设备光纤是否正常\">\n                  <el-radio-group v-model=\"item.equipmentOpticalFiber\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item\n                  label=\"设备网线是否正常\"\n                  style=\"margin-left: 17px;\"\n                >\n                  <el-radio-group v-model=\"item.equipmentNetworkCable\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n                <el-form-item\n                  label=\"设备标签是否正常\"\n                  style=\"margin-left: 17px;\"\n                >\n                  <el-radio-group v-model=\"item.equipmentLabel\">\n                    <el-radio\n                      v-for=\"item in sflist\"\n                      :label=\"item.label\"\n                      :key=\"item.label\"\n                      >{{ item.value }}</el-radio\n                    >\n                  </el-radio-group>\n                </el-form-item>\n              </div>\n            </el-form>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div\n      style=\"width: 720px;margin: 0 auto;margin-top: 50px;\"\n      v-if=\"active === 4\"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        4.缆线\n      </div>\n      <div style=\"height: 410px;overflow-y: auto;\">\n        <div\n          style=\"display: flex;align-items: center;margin-top: 20px;border-bottom: 1px solid rgba(225,225,225,1);\"\n          v-for=\"item in lxlist\"\n        >\n          <div\n            style=\"font-family: SourceHanSansSC-Bold;\nfont-size: 20px;\ncolor: #003396;\nfont-weight: 700;\nmargin-right: 42px;\"\n          >\n            {{ item.equipmentName }}\n          </div>\n          <div>\n            <el-form\n              ref=\"form\"\n              :model=\"form\"\n              label-width=\"220px\"\n              label-position=\"left\"\n            >\n              <el-form-item label=\"线缆有无破损断裂\">\n                <el-radio-group v-model=\"item.cableDamaged\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n              <el-form-item label=\"线缆扎带是否正常\">\n                <el-radio-group v-model=\"item.cableRibbon\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n              <el-form-item label=\"线缆接头是否有松动\">\n                <el-radio-group v-model=\"item.cableJoint\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n              <el-form-item label=\"线缆标签是否正常\">\n                <el-radio-group v-model=\"item.cableLabel\">\n                  <el-radio\n                    v-for=\"item in sflist\"\n                    :label=\"item.label\"\n                    :key=\"item.label\"\n                    >{{ item.value }}</el-radio\n                  >\n                </el-radio-group>\n              </el-form-item>\n            </el-form>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div\n      style=\"width: 1120px;margin: 0 auto;margin-top: 50px;\"\n      v-if=\"active === 5\"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        巡检异常情况明细表\n      </div>\n      <div\n        style=\"display: flex;justify-content: space-between;margin-top: 30px\"\n      >\n        <div class=\"fbtbox\">\n          <div class=\"labels\">巡检机房名称</div>\n          <div class=\"values\">NPJ122000111000000</div>\n        </div>\n        <div class=\"fbtbox\">\n          <div class=\"labels\">巡检时间</div>\n          <div class=\"values\">2024-10-12 11：00：00</div>\n        </div>\n        <div class=\"fbtbox\">\n          <div class=\"labels\">巡检人员</div>\n          <div class=\"values\">张三</div>\n        </div>\n      </div>\n      <div style=\"width: 100%;margin-top: 20px;\">\n        <el-table\n          :data=\"tableData\"\n          style=\"width: 100%\"\n          :header-cell-style=\"tableHeaderCellStyle\"\n          :cell-style=\"tableCellStyle\"\n          max-height=\"800px\"\n        >\n          <el-table-column prop=\"sbmc\" label=\"设备名称\" align=\"center\">\n          </el-table-column>\n          <el-table-column prop=\"xh\" label=\"型号\" align=\"center\">\n          </el-table-column>\n          <el-table-column prop=\"jgwz\" label=\"机柜位置\" align=\"center\">\n          </el-table-column>\n          <el-table-column prop=\"xlh\" label=\"序列号\" align=\"center\">\n          </el-table-column>\n          <el-table-column prop=\"gzqkms\" label=\"故障情况描述\" align=\"center\">\n          </el-table-column>\n          <el-table-column prop=\"clff\" label=\"处理方法\" align=\"center\">\n          </el-table-column>\n          <el-table-column prop=\"hfsj\" label=\"回复时间\" align=\"center\">\n          </el-table-column>\n        </el-table>\n        <div style=\"margin-top: 15px\">\n          <el-pagination\n            background\n            @current-change=\"handleCurrentChange\"\n            @size-change=\"handleSizeChange\"\n            :pager-count=\"5\"\n            :current-pageNo=\"page\"\n            :pageNo-sizes=\"[5, 10, 20, 30]\"\n            :pageNo-size=\"pageSize\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            :total=\"total\"\n          >\n          </el-pagination>\n        </div>\n      </div>\n    </div>\n    <div\n      style=\"display: flex;width: 500px;justify-content: center;\nmargin: 0 auto;margin-top: 60px;\"\n    >\n      <div class=\"buttonw btnc\" @click=\"syb\" v-if=\"active != 1 && active != 5\">\n        上一步\n      </div>\n      <div\n        class=\"buttonw btnc1\"\n        @click=\"xyb\"\n        v-if=\"active != 4 || abnormalData.sblist.length\"\n      >\n        下一步\n      </div>\n      <div\n        class=\"buttonw btnc1\"\n        @click=\"save\"\n        v-if=\"active == 4 && !abnormalData.sblist.length\"\n      >\n        提交并导出\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n//例如：import 《组件名称》 from '《组件路径》';\n\nimport { savaJcpfjlBatch } from \"../../../api/zczp\";\nimport {\n  getInit,\n  saveInspectionComputerRoom,\n  queryCabinetByCondition,\n  queryEquipmentByCondition,\n  saveInspectionCabinet,\n  saveInspectionEquipment,\n  exportInspectionForm\n} from \"../../../api/jfxj\";\n\nexport default {\n  name: \"\",\n  //import引入的组件需要注入到对象中才能使用\n  components: {},\n  props: {},\n  data() {\n    //这里存放数据\n    return {\n      page: 1,\n      pageSize: 10,\n      total: 0,\n      tableData: [\n        {\n          sbmc: \"设备名称1\",\n          xh: \"型号1\",\n          jgwz: \"机柜位置1\",\n          xlh: \"序列号1\",\n          gzqkms: \"故障情况描述1\",\n          clff: \"处理方法1\",\n          hfsj: \"回复时间1\"\n        },\n        {\n          sbmc: \"设备名称2\",\n          xh: \"型号2\",\n          jgwz: \"机柜位置2\",\n          xlh: \"序列号2\",\n          gzqkms: \"故障情况描述2\",\n          clff: \"处理方法2\",\n          hfsj: \"回复时间2\"\n        },\n        {\n          sbmc: \"设备名称3\",\n          xh: \"型号3\",\n          jgwz: \"机柜位置3\",\n          xlh: \"序列号3\",\n          gzqkms: \"故障情况描述3\",\n          clff: \"处理方法3\",\n          hfsj: \"回复时间3\"\n        }\n      ],\n      active: 1,\n      form: {\n        jfmc: \"\",\n        jfbh: \"\",\n        jgmc: \"\",\n        jgbh: \"\",\n        sbmc: \"\",\n        sbbh: \"\",\n        temperature: \"1\",\n        humidity: \"1\",\n        cleanliness: \"1\",\n        airConditioning: \"1\",\n        powerEnvironmentSystem: \"1\",\n        id: \"\",\n        computerRoomId: \"\",\n        computerRoomCode: \"\",\n        computerRoomName: \"\",\n        inspectionStartTime: \"\",\n        inspectionEndTime: \"\",\n        temperature: \"1\",\n        humidity: \"1\",\n        cleanliness: \"1\",\n        airConditioning: \"1\",\n        powerEnvironmentSystem: \"1\"\n      },\n      sflist: [\n        {\n          label: \"1\",\n          value: \"正常\"\n        },\n        {\n          label: \"2\",\n          value: \"不正常\"\n        }\n      ],\n      jglist: [],\n      sblist: [],\n      lxlist: []\n    };\n  },\n  //监听属性 类似于data概念\n  computed: {\n    abnormalData() {\n      const abnormalItems = {\n        form: {},\n        jglist: [],\n        sblist: [],\n        lxlist: []\n      };\n\n      // 检查 form 状态\n      if (this.form.temperature === \"2\") {\n        abnormalItems.form.temperature = \"机房温度不正常\";\n      }\n      if (this.form.humidity === \"2\") {\n        abnormalItems.form.humidity = \"机房湿度不正常\";\n      }\n      if (this.form.cleanliness === \"2\") {\n        abnormalItems.form.cleanliness = \"机房清洁度不正常\";\n      }\n      if (this.form.airConditioning === \"2\") {\n        abnormalItems.form.airConditioning = \"机房空调不正常\";\n      }\n      if (this.form.powerEnvironmentSystem === \"2\") {\n        abnormalItems.form.powerEnvironmentSystem = \"动力环境系统不正常\";\n      }\n\n      // 检查 jglist\n      this.jglist.forEach(item => {\n        if (\n          item.temperature === \"2\" ||\n          item.humidity === \"2\" ||\n          item.cleanliness === \"2\"\n        ) {\n          abnormalItems.jglist.push(item);\n        }\n        // if () {\n        //   abnormalItems.jglist.push(item);\n        // }\n        // if () {\n        //   abnormalItems.jglist.push(item);\n        // }\n      });\n\n      // 检查 sblist\n      this.sblist.forEach(item => {\n        if (\n          item.equipmentOnline === \"2\" ||\n          item.equipmentPower === \"2\" ||\n          item.equipmentNetwork === \"2\" ||\n          item.equipmentRunningSound === \"2\" ||\n          item.equipmentWarm === \"2\" ||\n          item.equipmentLoosen === \"2\" ||\n          item.equipmentDowntime === \"2\" ||\n          item.equipmentHardDisk === \"2\" ||\n          item.equipmentOhticalModule === \"2\" ||\n          item.equipmentPowerModule === \"2\" ||\n          item.equipmentFan === \"2\" ||\n          item.equipmentTemperature === \"2\" ||\n          item.equipmentPort === \"2\" ||\n          item.equipmentOpticalFiber === \"2\" ||\n          item.equipmentNetworkCable === \"2\" ||\n          item.equipmentLabel === \"2\"\n        ) {\n          abnormalItems.sblist.push(item);\n        }\n        // 其他检查...\n      });\n\n      // 检查 lxlist\n      this.lxlist.forEach(item => {\n        if (\n          item.cableDamaged === \"2\" ||\n          item.cableRibbon === \"2\" ||\n          item.cableJoint === \"2\" ||\n          item.cableLabel === \"2\"\n        ) {\n          abnormalItems.lxlist.push(item);\n        }\n      });\n\n      return abnormalItems;\n    }\n  },\n  // computed: {\n  //   hasAbnormal() {\n  //     // 检查 form 中的状态\n  //     const formAbnormal =\n  //       this.form.temperature === \"0\" ||\n  //       this.form.humidity === \"0\" ||\n  //       this.form.cleanliness === \"0\" ||\n  //       this.form.airConditioning === \"0\" ||\n  //       this.form.powerEnvironmentSystem === \"0\";\n\n  //     // 检查 jglist\n  //     const checkJGList = this.jglist.some(item => item.temperature === \"0\" || item.humidity === \"0\" || item.cleanliness === \"0\");\n\n  //     // 检查 sblist\n  //     const checkSBList = this.sblist.some(item =>\n  //       item.equipmentOnline === \"0\" ||\n  //       item.sbsfyjg === \"0\" ||\n  //       item.equipmentLoosen === \"0\" ||\n  //       item.equipmentDowntime === \"0\" ||\n  //       item.equipmentHardDisk === \"0\" ||\n  //       item.equipmentOhticalModule === \"0\" ||\n  //       item.equipmentPowerModule === \"0\" ||\n  //       item.equipmentFan === \"0\" ||\n  //       item.equipmentTemperature === \"0\" ||\n  //       item.equipmentPort === \"0\" ||\n  //       item.equipmentOpticalFiber === \"0\" ||\n  //       item.equipmentNetworkCable === \"0\" ||\n  //       item.equipmentLabel === \"0\"\n  //     );\n\n  //     // 检查 lxlist\n  //     const checkLXList = this.lxlist.some(item =>\n  //       item.lxywpsdl === \"0\" ||\n  //       item.lxjdsfzc === \"0\" ||\n  //       item.lxjtsfysd === \"0\" ||\n  //       item.lxbqsfzc === \"0\"\n  //     );\n\n  //     // 返回结果\n  //     return formAbnormal || checkJGList || checkSBList || checkLXList;\n  //   }\n  // },\n  //监控data中的数据变化\n  watch: {},\n  //方法集合\n  methods: {\n    handleCurrentChange(val) {\n      this.page = val;\n    },\n    handleSizeChange(val) {\n      this.pageSize = val;\n    },\n    tableCellStyle() {\n      return \"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;\";\n    },\n    tableHeaderCellStyle() {\n      return \"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;\";\n    },\n    async init() {\n      // alert(666)\n      // this.queryCabinetByCondition();\n      let data = await getInit();\n      if (data.code == 10000) {\n        if (data.data.temperature) {\n          console.log(1111);\n\n          this.form = data.data;\n        } else {\n          console.log(2222);\n\n          this.form.temperature = \"1\";\n          this.form.humidity = \"1\";\n          this.form.cleanliness = \"1\";\n          this.form.airConditioning = \"1\";\n          this.form.powerEnvironmentSystem = \"1\";\n        }\n        // this.$message.success(\"提交成功\");\n      } else {\n        // this.$message.error(\"提交失败\" + data.message);\n      }\n    },\n    async xyb() {\n      console.log(this.abnormalData);\n\n      if (this.active == 1) {\n        this.form.scanCode = this.$route.query.scanCode;\n        let data = await saveInspectionComputerRoom(this.form);\n        if (data.code == 10000) {\n          this.form.id = data.data;\n          this.queryCabinetByCondition();\n          this.queryEquipmentByCondition();\n          this.queryEquipmentByConditionlx();\n          // this.$message.success(\"提交成功\" + this.form.id);\n        } else {\n          // this.$message.error(\"提交失败\" + data.message);\n        }\n      }\n      if (this.active == 2) {\n        this.abnormalData.jglist.map(item => {\n          item.computerRoomInspectionId = this.form.id;\n        });\n        saveInspectionCabinet({\n          scanCode: this.$route.query.scanCode,\n          inspectionCabinetList: this.abnormalData.jglist,\n          computerRoomInspectionId: this.form.id\n        }).then(res => {});\n      }\n      if (this.active == 3) {\n        this.abnormalData.sblist.map(item => {\n          item.computerRoomInspectionId = this.form.id;\n        });\n        saveInspectionEquipment({\n          scanCode: this.$route.query.scanCode,\n          inspectionEquipmentList: this.abnormalData.sblist,\n          computerRoomInspectionId: this.form.id\n        }).then(res => {});\n      }\n      if (this.active == 4) {\n        this.abnormalData.lxlist.map(item => {\n          item.computerRoomInspectionId = this.form.id;\n        });\n        saveInspectionEquipment({\n          scanCode: this.$route.query.scanCode,\n          inspectionEquipmentList: this.abnormalData.lxlist,\n          computerRoomInspectionId: this.form.id\n        }).then(res => {});\n        this.$router.push({\n          path: \"/xjlclb\",\n          query: {\n            id: this.form.id,\n            scanCode: this.$route.query.scanCode\n          }\n        });\n      } else {\n        this.active++;\n      }\n    },\n    async save() {\n      saveInspectionEquipment({\n        inspectionEquipmentList: this.abnormalData.lxlist,\n        computerRoomInspectionId: this.form.id,\n        scanCode: this.$route.query.scanCode\n      }).then(res => {\n        if (res.code == 10000) {\n          this.$message({\n            message: \"提交成功\",\n            type: \"success\"\n          });\n        }\n      });\n      exportInspectionForm({\n        computerRoomInspectionId: this.form.id\n      }).then(res => {\n        this.dom_download(res, \"机房巡检信息\" + \".xls\");\n      });\n      // this.abnormalData.lxlist.map(item => {\n      //     item.computerRoomInspectionId = this.form.id;\n      //   });\n      //   saveInspectionEquipment({\n      //     inspectionCabinetList: this.abnormalData.lxlist,\n      //     computerRoomInspectionId: this.form.id\n      //   }).then(res => {});\n    },\n    //处理下载流\n    dom_download(content, fileName) {\n      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\n      //console.log(blob)\n      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象\n      let dom = document.createElement(\"a\"); //设置一个隐藏的a标签，href为输出流，设置download\n      console.log(\"dom\", dom);\n      dom.style.display = \"none\";\n      dom.href = url;\n      dom.setAttribute(\"download\", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\n      document.body.appendChild(dom);\n      dom.click();\n    },\n    async queryCabinetByCondition() {\n      queryCabinetByCondition({\n        cabinetCode: this.form.jgbh,\n        cabinetName: this.form.jgmc,\n        computerRoomInspectionId: this.form.id\n      }).then(res => {\n        console.log(res);\n        if (res.code == 10000) {\n          this.jglist = res.data;\n        }\n      });\n    },\n    //设备查询\n    async queryEquipmentByCondition() {\n      queryEquipmentByCondition({\n        equipmentMainType: \"1\",\n        cabinetCode: this.form.jgbh,\n        cabinetName: this.form.jgmc,\n        equipmentCode: this.form.sbbh,\n        equipmentName: this.form.sbmc,\n        computerRoomInspectionId: this.form.id\n      }).then(res => {\n        if (res.code == 10000) {\n          this.sblist = res.data;\n        }\n      });\n    },\n    //缆线查询\n    async queryEquipmentByConditionlx() {\n      queryEquipmentByCondition({\n        equipmentMainType: \"2\",\n        // cabinetCode: this.form.jgbh,\n        // cabinetName: this.form.jgmc,\n        // equipmentCode: this.form.sbbh,\n        // equipmentName: this.form.sbmc,\n        computerRoomInspectionId: this.form.id\n      }).then(res => {\n        // console.log(res);\n        if (res.code == 10000) {\n          this.lxlist = res.data;\n        }\n      });\n    },\n    syb() {\n      this.active--;\n    }\n  },\n  //生命周期 - 创建完成（可以访问当前this实例）\n  created() {},\n  //生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.init();\n  },\n  //生命周期 - 创建之前\n  beforeCreate() {},\n  //生命周期 - 挂载之前\n  beforeMount() {},\n  //生命周期 - 更新之前\n  beforeUpdate() {},\n  //生命周期 - 更新之后\n  updated() {},\n  //生命周期 - 销毁之前\n  beforeDestroy() {},\n  //生命周期 - 销毁完成\n  destroyed() {},\n  //如果页面有keep-alive缓存功能，这个函数会触发\n  activated() {}\n};\n</script>\n<style scoped>\n.box {\n  width: 1580px;\n  margin: 0 auto;\n}\n.top-box {\n  width: 100%;\n  display: flex;\n  border-bottom: 1px solid #e5e5e5;\n  margin-top: 20px;\n}\n.top-title {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n  margin-left: 10px;\n}\n.bt-title {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n.label-title {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 16px;\n  color: #080808;\n  font-weight: 400;\n}\n.buttonw {\n  /* width: 72px;\n  height: 32px; */\n  padding: 0px 20px;\n  text-align: center;\n  line-height: 32px;\n  color: #fff;\n  border-radius: 4px;\n  cursor: pointer;\n}\n.btnc {\n  background-color: #3ecbfe;\n  margin-right: 20px;\n}\n.btnc1 {\n  background-color: #3e9efe;\n}\n.btnc2 {\n  background-color: #20bdd1;\n  margin-left: 20px;\n}\n.tszt {\n  font-family: KaiTi;\n  font-weight: 700;\n}\n.cxbtn {\n  width: 72px;\n  height: 32px;\n  background: #3e9efe;\n  border-radius: 2px;\n  font-family: PingFangSC-Regular;\n  font-size: 14px;\n  color: #ffffff;\n  letter-spacing: 0.07px;\n  font-weight: 400;\n  text-align: center;\n  line-height: 32px;\n  margin-left: 20px;\n  margin-top: 14px;\n}\n.smzt {\n  font-size: 20px;\n}\n/deep/ .el-step__icon {\n  width: 36px;\n  height: 36px;\n}\n/deep/ .el-step.is-horizontal .el-step__line {\n  top: 18px;\n  left: 45px;\n  right: 12px;\n}\n/deep/ .el-step__head.is-process {\n  color: #fff;\n  border-color: #0077ff;\n}\n/deep/ .el-step__head.is-wait {\n  color: #fff;\n  border-color: #0077ff;\n}\n/deep/ .el-step__title.is-wait {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n/deep/ .el-step__title.is-process {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n/deep/ .el-step__icon.is-text {\n  border: 2px solid;\n  border-color: #0077ff;\n}\n/deep/ .el-step__head.is-success .is-text {\n  background-color: #0077ff;\n}\n/deep/ .el-step__head.is-success {\n  color: #fff;\n  border-color: #0077ff;\n}\n/deep/ .el-step__title.is-success {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n.inputcss /deep/ .el-input__inner {\n  width: 210px !important;\n  height: 32px;\n  border-radius: 4px;\n}\n.inputcss /deep/ .el-form--label-left .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 16px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-radio__inner {\n  width: 20px;\n  height: 20px;\n  border-radius: 2px !important;\n}\n\n/deep/ .el-radio__input.is-checked .el-radio__inner::after {\n  content: \"\";\n  width: 8px;\n  height: 3px;\n  border: 1px solid #0077ff;\n  border-top: transparent;\n  border-right: transparent;\n  text-align: center;\n  display: block;\n  position: absolute;\n  top: 5px;\n  left: 4px;\n  transform: rotate(-45deg);\n  border-radius: 0px;\n  background: none;\n}\n/deep/ .el-radio__input.is-checked .el-radio__inner {\n  background: #fff;\n}\n/deep/ .el-form-item {\n  margin: 10px 0;\n}\n.values {\n  font-family: SourceHanSansSC-Normal;\n  font-size: 14px;\n  color: #333333;\n  font-weight: 400;\n  margin-left: 24px;\n}\n.labels {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 16px;\n  color: #080808;\n  font-weight: 400;\n}\n.fbtbox {\n  display: flex;\n  align-items: center;\n  width: 350px;\n}\n/deep/\n  .el-table--enable-row-hover\n  .el-table__body\n  tr:hover:nth-child(even)\n  > td {\n  background-color: #dce8fb !important;\n}\n/deep/\n  .el-table--enable-row-hover\n  .el-table__body\n  tr:hover:nth-child(odd)\n  > td {\n  background-color: #dce8fb !important;\n}\n/deep/ .el-table__body tr:nth-child(even) {\n  background-color: #dce8fb; /* 偶数行（斑马线）的默认背景色 */\n}\n/deep/ .el-form--label-left .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-radio__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n.sb-box3 /deep/ .el-form-item {\n  border: 1px solid #c0c4cc;\n  padding: 10px;\n}\n\n\n</style>\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/ztqk/xjlc.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"box\"},[_vm._m(0),_vm._v(\" \"),_c('div',{staticStyle:{\"width\":\"70%\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('el-steps',{attrs:{\"active\":_vm.active,\"finish-status\":\"success\"}},[_c('el-step',{attrs:{\"title\":\"1.机房环境\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"2.机柜\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"3.设备\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"4.缆线\"}})],1)],1),_vm._v(\" \"),(_vm.active === 1)?_c('div',{staticStyle:{\"width\":\"640px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        1.机房环境\\n      \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"center\",\"margin-top\":\"20px\"}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"label-width\":\"260px\",\"label-position\":\"left\"}},[_c('el-form-item',{staticStyle:{\"position\":\"relative\"},attrs:{\"label\":\"机房温度是否正常\"}},[_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n                  传感器自动获取\\n                \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"left\":\"-90px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})]),_vm._v(\" \"),_c('el-radio-group',{model:{value:(_vm.form.temperature),callback:function ($$v) {_vm.$set(_vm.form, \"temperature\", $$v)},expression:\"form.temperature\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"position\":\"relative\"},attrs:{\"label\":\"机房湿度是否正常\"}},[_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n                  传感器自动获取\\n                \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"top\":\"14px\",\"left\":\"-90px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})]),_vm._v(\" \"),_c('el-radio-group',{model:{value:(_vm.form.humidity),callback:function ($$v) {_vm.$set(_vm.form, \"humidity\", $$v)},expression:\"form.humidity\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机房清洁度是否正常\"}},[_c('el-radio-group',{model:{value:(_vm.form.cleanliness),callback:function ($$v) {_vm.$set(_vm.form, \"cleanliness\", $$v)},expression:\"form.cleanliness\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机房空调是否正常\"}},[_c('el-radio-group',{model:{value:(_vm.form.airConditioning),callback:function ($$v) {_vm.$set(_vm.form, \"airConditioning\", $$v)},expression:\"form.airConditioning\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机房动力环境系统是否正常\"}},[_c('el-radio-group',{model:{value:(_vm.form.powerEnvironmentSystem),callback:function ($$v) {_vm.$set(_vm.form, \"powerEnvironmentSystem\", $$v)},expression:\"form.powerEnvironmentSystem\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1)],1)]):_vm._e(),_vm._v(\" \"),(_vm.active === 2)?_c('div',{staticStyle:{\"width\":\"720px\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        2.机柜\\n      \")]),_vm._v(\" \"),_c('el-form',{ref:\"form\",staticClass:\"inputcss\",staticStyle:{\"margin-top\":\"20px\"},attrs:{\"model\":_vm.form,\"label-width\":\"100px\",\"label-position\":\"left\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{attrs:{\"label\":\"机柜名称\"}},[_c('el-input',{model:{value:(_vm.form.jgmc),callback:function ($$v) {_vm.$set(_vm.form, \"jgmc\", $$v)},expression:\"form.jgmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"margin-left\":\"20px\"},attrs:{\"label\":\"机柜编号\"}},[_c('el-input',{model:{value:(_vm.form.jgbh),callback:function ($$v) {_vm.$set(_vm.form, \"jgbh\", $$v)},expression:\"form.jgbh\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"cxbtn\",on:{\"click\":_vm.queryCabinetByCondition}},[_vm._v(\"查询\")])],1)]),_vm._v(\" \"),_c('div',{staticStyle:{\"height\":\"270px\",\"overflow-y\":\"auto\"}},_vm._l((_vm.jglist),function(item){return _c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"margin-top\":\"20px\",\"border-bottom\":\"1px solid rgba(225,225,225,1)\"}},[_c('div',{staticStyle:{\"font-family\":\"SourceHanSansSC-Bold\",\"font-size\":\"20px\",\"color\":\"#003396\",\"font-weight\":\"700\",\"margin-right\":\"42px\",\"width\":\"120px\"}},[_vm._v(\"\\n            \"+_vm._s(item.cabinetName)+\"\\n          \")]),_vm._v(\" \"),_c('div',[_c('el-form',{ref:\"form\",refInFor:true,attrs:{\"model\":_vm.form,\"label-width\":\"220px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":\"机柜门是否常闭\"}},[_c('el-radio-group',{model:{value:(item.temperature),callback:function ($$v) {_vm.$set(item, \"temperature\", $$v)},expression:\"item.temperature\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机柜电力是否正常\"}},[_c('el-radio-group',{model:{value:(item.humidity),callback:function ($$v) {_vm.$set(item, \"humidity\", $$v)},expression:\"item.humidity\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机柜内是否有灰尘\"}},[_c('el-radio-group',{model:{value:(item.cleanliness),callback:function ($$v) {_vm.$set(item, \"cleanliness\", $$v)},expression:\"item.cleanliness\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1)],1)])}),0)],1):_vm._e(),_vm._v(\" \"),(_vm.active === 3)?_c('div',{staticStyle:{\"width\":\"1600px\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        3.设备\\n      \")]),_vm._v(\" \"),_c('el-form',{ref:\"form\",staticClass:\"inputcss\",staticStyle:{\"margin-top\":\"20px\"},attrs:{\"model\":_vm.form,\"label-width\":\"100px\",\"label-position\":\"left\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{attrs:{\"label\":\"机柜名称\"}},[_c('el-input',{model:{value:(_vm.form.jgmc),callback:function ($$v) {_vm.$set(_vm.form, \"jgmc\", $$v)},expression:\"form.jgmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"margin-left\":\"20px\"},attrs:{\"label\":\"机柜编号\"}},[_c('el-input',{model:{value:(_vm.form.jgbh),callback:function ($$v) {_vm.$set(_vm.form, \"jgbh\", $$v)},expression:\"form.jgbh\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"margin-left\":\"20px\"},attrs:{\"label\":\"设备名称\"}},[_c('el-input',{model:{value:(_vm.form.sbmc),callback:function ($$v) {_vm.$set(_vm.form, \"sbmc\", $$v)},expression:\"form.sbmc\"}})],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"margin-left\":\"20px\"},attrs:{\"label\":\"设备编号\"}},[_c('el-input',{model:{value:(_vm.form.sbbh),callback:function ($$v) {_vm.$set(_vm.form, \"sbbh\", $$v)},expression:\"form.sbbh\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"cxbtn\",on:{\"click\":_vm.queryEquipmentByCondition}},[_vm._v(\"查询\")])],1)]),_vm._v(\" \"),_c('div',{staticStyle:{\"height\":\"386px\",\"overflow-y\":\"auto\"}},_vm._l((_vm.sblist),function(item){return _c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"margin-top\":\"20px\",\"border\":\"1px solid #c0c4cc\",\"padding-left\":\"20px\"}},[_c('div',{staticStyle:{\"font-family\":\"SourceHanSansSC-Bold\",\"font-size\":\"20px\",\"color\":\"#003396\",\"font-weight\":\"700\",\"margin-right\":\"42px\",\"width\":\"180px\",\"text-align\":\"center\"}},[_vm._v(\"\\n            \"+_vm._s(item.equipmentName)+\"\\n          \")]),_vm._v(\" \"),_c('div',{staticClass:\"sb-box3\"},[_c('el-form',{ref:\"form\",refInFor:true,attrs:{\"model\":_vm.form,\"label-width\":\"220px\",\"label-position\":\"left\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\",\"width\":\"1330px\"}},[_c('el-form-item',{attrs:{\"label\":\"设备是否在线\"}},[_c('el-radio-group',{model:{value:(item.equipmentOnline),callback:function ($$v) {_vm.$set(item, \"equipmentOnline\", $$v)},expression:\"item.equipmentOnline\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备电力是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentPower),callback:function ($$v) {_vm.$set(item, \"equipmentPower\", $$v)},expression:\"item.equipmentPower\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备运行声音是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentRunningSound),callback:function ($$v) {_vm.$set(item, \"equipmentRunningSound\", $$v)},expression:\"item.equipmentRunningSound\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\",\"width\":\"1330px\"}},[_c('el-form-item',{attrs:{\"label\":\"设备是否有告警\"}},[_c('el-radio-group',{model:{value:(item.equipmentWarm),callback:function ($$v) {_vm.$set(item, \"equipmentWarm\", $$v)},expression:\"item.equipmentWarm\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备是否有松动\"}},[_c('el-radio-group',{model:{value:(item.equipmentLoosen),callback:function ($$v) {_vm.$set(item, \"equipmentLoosen\", $$v)},expression:\"item.equipmentLoosen\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备是否有宕机\"}},[_c('el-radio-group',{model:{value:(item.equipmentDowntime),callback:function ($$v) {_vm.$set(item, \"equipmentDowntime\", $$v)},expression:\"item.equipmentDowntime\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\",\"width\":\"1330px\"}},[_c('el-form-item',{attrs:{\"label\":\"服务器硬盘是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentHardDisk),callback:function ($$v) {_vm.$set(item, \"equipmentHardDisk\", $$v)},expression:\"item.equipmentHardDisk\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备光模块是否齐全\"}},[_c('el-radio-group',{model:{value:(item.equipmentOhticalModule),callback:function ($$v) {_vm.$set(item, \"equipmentOhticalModule\", $$v)},expression:\"item.equipmentOhticalModule\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备电源模块是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentPowerModule),callback:function ($$v) {_vm.$set(item, \"equipmentPowerModule\", $$v)},expression:\"item.equipmentPowerModule\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"1330px\",\"justify-content\":\"space-between\"}},[_c('el-form-item',{attrs:{\"label\":\"设备风扇是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentFan),callback:function ($$v) {_vm.$set(item, \"equipmentFan\", $$v)},expression:\"item.equipmentFan\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备温度是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentTemperature),callback:function ($$v) {_vm.$set(item, \"equipmentTemperature\", $$v)},expression:\"item.equipmentTemperature\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备端口是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentPort),callback:function ($$v) {_vm.$set(item, \"equipmentPort\", $$v)},expression:\"item.equipmentPort\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"1330px\",\"justify-content\":\"space-between\"}},[_c('el-form-item',{attrs:{\"label\":\"设备光纤是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentOpticalFiber),callback:function ($$v) {_vm.$set(item, \"equipmentOpticalFiber\", $$v)},expression:\"item.equipmentOpticalFiber\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"margin-left\":\"17px\"},attrs:{\"label\":\"设备网线是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentNetworkCable),callback:function ($$v) {_vm.$set(item, \"equipmentNetworkCable\", $$v)},expression:\"item.equipmentNetworkCable\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{staticStyle:{\"margin-left\":\"17px\"},attrs:{\"label\":\"设备标签是否正常\"}},[_c('el-radio-group',{model:{value:(item.equipmentLabel),callback:function ($$v) {_vm.$set(item, \"equipmentLabel\", $$v)},expression:\"item.equipmentLabel\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1)])],1)])}),0)],1):_vm._e(),_vm._v(\" \"),(_vm.active === 4)?_c('div',{staticStyle:{\"width\":\"720px\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        4.缆线\\n      \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"height\":\"410px\",\"overflow-y\":\"auto\"}},_vm._l((_vm.lxlist),function(item){return _c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"margin-top\":\"20px\",\"border-bottom\":\"1px solid rgba(225,225,225,1)\"}},[_c('div',{staticStyle:{\"font-family\":\"SourceHanSansSC-Bold\",\"font-size\":\"20px\",\"color\":\"#003396\",\"font-weight\":\"700\",\"margin-right\":\"42px\"}},[_vm._v(\"\\n            \"+_vm._s(item.equipmentName)+\"\\n          \")]),_vm._v(\" \"),_c('div',[_c('el-form',{ref:\"form\",refInFor:true,attrs:{\"model\":_vm.form,\"label-width\":\"220px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":\"线缆有无破损断裂\"}},[_c('el-radio-group',{model:{value:(item.cableDamaged),callback:function ($$v) {_vm.$set(item, \"cableDamaged\", $$v)},expression:\"item.cableDamaged\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"线缆扎带是否正常\"}},[_c('el-radio-group',{model:{value:(item.cableRibbon),callback:function ($$v) {_vm.$set(item, \"cableRibbon\", $$v)},expression:\"item.cableRibbon\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"线缆接头是否有松动\"}},[_c('el-radio-group',{model:{value:(item.cableJoint),callback:function ($$v) {_vm.$set(item, \"cableJoint\", $$v)},expression:\"item.cableJoint\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"线缆标签是否正常\"}},[_c('el-radio-group',{model:{value:(item.cableLabel),callback:function ($$v) {_vm.$set(item, \"cableLabel\", $$v)},expression:\"item.cableLabel\"}},_vm._l((_vm.sflist),function(item){return _c('el-radio',{key:item.label,attrs:{\"label\":item.label}},[_vm._v(_vm._s(item.value))])}),1)],1)],1)],1)])}),0)]):_vm._e(),_vm._v(\" \"),(_vm.active === 5)?_c('div',{staticStyle:{\"width\":\"1120px\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        巡检异常情况明细表\\n      \")]),_vm._v(\" \"),_vm._m(1),_vm._v(\" \"),_c('div',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"20px\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData,\"header-cell-style\":_vm.tableHeaderCellStyle,\"cell-style\":_vm.tableCellStyle,\"max-height\":\"800px\"}},[_c('el-table-column',{attrs:{\"prop\":\"sbmc\",\"label\":\"设备名称\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xh\",\"label\":\"型号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"jgwz\",\"label\":\"机柜位置\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"xlh\",\"label\":\"序列号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"gzqkms\",\"label\":\"故障情况描述\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"clff\",\"label\":\"处理方法\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"hfsj\",\"label\":\"回复时间\",\"align\":\"center\"}})],1),_vm._v(\" \"),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('el-pagination',{attrs:{\"background\":\"\",\"pager-count\":5,\"current-pageNo\":_vm.page,\"pageNo-sizes\":[5, 10, 20, 30],\"pageNo-size\":_vm.pageSize,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}})],1)],1)]):_vm._e(),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"500px\",\"justify-content\":\"center\",\"margin\":\"0 auto\",\"margin-top\":\"60px\"}},[(_vm.active != 1 && _vm.active != 5)?_c('div',{staticClass:\"buttonw btnc\",on:{\"click\":_vm.syb}},[_vm._v(\"\\n        上一步\\n      \")]):_vm._e(),_vm._v(\" \"),(_vm.active != 4 || _vm.abnormalData.sblist.length)?_c('div',{staticClass:\"buttonw btnc1\",on:{\"click\":_vm.xyb}},[_vm._v(\"\\n        下一步\\n      \")]):_vm._e(),_vm._v(\" \"),(_vm.active == 4 && !_vm.abnormalData.sblist.length)?_c('div',{staticClass:\"buttonw btnc1\",on:{\"click\":_vm.save}},[_vm._v(\"\\n        提交并导出\\n      \")]):_vm._e()])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"top-box\"},[_c('div',[_c('img',{attrs:{\"src\":require(\"./img/title.png\"),\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"top-title\"},[_vm._v(\"巡检流程\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\",\"margin-top\":\"30px\"}},[_c('div',{staticClass:\"fbtbox\"},[_c('div',{staticClass:\"labels\"},[_vm._v(\"巡检机房名称\")]),_vm._v(\" \"),_c('div',{staticClass:\"values\"},[_vm._v(\"NPJ122000111000000\")])]),_vm._v(\" \"),_c('div',{staticClass:\"fbtbox\"},[_c('div',{staticClass:\"labels\"},[_vm._v(\"巡检时间\")]),_vm._v(\" \"),_c('div',{staticClass:\"values\"},[_vm._v(\"2024-10-12 11：00：00\")])]),_vm._v(\" \"),_c('div',{staticClass:\"fbtbox\"},[_c('div',{staticClass:\"labels\"},[_vm._v(\"巡检人员\")]),_vm._v(\" \"),_c('div',{staticClass:\"values\"},[_vm._v(\"张三\")])])])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-127d3c42\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/ztqk/xjlc.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-127d3c42\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./xjlc.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./xjlc.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./xjlc.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-127d3c42\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./xjlc.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-127d3c42\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/xjlc.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}