import {createAPI, createFileAPI, createUploadAPI,createDownloadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

//涉密岗位
export const getAllGwxx = data => createAPI(BASE_URL+"/rygl/gwdj/getAllGwxx", 'get',data)
//涉密人员查看无分页
export const getAllYhxx = data => createAPI(BASE_URL+"/rygl/yhxx/getAllYhxx", 'get',data)
//离职离岗
export const getAllLzlg = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLzlg/getAllLzlg", 'get',data)
//查询全部Dm责任人
export const getAllDmzrr = data => createAPI(BASE_URL+"/dmgl/dmzrr/getAllDmzrr", 'get',data)
//查询全部Dm授权
export const getAllDmsq = data => createAPI(BASE_URL+"/dmgl/dmsq/getAllDmsq", 'get',data)
//查询全部国家mm事项
export const getAllGjmmsx = data => createAPI(BASE_URL+"/dmgl/gjmmsx/getAllGjmmsx", 'get',data)
//查询全部dm培训
export const getAllDmpx = data => createAPI(BASE_URL+"/dmgl/dmpx/getAllDmpx", 'get',data)
//查询全部Dm情况年度统计
export const getAllNdtj = data => createAPI(BASE_URL+"/dmgl/ndtj/getAllNdtj", 'get',data)
//不明确事项确定情况
export const getAllBmqsxqdqk = data => createAPI(BASE_URL+"/dmgl/bmqsxqdqk/getAllBmqsxqdqk", 'get',data)
//查询全部zf采购项目情况
export const getAllSmzfcgxmqk = data => createAPI(BASE_URL+"/dmgl/smzfcgxmqk/getAllSmzfcgxmqk", 'get',data)
export const getAllBmzd = data => createAPI(BASE_URL+"/bmzt/djb/getAllBmzd", 'get',data)

//自选
export const exportZxData = data => createDownloadAPI(BASE_URL+"/bmzt/djb/exportZxData", 'post',data)
//本年
export const exportDmsxData = data => createDownloadAPI(BASE_URL+"/bmzt/djb/exportDmsxData", 'get',data)


//自选
export const exportZxDwRyData = data => createDownloadAPI(BASE_URL+"/rygl/yhxx/exportZxDwRyData", 'post',data)
//本年
export const exportYhxxData = data => createDownloadAPI(BASE_URL+"/rygl/yhxx/exportYhxxData", 'get',data)
