const key = 'login-user'
// 自查自评专用key，用以存储自查自评任务ID，单位ID，抽查的内设机构ID等id，目的是为了让tag页切回来的时候数据能够正常回显
const zczpIdskey = 'zczp-ids-key'

// 设置本地数据
export const setWindowLocation = (data) => {
  window.localStorage.setItem(key, JSON.stringify(data))
}

// 获取本地数据
export const getWindowLocation = () => {
  return JSON.parse(window.localStorage.getItem(key))
}

// 删除本地数据
export const removeWindowLocation = () => {
  return window.localStorage.removeItem(key)
}

/**
 * 自查自评专用
 */
// 修改自查自评对象
export const setZczpIdsObj = (field, value) => {
  let zczpObj = JSON.parse(window.localStorage.getItem(zczpIdskey))
  // 判空，空则初始化为空对象
  if (!zczpObj) {
    zczpObj = {}
  }
  zczpObj[field] = value
  window.localStorage.setItem(zczpIdskey, JSON.stringify(zczpObj))
}
// 初始化自查自评
export const initZczpIdsObj = () => {
  return window.localStorage.setItem(zczpIdskey, JSON.stringify({}))
}
// 获取自查自评对象
export const getZczpIdsObj = () => {
  let resObj = window.localStorage.getItem(zczpIdskey)
  console.log(resObj);
  if(!resObj) {
    resObj = '{}'
  }
  resObj = JSON.parse(resObj)
  if (!resObj) {
    resObj = {}
  }
  return resObj
}
// 移除自查自评对象字段
export const removeZczpIdsObjField = (field) => {
  let resObj = window.localStorage.getItem(zczpIdskey)
  if(!resObj) {
    resObj = '{}'
  }
  resObj = JSON.parse(resObj)
  if (!resObj) {
    resObj = {}
  }
  resObj[field] = undefined
  window.localStorage.setItem(zczpIdskey, JSON.stringify(resObj))
}
