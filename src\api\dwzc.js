import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//单位信息注册
export const registerDwxx = data => createAPI(BASE_URL+"/dwxx/registerDwxx", 'post',data)
//获取单位类型
export const getAllDwlx = data => createAPI(BASE_URL+"/dmb/csmx/getAllDwlx", 'get',data)
//获取单位级别
export const getAllDwjb = data => createAPI(BASE_URL+"/dmb/csmx/getAllDwjb", 'get',data)
//获取所属领域
export const getAllSsly = data => createAPI(BASE_URL+"/dmb/csmx/getAllSsly", 'get',data)
//获取所属层级
export const getAllSscj = data => createAPI(BASE_URL+"/dmb/csmx/getAllSscj", 'get',data)
//获取全部省数据
export const getProvinceList = data => createAPI(BASE_URL+"/province/getProvinceList", 'get',data)
//根据省code获取市
export const getCityByProvincecode = data => createAPI(BASE_URL+"/city/getCityByProvincecode", 'get',data)
//根据市code获取区
export const getAreaByCitycode = data => createAPI(BASE_URL+"/area/getAreaByCitycode", 'get',data)
//判断单位是否已经注册
export const verifyRegiste = data => createAPI(BASE_URL+"/dwxx/verifyRegiste", 'get',data)
//用户登录
export const login = data => createAPI(BASE_URL+"/api/layout_03_01/login/login", 'post',data)
//获取当前登录用户的信息
export const getUserInfo = data => createAPI(BASE_URL+"/user/getUserInfo", 'get',data)

//修改密码时验证旧密码是否正确
export const verifyPassword = data => createAPI(BASE_URL+"/user/verifyPassword", 'get',data)
//修改密码和姓名
export const updateXmAndMm = data => createAPI(BASE_URL+"/user/updateXmAndMm", 'post',data)

//获取单位信息
export const getDwxx = data => createAPI(BASE_URL+"/dwxx/getDwxx", 'get',data)
//修改注册信息(注册信息维护)
export const updateRegisteInfo = data => createAPI(BASE_URL+"/dwxx/updateRegisteInfo", 'post',data)

// 重置密码
export const resetPassword = data => createAPI(BASE_URL+"/user/resetPassword", 'post',data)
//获取全部用户
export const getYhxxList = data => createAPI(BASE_URL+"/user/getYhxxList", 'get',data)

//获取上级单位
export const getDwxxList = data => createAPI(BASE_URL+"/dwxx/getDwxxList", 'get',data)
