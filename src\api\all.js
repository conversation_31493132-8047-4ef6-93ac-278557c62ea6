import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//全部涉密计算机
export const getAllSmjsj = data => createAPI(BASE_URL+"/sbgl/smjsj/getAllSmjsj", 'get',data)
//全部非涉密计算机
export const getAllFmjsj = data => createAPI(BASE_URL+"/sbgl/fmjsj/getAllFmjsj", 'get',data)
//全部涉密移动存储介质
export const getAllYdccjz = data => createAPI(BASE_URL+"/sbgl/ydccjz/getAllYdccjz", 'get',data)
//全部涉密办公自动化
export const getAllSmxxsb = data => createAPI(BASE_URL+"/sbgl/smxxsb/getAllSmxxsb", 'get',data)
//全部非涉密办公自动化
export const getAllFmxxsb = data => createAPI(BASE_URL+"/sbgl/fmxxsb/getAllFmxxsb", 'get',data)
//全部涉密网络设备
export const getAllSmwlsb = data => createAPI(BASE_URL+"/sbgl/smwlsb/getAllSmwlsb", 'get',data)
//全部非涉密网络设备
export const getAllFmwlsb = data => createAPI(BASE_URL+"/sbgl/fmwlsb/getAllFmwlsb", 'get',data)
//全部非涉密网络设备
export const getAllAqfhcp = data => createAPI(BASE_URL+"/sbgl/xxsb/getAllAqfhcp", 'get',data)
//全部载体信息
export const getAllZt = data => createAPI(BASE_URL+"/ztgl/zt/getAllZt", 'get',data)

