/* 公共 */
.fr {
    float: right;
}
.fl {
    float: left;
}
.clearBoth {
    clear: both;
}
.positionR {
    position: relative;
}
.positionA {
    position: absolute;
}
.overflowHidden {
    overflow: hidden;
}
.maxWidth {
    width: 100%;
}
.maxHeight {
    height: 100%;
}
/* //文字位置 */
.text-center{
  text-align: center;
}
.text-left{
  text-align: left;
}
.text-right{
  text-align: right;
}
/* //文字粗体 */
.text-bold{
  font-weight: bold;
}
/* //文字粗体 */
.text-normal{
  font-weight: normal;
}
/* //可见性 */
.hidden{
  display: none;
}
.invisible{
  visibility: hidden;
}
.opacity-0{
  opacity: 0;
}
.inline{
  display: inline;
}
.inline-block{
  display: inline-block;
}
.block{
  display: block;
}
.vertical-middle{
  vertical-align: middle !important;
}
.vertical-top{
  vertical-align: top;
}
/* //鼠标光标样式 */
.cursorClick{
  cursor: pointer;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
/* //滚动条
//隐藏滚动条 */
.no-scroll{
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
  overflow: hidden;
}
.no-scroll::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.overflow-auto{
  overflow: auto !important;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-hidden-y{
  overflow-y: hidden;
}
/* //文字省略 */
.text-ellipsis{
  overflow-x: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  white-space: nowrap;
}
.text-ellipsis-2{
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.text-ellipsis-3{
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
/* // 不换行显示 */
.nowrap{
  white-space: nowrap;
}
/* //旋转180度 */
.rotate180{
  transform: rotateX(180deg);
}