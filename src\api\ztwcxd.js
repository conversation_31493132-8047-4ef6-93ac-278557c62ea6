import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1.添加涉密载体-载体外出携带
export const addZtXdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/addZtXdwc", 'post',data)
//2.删除涉密载体-载体外出携带
export const removeZtXdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/removeZtXdwc", 'post',data)
//3.修改涉密载体-载体外出携带
export const updateZtXdwc = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/updateZtXdwc", 'post',data)
//4.分页查询涉密载体-载体外出携带
export const selectZtXdwcPage = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/selectZtXdwcPage", 'get',data)
//5.通过jlid查询涉密载体-载体外出携带
export const getZtXdwcByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/getZtXdwcByJlid", 'get',data)
//根据原jlid查询载体清单
export const getZtqdListByYjlid = data => createAPI(BASE_URL+"/ztgl/ztqd/getZtqdListByYjlid", 'get',data)
//6.通过slid查询涉密载体-载体外出携带
export const getZtXdwcBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/getZtXdwcBySlid", 'get',data)
//根据实例id查询记录id
export const getXdwcJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ztgl_xdwc/getXdwcJlidBySlid", 'get',data)
//根据jlid修改载体清单
export const updateZtqdByJlid = data => createAPI(BASE_URL+"/ztgl/ztqd/updateZtqdByJlid", 'post',data)
export const deleteZtqdByYjlid = data => createAPI(BASE_URL+"/ztgl/ztqd/deleteZtqdByYjlid", 'post',data)


export const selectZtglZtqdPage = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/selectZtglZtqdPage", 'get',data)

//1、添加载体携带外出登记
export const addZtXdwcDj = data => createAPI(BASE_URL+"/ztgl_xdwcdj/addZtXdwcDj", 'post',data)
//2.根据记录id查询携带外出登记
export const getZtXdwcDjByJlid = data => createAPI(BASE_URL+"/ztgl_xdwcdj/getZtXdwcDjByJlid", 'get',data)
//3.查询携带外出登记-分页
export const selectZtXdwcDJPage = data => createAPI(BASE_URL+"/ztgl_xdwcdj/selectZtXdwcPage", 'get',data)
//4.根据记录id删除携带外出登记
export const deleteZtXdwcDj = data => createAPI(BASE_URL+"/ztgl_xdwcdj/deleteZtXdwcDj", 'post',data)
//5.根据记录id修改携带外出登记
export const updateZtXdwcDj = data => createAPI(BASE_URL+"/ztgl_xdwcdj/updateZtXdwcDj", 'post',data)
//6.查询未归还载体
export const selectWghZt = data => createAPI(BASE_URL+"/ztgl_xdwcdj/selectWghZt", 'get',data)
//7.根据清单的yjlid查找slid修改登记表信息
export const updateByYjlid = data => createAPI(BASE_URL+"/ztgl_xdwcdj/updateByYjlid", 'post',data)
