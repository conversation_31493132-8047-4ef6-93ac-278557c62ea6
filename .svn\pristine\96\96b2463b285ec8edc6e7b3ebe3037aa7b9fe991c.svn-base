<!--  -->
<template>
  <div class="box">
    <div class="top-box">
      <div>
        <img src="./img/title.png" alt="" />
      </div>
      <div class="top-title">故障处理流程</div>
    </div>
    <div style="width: 70%;margin: 0 auto;margin-top: 50px;" v-if="form.replaceFlag == 1">
      <el-steps :active="active" finish-status="success">
        <el-step title="步骤一"></el-step>
        <el-step title="步骤二"></el-step>
        <el-step title="步骤三"></el-step>
        <el-step title="步骤四"></el-step>
        <el-step title="步骤五"></el-step>
        <el-step title="步骤六"></el-step>
        <el-step title="步骤七"></el-step>
      </el-steps>
    </div>
    <div style="width: 70%;margin: 0 auto;margin-top: 50px;" v-else>
      <el-steps :active="active" finish-status="success">
        <el-step title="步骤一"></el-step>
        <el-step title="步骤二"></el-step>
        <el-step title="步骤三"></el-step>
        <el-step title="步骤四"></el-step>
        <el-step title="步骤五"></el-step>
      </el-steps>
    </div>
    <div style="width: 580px;margin: 0 auto;margin-top: 100px;" v-if="active === 1">
      <div class="bt-title">
        项目经理向省分中心以及国家中心维护组报备
      </div>
      <div style="display: flex;margin-top: 20px;">
        <div class="label-title">
          报备组织：
        </div>
        <div style="margin-left: 10px;">
          <el-radio v-model="form.reportOrganiaztion" label="1">省中心</el-radio>
          <el-radio v-model="form.reportOrganiaztion" label="2">省移动</el-radio>
          <el-radio v-model="form.reportOrganiaztion" label="3">北京移动组</el-radio>
          <el-radio v-model="form.reportOrganiaztion" label="4">其它</el-radio>
        </div>
      </div>
    </div>
    <div style="width: 600px;margin: 0 auto;margin-top: 100px;" v-if="active === 2">
      <div class="bt-title" style="position: relative;">
        协调厂家工程师到现场处理故障，项目经理作全程旁站陪同。
        <el-popover placement="right" width="200" trigger="hover">
          <div>
            <div style="display:flex;margin-bottom:10px">
              <i class="el-icon-info" style="color:#409eef;    position: relative;
    top: 2px;"></i>
              <div class="tszt">提示</div>
            </div>
            <div class="smzt">
              项目经理去省移动公司，在思安设备上取得故障处理授权（二维码）。对于时限要求高的故障（2小时以内），直接去机房，现场录入故障处理授权。
            </div>
          </div>
          <i class="el-icon-info" style="color:#409eef;position: absolute;    right: 0px;top: 7px;"
            slot="reference"></i>
        </el-popover>
      </div>

      <div style="display: flex;margin-top: 20px;justify-content: center;">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="left" status-icon>
          <el-form-item label="厂家名称">
            <el-input v-model="form.manufacturerName"></el-input>
          </el-form-item>
          <el-form-item label="工程师姓名">
            <el-input v-model="form.engineerName"></el-input>
          </el-form-item>
          <el-form-item label="联系方式" prop="manufacturerPhone">
            <el-input v-model="form.manufacturerPhone"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div style="width: 718px;margin: 0 auto;margin-top: 100px;" v-if="active === 3">
      <div class="bt-title">
        携带配件到现场,现场处理故障。<br />判断是否需要更换新配件，如果不需要更换配件。<br />例如重启设备或者重新插拔线缆接头等。处理后，试运行。
      </div>
      <div style="display: flex;margin-top: 20px;">
        <div class="label-title">
          配件是否需要更换：
        </div>
        <div style="margin-left: 10px;">
          <el-radio v-model="form.replaceFlag" label="1">是</el-radio>
          <el-radio v-model="form.replaceFlag" label="2">否</el-radio>
        </div>
      </div>
    </div>
    <div style="width: 718px;margin: 0 auto;margin-top: 100px;" v-if="active === 4 && form.replaceFlag == 1">
      <div class="bt-title">
        记录故障备件序列号和新配件序列号，更换故障配件，新配件试运行。
      </div>
      <div style="display: flex;margin-top: 20px;justify-content: center;">
        <el-form ref="form" :model="form" label-width="160px" label-position="left">
          <el-form-item label="故障设备序列号">
            <el-input v-model="form.equipmentSerialNumber" disabled></el-input>
          </el-form-item>
          <el-form-item label="原配件序列号">
            <el-input v-model="form.equipmentSerialNumberOld"></el-input>
          </el-form-item>
          <el-form-item label="新配件序列号">
            <el-input v-model="form.equipmentSerialNumberNew"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div style="width: 718px;margin: 0 auto;margin-top: 100px;" v-if="
      (active === 5 && form.replaceFlag == 1) ||
      (active === 4 && form.replaceFlag == 2)
    ">
      <div class="bt-title" style="text-align: center;">
        国家中心维护组确认故障恢复，运行正常。
      </div>
    </div>
    <div style="width: 718px;margin: 0 auto;margin-top: 100px;" v-if="active === 6 && form.replaceFlag == 1">
      <div class="bt-title">
        厂家工程师将故障配件（除核心设备及配件）返厂。核心设备及配件带回省移动公司接口人处，进入待处理库，等待接口人的统一处理
      </div>
    </div>
    <div style="width: 718px;margin: 0 auto;margin-top: 100px;" v-if="
      (active === 7 && form.replaceFlag == 1) ||
      (active === 5 && form.replaceFlag == 2)
    ">
      <div class="bt-title" style="text-align: center;">
        故障处理单
      </div>
      <div style="display: flex;margin-top: 20px;justify-content: center;">
        <el-form ref="form" :model="form" label-width="180px" label-position="left">
          <el-form-item label="故障设备编号：">
            <el-input v-model="form.equipmentCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="故障原因：">
            <el-input v-model="form.reason"></el-input>
          </el-form-item>
          <el-form-item label="故障处理时间：">
            <el-input v-model="form.faultHandlingTime" disabled></el-input>
          </el-form-item>
          <el-form-item label="故障处理负责人：">
            <el-input v-model="form.createByName" disabled></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div style="display: flex;width: 500px;justify-content: center;
margin: 0 auto;margin-top: 60px;">
      <div class="buttonw btnc" @click="syb" v-if="active != 1">上一步</div>
      <div class="buttonw btnc1" @click="xyb" v-if="
        !(
          (active === 7 && form.replaceFlag == 1) ||
          (active === 5 && form.replaceFlag == 2)
        )
      ">
        下一步
      </div>
      <div class="buttonw btnc2" @click="dcbutton"
        v-if="(active === 7 && form.replaceFlag == 1) || (active === 5 && form.replaceFlag == 2)">
        打印故障处理单
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  saveFaultHandling,
  exportFaultHandling,
  getInitFaultHandling,
  exportDestructionEquipment, saveDestructionEquipment
} from "../../../api/jfxj";
export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      active: 1,
      form: {
        id: "",
        reportOrganiaztion: "4",
        manufacturerName: "",
        engineerName: "",
        manufacturerPhone: "",
        replaceFlag: "2",
        equipmentSerialNumber: this.$route.query.equipmentSerialNumber,
        equipmentSerialNumberNew: "",
        equipmentSerialNumberOld: "",
        maintenanceStatus: "",
        equipmentCode: this.$route.query.equipmentCode,
        reason: "",
        faultHandlingTime: "",
        // assignee: ""
        createByName: "",
      },
      rules: {
        manufacturerPhone: [
          // { required: true, message: "请输入联系方式", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码", trigger: "blur" }
        ]
      },
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    async init() {
      let data = await getInitFaultHandling(this.form);
      if (data.code == 10000) {
        // if (data.data.id) {
        //   this.form = data.data;
        // }
        this.form.createByName = data.data.createByName;
      }
    },
    async xyb() {
      this.form.maintenanceStatus = this.active;
      this.form.deleteFlag = 0;
      saveFaultHandling(this.form).then(res => {
        if (res.code == 10000) {
          this.form.id = res.data;
        }
      });
      this.active++;
    },
    // syb() {
    //   this.active--;
    // },
    syb() {
      this.form.maintenanceStatus = this.active;
      this.form.deleteFlag = 1;
      saveFaultHandling(this.form).then(res => {
        if (res.code == 10000) {
          this.form.id = res.data;
        }
      });
      this.active--;
    },

    async dcbutton() {
      this.form.maintenanceStatus = this.active;
      const saveResponse = await saveFaultHandling(this.form);
      // alert(JSON.stringify(saveResponse));
      if (saveResponse.code !== 10000) {
        this.$message.error("保存失败：" + saveResponse.message);
        return;
      }
      this.form.id = saveResponse.data;
      console.log("this.form.id", this.form.id)
      const exportResponse = await exportFaultHandling(this.form);
      console.log("exportResponse", exportResponse)
      if (exportResponse.code !== 10000) {
        this.$message.error(exportResponse.message);
        return;
      }
      // 导出接口返回的是文件流，直接处理下载
      this.$message.success("导出成功");
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = 'none'
      dom.href = url
      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom)
      dom.click()
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {

    this.init();
    // 创建一个新的Date对象，获取当前日期和时间
    var now = new Date();

    // 获取年、月、日
    var year = now.getFullYear();
    var month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需要加1，并且格式化为两位数
    var day = String(now.getDate()).padStart(2, "0"); // 格式化为两位数

    // 组合成年-月-日的格式
    var formattedDate = year + "-" + month + "-" + day;

    console.log("formattedDate", formattedDate);

    this.form.faultHandlingTime = formattedDate;
    this.form.equipmentCode = this.$route.query.equipmentCode
    this.form.equipmentSerialNumber = this.$route.query.equipmentSerialNumber
  },
  //生命周期 - 创建之前
  beforeCreate() { },
  //生命周期 - 挂载之前
  beforeMount() { },
  //生命周期 - 更新之前
  beforeUpdate() { },
  //生命周期 - 更新之后
  updated() { },
  //生命周期 - 销毁之前
  beforeDestroy() { },
  //生命周期 - 销毁完成
  destroyed() { },
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() { }
};
</script>
<style scoped>
.box {
  width: 1580px;
  margin: 0 auto;
}

.top-box {
  width: 100%;
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 20px;
}

.top-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
  margin-left: 10px;
}

.bt-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}

.label-title {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

.buttonw {
  /* width: 72px;
  height: 32px; */
  padding: 0px 20px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.btnc {
  background-color: #3ecbfe;
  margin-right: 20px;
}

.btnc1 {
  background-color: #3e9efe;
}

.btnc2 {
  background-color: #20bdd1;
  margin-left: 20px;
}

.tszt {
  font-family: KaiTi;
  font-weight: 700;
}

.smzt {
  font-size: 12px;
}

/deep/ .el-step__icon {
  width: 36px;
  height: 36px;
}

/deep/ .el-step.is-horizontal .el-step__line {
  top: 18px;
  left: 45px;
  right: 12px;
}

/deep/ .el-step__head.is-process {
  color: #fff;
  border-color: #0077ff;
}

/deep/ .el-step__head.is-wait {
  color: #fff;
  border-color: #0077ff;
}

/deep/ .el-step__title.is-wait {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}

/deep/ .el-step__title.is-process {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}

/deep/ .el-step__icon.is-text {
  border: 2px solid;
  border-color: #0077ff;
}

/deep/ .el-step__head.is-success .is-text {
  background-color: #0077ff;
}

/deep/ .el-step__head.is-success {
  color: #fff;
  border-color: #0077ff;
}

/deep/ .el-step__title.is-success {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}

/deep/ .el-input__inner {
  width: 350px !important;
  height: 32px;
  border-radius: 4px;
}

/deep/ .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

/deep/ .el-radio__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

/deep/ .el-color-picker__icon,
.el-input,
.el-textarea {
  font-size: 18px;
}
</style>
