import {
  createAPI,
  createDown,
  createFileAPI,
  createUploadAPI,
  BASE_URL
} from "./request";

//下载导入模板
//模板导入功能
//涉密岗位
//下载涉密岗位导入模板
export const downloadImportTemplate = data => createDown(BASE_URL + "/rygl/gwdj/downloadImportTemplate", "get", data);
// 涉密岗位模板上传解析
export const uploadFileSmgwgl = data =>  createUploadAPI(BASE_URL + "/rygl/gwdj/uploadFile", "post", data);
//上传解析失败时 下载错误批注文件
export const downloadSmgwError = data =>  createDown(BASE_URL + "/rygl/gwdj/downloadSmgwError", "get", data);
//删除全部岗位信息
export const deleteAllGwxx = data =>  createAPI(BASE_URL + "/rygl/gwdj/deleteAllGwxx", "post", data);

//涉密人员
//下载涉密人员导入模板
export const downloadImportTemplateSmry = data => createDown(BASE_URL + "/rygl/yhxx/downloadImportTemplate", "get", data);
// 涉密人员模板上传解析
export const uploadFileSmry = data =>  createUploadAPI(BASE_URL + "/rygl/yhxx/uploadFile", "post", data);
//上传解析失败时 下载错误批注文件
export const downloadYhxxError = data =>  createDown(BASE_URL + "/rygl/yhxx/downloadYhxxError", "get", data);
//删除全部涉密人员
export const deleteAllYhxx = data =>  createAPI(BASE_URL + "/rygl/yhxx/deleteAllYhxx", "post", data);

//岗位变更
//下载岗位变更导入模板
export const downloadImportTemplateGwbg = data => createDown(BASE_URL + "/rygl/mjbg/downloadImportTemplate", "get", data);
// 岗位变更模板上传解析
export const uploadFileGwbg = data =>  createUploadAPI(BASE_URL + "/rygl/mjbg/uploadFile", "post", data);
//上传解析失败时 下载错误批注文件
export const downloadMjbgError = data =>  createDown(BASE_URL + "/rygl/mjbg/downloadMjbgError", "get", data);
//删除全部岗位变更
export const deleteAllMjbg = data =>  createAPI(BASE_URL + "/rygl/mjbg/deleteAllMjbg", "post", data);
//导入解析成功添加接口 不带联动
export const importAddMjbg = data =>  createAPI(BASE_URL + "/rygl/mjbg/importAddMjbg", "post", data);

//离职离岗
//下载离职离岗导入模板
export const downloadImportTemplateLglz = data => createDown(BASE_URL + "/rygl/lghz/downloadImportTemplate", "get", data);
// 离职离岗模板上传解析
export const uploadFileLglz = data =>  createUploadAPI(BASE_URL + "/rygl/lghz/uploadFile", "post", data);
//上传解析失败时 下载错误批注文件
export const downloadLzlgError = data =>  createDown(BASE_URL + "/rygl/lghz/downloadLzlgError", "get", data);
//删除全部离职离岗
export const deleteAllLzlg = data =>  createAPI(BASE_URL + "/rygl/lghz/deleteAllLzlg", "post", data);
//导入解析成功添加接口 不带联动
export const importAddLghz = data =>  createAPI(BASE_URL + "/rygl/lghz/importAddLghz", "post", data);

//场所管理
//下载场所管理导入模板
export const downloadImportTemplateCsgl = data => createDown(BASE_URL + "/csgl/csdj/downloadImportTemplate", "get", data);
//下载非授权导入模板
export const downloadImportTemplatewsq = data => createDown(BASE_URL + "/csgl_wsqjrqd/downloadImportTemplate", "get", data);
//下载非密重点人员模板
export const downloadImportTemplatefmzdry = data => createDown(BASE_URL + "/rygl-zdry/downloadImportTemplate", "get", data);
//场所管理模板上传解析
export const uploadFileCsgl = data =>  createUploadAPI(BASE_URL + "/csgl/csdj/uploadFile", "post", data);
//非密重点人员模板上传解析
export const uploadFilefmzdry = data =>  createUploadAPI(BASE_URL + "/rygl-zdry/uploadFile", "post", data);
//非授权人员模板上传解析
export const uploadFilewsq = data =>  createUploadAPI(BASE_URL + "/csgl_wsqjrqd/uploadFile", "post", data);
//上传解析失败时 下载错误批注文件
export const downloadSmcsError = data =>  createDown(BASE_URL + "/csgl/csdj/downloadSmcsError", "get", data);
//上传解析失败时 下载错误批注文件
export const downloadWsqjrqdError = data =>  createDown(BASE_URL + "/csgl_wsqjrqd/downloadWsqjrqdError", "get", data);
//上传解析失败时 下载错误批注文件
export const downloadZdryError = data =>  createDown(BASE_URL + "/rygl-zdry/downloadZdryError", "get", data);
//删除全部场所管理
export const deleteAllSmcs = data =>  createAPI(BASE_URL + "/csgl/csdj/deleteAllSmcs", "post", data);

//场所变更
//下载场所变更导入模板
export const downloadImportTemplateCsbg = data => createDown(BASE_URL + "/csgl/csbgdj/downloadImportTemplate", "get", data);
//场所变更模板上传解析
export const uploadFileCsbg = data =>  createUploadAPI(BASE_URL + "/csgl/csbgdj/uploadFile", "post", data);
//上传解析失败时 下载错误批注文件
export const downloadCsbgError = data =>  createDown(BASE_URL + "/csgl/csbgdj/downloadCsbgError", "get", data);
//删除全部场所变更
export const deleteAllCsbg = data =>  createAPI(BASE_URL + "/csgl/csbgdj/deleteAllCsbg", "post", data);
//导入解析成功添加接口 不带联动
export const importAddCsbg = data =>  createAPI(BASE_URL + "/csgl/csbgdj/importAddCsbg", "post", data);

//涉密计算机
//下载涉密计算机导入模板
export const downloadImportTemplateSmjsj = data => createDown(BASE_URL + "/sbgl/smjsj/downloadImportTemplate", "get", data);
// 涉密计算机模板上传解析
export const uploadFileSmjsj = data =>  createUploadAPI(BASE_URL + "/sbgl/smjsj/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadSmjsjError = data =>  createDown(BASE_URL + "/sbgl/smjsj/downloadSmjsjError", "get", data);
//删除全部涉密计算机
export const deleteAllSmjsj = data =>  createAPI(BASE_URL + "/sbgl/smjsj/deleteAllSmjsj", "post", data);

//非涉密计算机
// 下载非涉密计算机导入模板
export const downloadImportTemplateFsmjsj = data => createDown(BASE_URL + "/sbgl/fmjsj/downloadImportTemplate", "get", data);
// 非涉密计算机模板上传解析
export const uploadFileFsmjsj = data =>  createUploadAPI(BASE_URL + "/sbgl/fmjsj/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadFmjsjError = data =>  createDown(BASE_URL + "/sbgl/fmjsj/downloadFmjsjError", "get", data);
//删除全部非密计算机
export const deleteAllFmjsj = data =>  createAPI(BASE_URL + "/sbgl/fmjsj/deleteAllFmjsj", "post", data);

//涉密移动存储介质
// 下载涉密移动存储介质导入模板
export const downloadImportTemplateYdcc = data => createDown(BASE_URL + "/sbgl/ydccjz/downloadImportTemplate", "get", data);
// 涉密移动存储介质模板上传解析
export const uploadFileYdcc = data =>  createUploadAPI(BASE_URL + "/sbgl/ydccjz/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadYdccjzError = data =>  createDown(BASE_URL + "/sbgl/ydccjz/downloadYdccjzError", "get", data);
//删除全部涉密移动存储介质
export const deleteAllYdccjz = data =>  createAPI(BASE_URL + "/sbgl/ydccjz/deleteAllYdccjz", "post", data);

//涉密办公自动化设备
// 下载涉密办公自动化设备导入模板
export const downloadImportTemplateSmbgzdh = data => createDown(BASE_URL + "/sbgl/smxxsb/downloadImportTemplate", "get", data);
// 涉密办公自动化设备模板上传解析
export const uploadFileSmbgzdh = data =>  createUploadAPI(BASE_URL + "/sbgl/smxxsb/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadSmxxsbError = data =>  createDown(BASE_URL + "/sbgl/smxxsb/downloadSmxxsbError", "get", data);
//删除全部涉密办公自动化设备
export const deleteAllSmxxsb = data =>  createAPI(BASE_URL + "/sbgl/smxxsb/deleteAllSmxxsb", "post", data);

//非涉密办公自动化设备
// 下载非涉密办公自动化设备导入模板
export const downloadImportTemplateFmbgzdh = data => createDown(BASE_URL + "/sbgl/fmxxsb/downloadImportTemplate", "get", data);
// 非涉密办公自动化设备模板上传解析
export const uploadFileFmbgzdh = data =>  createUploadAPI(BASE_URL + "/sbgl/fmxxsb/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadFmxxsbError = data =>  createDown(BASE_URL + "/sbgl/fmxxsb/downloadFmxxsbError", "get", data);
//删除全部非涉密办公自动化设备
export const deleteAllFmxxsb = data =>  createAPI(BASE_URL + "/sbgl/fmxxsb/deleteAllFmxxsb", "post", data);

//涉密网络设备
// 下载涉密网络设备导入模板
export const downloadImportTemplateSmwlsb = data => createDown(BASE_URL + "/sbgl/smwlsb/downloadImportTemplate", "get", data);
// 涉密网络设备模板上传解析
export const uploadFileSmwlsb = data =>  createUploadAPI(BASE_URL + "/sbgl/smwlsb/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadSmwlsbError = data =>  createDown(BASE_URL + "/sbgl/smwlsb/downloadSmwlsbError", "get", data);
//删除全部涉密网络设备
export const deleteAllSmwlsb = data =>  createAPI(BASE_URL + "/sbgl/smwlsb/deleteAllSmwlsb", "post", data);

//非涉密网络设备
// 下载非涉密网络设备导入模板
export const downloadImportTemplateFmwlsb = data => createDown(BASE_URL + "/sbgl/fmwlsb/downloadImportTemplate", "get", data);
// 非涉密网络设备模板上传解析
export const uploadFileFmwlsb = data =>  createUploadAPI(BASE_URL + "/sbgl/fmwlsb/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadFmwlsbError = data =>  createDown(BASE_URL + "/sbgl/fmwlsb/downloadFmwlsbError", "get", data);
//删除全部非密网络设备
export const deleteAllFmwlsb = data =>  createAPI(BASE_URL + "/sbgl/fmwlsb/deleteAllFmwlsb", "post", data);

//安全产品
// 下载安全产品导入模板
export const downloadImportTemplateAqcp = data => createDown(BASE_URL + "/sbgl/xxsb/downloadImportTemplate", "get", data);
// 安全产品模板上传解析
export const uploadFileAqcp = data =>  createUploadAPI(BASE_URL + "/sbgl/xxsb/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadXxsbError = data =>  createDown(BASE_URL + "/sbgl/xxsb/downloadXxsbError", "get", data);
//删除全部安全产品
export const deleteAllXxsb = data =>  createAPI(BASE_URL + "/sbgl/xxsb/deleteAllXxsb", "post", data);

//涉密载体
// 下载涉密载体导入模板
export const downloadImportTemplateSmzt = data => createDown(BASE_URL + "/ztgl/zt/downloadImportTemplate", "get", data);
//涉密载体模板上传解析
export const uploadFileSmzt = data =>  createUploadAPI(BASE_URL + "/ztgl/zt/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadSmztError = data =>  createDown(BASE_URL + "/ztgl/zt/downloadSmztError", "get", data);
//删除全部涉密载体
export const deleteAllZt = data =>  createAPI(BASE_URL + "/ztgl/zt/deleteAllZt", "post", data);

//定密责任人
// 下载定密责任人导入模板
export const downloadImportTemplateDmzrr = data => createDown(BASE_URL + "/dmgl/dmzrr/downloadImportTemplate", "get", data);
//定密责任人模板上传解析
export const uploadFileDmzrr = data =>  createUploadAPI(BASE_URL + "/dmgl/dmzrr/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadDmzrrError = data =>  createDown(BASE_URL + "/dmgl/dmzrr/downloadDmzrrError", "get", data);
//删除全部定密责任人
export const deleteAllDmzrr = data =>  createAPI(BASE_URL + "/dmgl/dmzrr/deleteAllDmzrr", "post", data);

//定密授权
// 下载定密授权导入模板
export const downloadImportTemplateDmsq = data => createDown(BASE_URL + "/dmgl/dmsq/downloadImportTemplate", "get", data);
//定密授权模板上传解析
export const uploadFileDmsq = data =>  createUploadAPI(BASE_URL + "/dmgl/dmsq/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadDmsqError = data =>  createDown(BASE_URL + "/dmgl/dmsq/downloadDmsqError", "get", data);
//删除全部定密授权
export const deleteAllDmsq = data =>  createAPI(BASE_URL + "/dmgl/dmsq/deleteAllDmsq", "post", data);

//国家秘密事项
// 下载国家秘密事项导入模板
export const downloadImportTemplateGjmmsx = data => createDown(BASE_URL + "/dmgl/gjmmsx/downloadImportTemplate", "get", data);
//国家秘密事项模板上传解析
export const uploadFileGjmmsx = data =>  createUploadAPI(BASE_URL + "/dmgl/gjmmsx/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadGjmmsxError = data =>  createDown(BASE_URL + "/dmgl/gjmmsx/downloadGjmmsxError", "get", data);
//删除全部国家秘密事项
export const deleteAllGjmmsx = data =>  createAPI(BASE_URL + "/dmgl/gjmmsx/deleteAllGjmmsx", "post", data);

//定密培训
// 下载定密培训导入模板
export const downloadImportTemplateDmpx = data => createDown(BASE_URL + "/dmgl/dmpx/downloadImportTemplate", "get", data);
//定密培训模板上传解析
export const uploadFileDmpx = data =>  createUploadAPI(BASE_URL + "/dmgl/dmpx/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadDmpxError = data =>  createDown(BASE_URL + "/dmgl/dmpx/downloadDmpxError", "get", data);
//删除全部定密培训
export const deleteAllDmpx = data =>  createAPI(BASE_URL + "/dmgl/dmpx/deleteAllDmpx", "post", data);

//不明确事项确定情况
// 下载不明确事项确定情况导入模板
export const downloadImportTemplateBmqsxqdqk = data => createDown(BASE_URL + "/dmgl/bmqsxqdqk/downloadImportTemplate", "get", data);
//不明确事项确定情况模板上传解析
export const uploadFileBmqsxqdqk = data =>  createUploadAPI(BASE_URL + "/dmgl/bmqsxqdqk/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadBmqsxqdqkError = data =>  createDown(BASE_URL + "/dmgl/bmqsxqdqk/downloadBmqsxqdqkError", "get", data);
//删除全部不明确事项确定情况
export const deleteAllBmqsxqdqk = data =>  createAPI(BASE_URL + "/dmgl/bmqsxqdqk/deleteAllBmqsxqdqk", "post", data);

//政府采购项目情况
// 下载政府采购项目情况导入模板
export const downloadImportTemplateSmzfcgxmqk = data => createDown(BASE_URL + "/dmgl/smzfcgxmqk/downloadImportTemplate", "get", data);
//政府采购项目情况模板上传解析
export const uploadFileSmzfcgxmqk = data =>  createUploadAPI(BASE_URL + "/dmgl/smzfcgxmqk/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadSmzfcgxmqkError = data =>  createDown(BASE_URL + "/dmgl/smzfcgxmqk/downloadSmzfcgxmqkError", "get", data);
//删除全部政府采购项目情况
export const deleteAllSmzfcgxmqk = data =>  createAPI(BASE_URL + "/dmgl/smzfcgxmqk/deleteAllSmzfcgxmqk", "post", data);

//培训清单
// 下载培训清单导入模板
export const downloadImportTemplatePxqd = data => createDown(BASE_URL + "/jypx/pxdj/downloadImportTemplate", "get", data);
//培训清单模板上传解析
export const uploadFilePxqd = data =>  createUploadAPI(BASE_URL + "/jypx/pxdj/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadJypxError = data =>  createDown(BASE_URL + "/jypx/pxdj/downloadJypxError", "get", data);
//删除全部培训清单
export const deleteAllJypx = data =>  createAPI(BASE_URL + "/jypx/pxdj/deleteAllJypx", "post", data);

//定密情况年度统计
// 下载定密情况年度统计导入模板
export const downloadImportTemplateNdtj = data => createDown(BASE_URL + "/dmgl/ndtj/downloadImportTemplate", "get", data);
//定密情况年度统计模板上传解析
export const uploadFileNdtj = data =>  createUploadAPI(BASE_URL + "/dmgl/ndtj/uploadFile", "post", data);
//下载解析失败时的错误批注文件
export const downloadNdtjError = data =>  createDown(BASE_URL + "/dmgl/ndtj/downloadNdtjError", "get", data);
//删除全部定密情况年度统计
export const deleteAllNdtj = data =>  createAPI(BASE_URL + "/dmgl/ndtj/deleteAllNdtj", "post", data);

//涉密计算机
//7.下载涉密key台账导入模板
export const downloadImportTemplatekey = data => createDown(BASE_URL + "/sbgl/key/downloadImportTemplate", "get", data);
//8.涉密key台账模板导入解析
export const uploadFilekey = data =>  createUploadAPI(BASE_URL + "/sbgl/key/uploadFile", "post", data);
//9.下载涉密key台账错误批注文件
export const downloadSmKeyError = data =>  createDown(BASE_URL + "/sbgl/key/downloadSmKeyError", "get", data);
//下载非密人员导入模板
export const downloadImportFmry = data => createDown(BASE_URL + "/rygl-fmry/downloadImportFmry", "get", data);
//非密人员模板导入
export const uploadFilefmry = data => createUploadAPI(BASE_URL + "/rygl-fmry/uploadFile", "post", data);
//下载上传失败时的错误批注文件
export const downloadFmryError = data => createDown(BASE_URL + "/rygl-fmry/downloadFmryError", "get", data);
//删除全部涉密计算机
// export const deleteAllSmjsj = data =>  createAPI(BASE_URL + "/sbgl/smjsj/deleteAllSmjsj", "post", data);