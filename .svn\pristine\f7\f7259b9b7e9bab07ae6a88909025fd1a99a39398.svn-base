import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//待办任务列表
export const getTodoList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getTodoList", 'post',data)
//服务类型查询
export const getFwlxList = data => createAPI(BASE_URL+"/api/select/getFwlxList", 'post',data)

//办理信息 - blxx审批指南
export const getBlzn = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getBlzn", 'post',data)
//办理信息 - blxx审批信息
export const getRyscInfoBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getRyscInfoBySlid", 'get',data)
//办理信息 - blxx审批信息
export const getLzlgInfoBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLzlg/getLzlgInfoBySlid", 'get',data)
//办理信息 - blxx审批信息
export const getZgfsInfoBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlZgfs/getZgfsInfoBySlid", 'get',data)
//根据lcslid获取在岗复审信息详情
export const getDjgwbgInfoByLcsllid = data => createAPI(BASE_URL+"/rygl-djgwbg/getDjgwbgInfoByLcsllid", 'get',data)
//通过rwid涉密人员-出国出境-审批记录表单
export const selectRyglCgcjBySlId = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/selectRyglCgcjBySlId", 'get',data)
//办理信息 - blxx流程跟踪
export const getSpGjxx = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getSpGjxx", 'post',data)
//判断实例所处环节
export const getSchj = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getSchj", 'post',data)
//判断实例所处环节
export const getSxsh = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/sh", 'post',data)
//查询审批用户列表
export const getSpUserList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getSpUserList", 'post',data)
// 非第一环节选择审批人
export const tjclr = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/tjclr", 'post',data)
//修改任用审查详情记录
export const updateRysc = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/updateRysc", 'post',data)
//修改任用审查详情记录
export const updateZgfs = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlZgfs/updateZgfs", 'post',data)
//修改任用审查详情记录
export const updateLzlg = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLzlg/updateLzlg", 'post',data)

//已办列表
export const getYblbList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getYblbList", 'post',data)

//已办列表
export const getWdfqList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/getWdfqList", 'post',data)
//根据涉密人员id查询任用审查记录
export const selectRyscPageBySmryid = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlSxbl/selectRyscPageBySmryid", 'get',data)
//根据涉密人员id查询在岗复审记录
export const selectZgfsPageBySmryid = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlZgfs/selectZgfsPageBySmryid", 'get',data)
//根据涉密人员id查询离职离岗记录
export const selectLzlgPageBySmryid = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLzlg/selectLzlgPageBySmryid", 'get',data)
//根据涉密人员id查询岗位变更记录
export const selectDjgwbgPageBySmryid = data => createAPI(BASE_URL+"/rygl-djgwbg/selectDjgwbgPageBySmryid", 'get',data)
//根据涉密人员id查询出国出境记录
export const selectRyglCgcjPageBySmryId = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/selectRyglCgcjPageBySmryId", 'get',data)