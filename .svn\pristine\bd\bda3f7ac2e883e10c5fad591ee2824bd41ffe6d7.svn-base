// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css';
import "./assets/style/commonStyle.css";
import App from './App'
import router from './router'
import axios from 'axios'
import VueAxios from 'vue-axios'
import * as store from './store/index'
import * as echarts from 'echarts'
import 'echarts-liquidfill'
// import store from './store/index';
// 引入公共样式表-锚点菜单
import '../style/md_menu.css'
// 引入公共样式表-dialog自定义样式
import '../style/dialog_customize.css'
import './assets/font/font.css'
import './assets/font/font1.css'
import './assets/font/font2.css'
import './assets/font/font3.css'
import './assets/font/font4.css'
import './assets/font/font5.css'

// import store from '../renderer/store'
import {
  message
} from '@/utils/resetMessage';

Vue.config.productionTip = false
Vue.use(VueAxios, axios)
// ElementUI.TableColumn.props.showOverflowTooltip ={type:Boolean, default: true}
Vue.use(ElementUI)
Vue.prototype.$message = message;
Vue.prototype.$echarts = echarts


// router.beforeEach((to, from, next) => {
//   // const token = localStorage.getItem('token')
//   if (to.meta.requireAuth && !router.app.$options.store===store.state.Counter.token) {
//     next('/login')
//   } else {
//     next()
//   }
// })

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})

