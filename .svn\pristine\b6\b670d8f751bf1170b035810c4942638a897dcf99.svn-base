<template>
  <div class="bg_con" style="height: calc(100% - 38px);">
    <div style="width: 100%; position: relative; overflow: hidden; height: 100%;">

      <div class="dabg" style="height: 100%;">
        <div class="content" style="height: 100%;">
          <div class="table" style="height: 100%;">
            <!-- -----------------操作区域--------------------------- -->
            <div class="mhcx">
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:left">
                <el-form-item style="font-weight: 700;">
                  <el-input v-model="formInline.zcbh" clearable placeholder="编号" class="widths">
                  </el-input>
                </el-form-item>
                <el-form-item style="font-weight: 700;">
                  <el-date-picker v-model="formInline.qyrq" type="daterange" range-separator="至"
                    start-placeholder="启用起始日期" end-placeholder="启用结束日期" format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                  </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                </el-form-item>
                <el-form-item>
                  <el-button type="warning" icon="el-icon-circle-close" @click="cz">重置</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" :model="formInline" size="medium" class="demo-form-inline" style="float:right">
                <el-form-item style="float: right;">
                  <el-button type="danger" size="medium" @click="shanchu" icon="el-icon-delete-solid">
                    删除
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="primary" size="medium" icon="el-icon-download" @click="exportList()">导出
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <input type="file" ref="upload"
                    style="display: none;position: absolute;top:10px; right:0;opacity:0; cursor: pointer;height: 32px;width: 56px;z-index:1;"
                    accept=".xls,.xlsx">
                  <el-button type="primary" icon="el-icon-upload2" size="medium" @click="dr_dialog = true">
                    导入
                  </el-button>
                </el-form-item>
                <el-form-item style="float: right;">
                  <el-button type="success" size="medium" @click="xzaqcp" icon="el-icon-plus">
                    新增
                  </el-button>
                </el-form-item>
              </el-form>
            </div>


            <!-- -----------------审查组人员列表--------------------------- -->
            <div class="table_content_padding" style="height: 100%;">
              <div class="table_content" style="height: 100%;">
                <el-table :data="aqcpList" border @selection-change="selectRow"
                  :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }"
                  style="width: 100%;border:1px solid #EBEEF5;" height="calc(100% - 34px - 41px - 3px)" stripe>
                  <el-table-column type="selection" width="55" align="center"> </el-table-column>
                  <el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
                  <!-- <el-table-column prop="mc" label="名称"></el-table-column> -->
                  <el-table-column prop="ppxh" label="品牌型号"></el-table-column>
                  <el-table-column prop="lx" label="类型"></el-table-column>
                  <el-table-column prop="zcbh" label="固定资产编号"></el-table-column>
                  <el-table-column prop="qyrq" label="启用日期"></el-table-column>
                  <el-table-column prop="sl" label="数量"></el-table-column>
                  <el-table-column prop="yt" label="用途"></el-table-column>
                  <el-table-column prop="" label="操作" width="120">
                    <template slot-scope="scoped">
                      <el-button size="medium" type="text" @click="xqyl(scoped.row)">详情
                      </el-button>
                      <el-button size="medium" type="text" @click="updateItem(scoped.row)">修改
                      </el-button>
                    </template>
                  </el-table-column>

                </el-table>

                <!-- -------------------------分页区域---------------------------- -->
                <div style="border: 1px solid #ebeef5;">
                  <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange"
                    :pager-count="5" :current-page="page" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
                    layout="total, prev, pager, sizes,next, jumper" :total="total">
                  </el-pagination>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模板下载 -->
        <el-dialog title="开始导入" class="scbg-dialog" width="600px" @close="mbxzgb" :visible.sync="dr_dialog" show-close>
          <div style="padding: 20px;">
            <div class="daochu">
              <div>一、请点击“导出模板”，并参照模板填写信息。</div>
              <el-button type="primary" size="mini" @click="mbdc">
                模板导出
              </el-button>
            </div>
            <div class="daochu">
              <div>二、数据导入方式：</div>
              <el-radio-group v-model="sjdrfs" @change="Radio($event)">
                <el-radio label="1">追加（导入时已有的记录信息不变，只添加新的记录）</el-radio>
                <el-radio label="2">覆盖（导入时更新已有的记录信息，并添加新的记录）</el-radio>
              </el-radio-group>
            </div>
            <div class="daochu">
              <div>三、将按模板填写的文件，导入到系统中。</div>
              <el-button type="primary" size="mini" @click="chooseFile">
                上传导入
              </el-button>
            </div>
          </div>
        </el-dialog>
        <!-- -----------------导入-弹窗--------------------------- -->
        <el-dialog width="1000px" height="800px" title="导入安全产品" class="scbg-dialog" :visible.sync="dialogVisible_dr"
          show-close>
          <div style="height: 600px;">
            <el-table :data="dr_cyz_list" ref="multipleTable" @selection-change="handleSelectionChange"
              style="width: 100%;border:1px solid #EBEEF5;" height="100%" stripe>
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column prop="类型" label="类型"></el-table-column>
              <el-table-column prop="品牌型号" label="品牌型号"></el-table-column>
              <el-table-column prop="资产编号" label="资产编号"></el-table-column>
              <el-table-column prop="用途" label="用途"></el-table-column>
              <el-table-column prop="安装位置" label="安装位置"></el-table-column>
              <el-table-column prop="数量" label="数量"></el-table-column>

            </el-table>
          </div>

          <div style="height :30px;display: flex;align-items: center;justify-content: center;margin: 10px 0;">
            <el-button type="primary" @click="drcy" size="mini">导 入</el-button>
            <el-button type="warning" @click="dialogVisible_dr = false" size="mini">关 闭</el-button>
          </div>
        </el-dialog>

        <!-- -----------------涉密人员离岗离职信息-弹窗--------------------------- -->
        <el-dialog title="安全产品详细信息" :close-on-click-modal="false" :visible.sync="dialogVisible" width="47%" class="xg"
          :before-close="handleClose" @close="close('formName')">
          <el-form ref="formName" :model="tjlist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="资产编号" prop="zcbh" class="one-line aqcp">
              <el-input placeholder="资产编号" v-model="tjlist.zcbh" clearable @blur="onInputBlur(1)">
              </el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="tjlist.qyrq" clearable type="date" style="width: 100%;" placeholder="选择日期"
                  format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="lx">
                <el-select v-model="tjlist.lx" placeholder="请选择类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="品牌型号" prop="ppxh">
                <el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="tjlist.ppxh" style="width: 100%;"
                  :fetch-suggestions="querySearchppxh" placeholder="品牌型号">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="用途" prop="yt">
                <el-autocomplete class="inline-input" value-key="yt" v-model.trim="tjlist.yt" style="width: 100%;"
                  :fetch-suggestions="querySearchyt" placeholder="用途">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="安装位置" prop="azwz">
                <el-autocomplete class="inline-input" value-key="azwz" v-model.trim="tjlist.azwz" style="width: 100%;"
                  :fetch-suggestions="querySearchazwz" placeholder="安装位置">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="数量" prop="sl">
                <el-input type="number" placeholder="数量" v-model="tjlist.sl" clearable></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitTj('formName')">保 存</el-button>
            <el-button type="warning" @click="handleClose">关 闭</el-button>
          </span>
        </el-dialog>
        <el-dialog title="修改安全产品详细信息" :close-on-click-modal="false" :visible.sync="xgdialogVisible" width="47%"
          class="xg" @close="close1('form')">
          <el-form ref="form" :model="xglist" :rules="rules" label-width="120px" size="mini">
            <el-form-item label="资产编号" prop="zcbh" class="one-line aqcp">
              <el-input placeholder="资产编号" v-model="xglist.zcbh" clearable @blur="onInputBlur(2)" disabled>
              </el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.qyrq" style="width: 100%;" clearable type="date" placeholder="选择日期"
                  format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" placeholder="请选择类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="品牌型号" prop="ppxh">
                <el-autocomplete class="inline-input" value-key="ppxh" v-model.trim="xglist.ppxh" style="width: 100%;"
                  :fetch-suggestions="querySearchppxh" placeholder="品牌型号">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="用途" prop="yt">
                <el-autocomplete class="inline-input" value-key="yt" v-model.trim="xglist.yt" style="width: 100%;"
                  :fetch-suggestions="querySearchyt" placeholder="用途">
                </el-autocomplete>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="安装位置" prop="azwz">
                <el-autocomplete class="inline-input" value-key="azwz" v-model.trim="xglist.azwz" style="width: 100%;"
                  :fetch-suggestions="querySearchazwz" placeholder="安装位置">
                </el-autocomplete>
              </el-form-item>
              <el-form-item label="数量" prop="sl">
                <el-input type="number" placeholder="数量" v-model="xglist.sl" clearable></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updataDialog('form')">保 存</el-button>
            <el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="安全产品详细信息详情" :close-on-click-modal="false" :visible.sync="xqdialogVisible" width="47%"
          class="xg">
          <el-form ref="form" :model="xglist" label-width="120px" size="mini" disabled>
            <el-form-item label="资产编号" prop="zcbh" class="one-line">
              <el-input placeholder="资产编号" v-model="xglist.zcbh" clearable></el-input>
            </el-form-item>
            <div style="display:flex">
              <el-form-item label="启用日期" prop="qyrq">
                <!-- <el-input v-model="tjlist.sgsj" clearable></el-input> -->
                <el-date-picker v-model="xglist.qyrq" clearable type="date" style="width: 100%;" placeholder="选择日期"
                  format="yyyy年MM月dd日" value-format="yyyy年MM月dd日">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="类型" prop="lx">
                <el-select v-model="xglist.lx" placeholder="请选择类型" style="width: 100%;">
                  <el-option v-for="item in sblxxz" :label="item.sblxmc" :value="item.sblxmc" :key="item.sblxid">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="品牌型号" prop="ppxh">
                <el-input placeholder="品牌型号" v-model="xglist.ppxh" clearable></el-input>
              </el-form-item>
              <el-form-item label="用途" prop="yt">
                <el-input placeholder="用途" v-model="xglist.yt" clearable></el-input>
              </el-form-item>
            </div>
            <div style="display:flex">
              <el-form-item label="安装位置" prop="azwz">
                <el-input placeholder="安装位置" v-model="xglist.azwz" clearable></el-input>
              </el-form-item>
              <el-form-item label="数量" prop="sl">
                <el-input type="number" placeholder="数量" v-model="xglist.sl" clearable></el-input>
              </el-form-item>
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" @click="xqdialogVisible = false">关 闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>

</template>
<script>
  export default {
    components: {},
    props: {},
    data() {
      return {
        zcbh: '', //资产编号
        pdaqcp: 0, //提示信息判断
        sblxxz: [], //下拉框数据
        aqcpList: [], //列表数据
        tableDataCopy: [], //查询备份数据
        xglist: {}, //修改与详情数据
        updateItemOld: {}, //修改，详情弹框信息
        xgdialogVisible: false, //修改弹框
        xqdialogVisible: false, //详情弹框
        formInline: {}, //查询区域数据
        tjlist: {
          zcbh: '',
          qyrq: '',
          lx: '',
          ppxh: '',
          yt: '',
          azwz: '',
          sl: '',
        }, //添加数据
        rules: {
          zcbh: [{
            required: true,
            message: '请输入资产编号',
            trigger: 'blur'
          }, ],
          qyrq: [{
            required: true,
            message: '请选择启用日期',
            trigger: 'blur'
          }, ],
          lx: [{
            required: true,
            message: '请选择类型',
            trigger: 'blur'
          }, ],
          ppxh: [{
            required: true,
            message: '请输入品牌型号',
            trigger: ['blur', 'change'],
          }, ],
          yt: [{
            required: true,
            message: '请输入用途',
            trigger: ['blur', 'change'],
          }, ],
          azwz: [{
            required: true,
            message: '请输入安装位置',
            trigger: ['blur', 'change'],
          }, ],
          sl: [{
            required: true,
            message: '请输入数量',
            trigger: 'blur'
          }, ],
        }, //校验
        page: 1, //当前页
        pageSize: 10, //每页条数
        total: 0, //总共数据数
        selectlistRow: [], //列表的值
        dialogVisible: false, //添加弹窗状态
        //导入
        dialogVisible_dr: false, //导入成员组弹窗状态
        dr_cyz_list: [], //待选择导入成员组列表
        multipleTable: [], //已选择导入成员组列表
        dwmc: '', //单位名称
        year: '', //年
        yue: '', //月
        ri: '', //日
        Date: '', //时间
        xh: [], //导出列表序号
        dclist: [], //复制列表数据
        dr_dialog: false,
        //数据导入方式
        sjdrfs: ''
      };
    },
    computed: {},
    mounted() {

    },
    methods: {
      //新增数据按钮事件
      xzaqcp() {
        this.dialogVisible = true
      },
      //数据导入方式按钮事件
      Radio(val) {

      },
      mbxzgb() {

      },
      mbdc() {

      },
      //导入


      //----成员组选择
      handleSelectionChange(val) {

      },
      //---确定导入成员组
      drcy() {

      },
      //----表格导入方法
      readExcel(e) {

      },
      //修改
      updataDialog(form) {


      },
      //详情弹框
      xqyl(row) {

      },
      //修改弹框
      updateItem(row) {

      },
      //查询
      onSubmit() {
        // //  form是查询条件
        // console.log(this.formInline);
        // // 备份了一下数据
        // let arr = this.tableDataCopy
        // // 通过遍历key值来循环处理
        // Object.keys(this.formInline).forEach(e => {
        // 	// 调用自己定义好的筛选方法
        // 	console.log(this.formInline[e]);
        // 	arr = this.filterFunc(this.formInline[e], e, arr)
        // })
        // // 为表格赋值
        // this.aqcpList = arr

      },
      //查询方法
      filterFunc(val, target, filterArr) {

      },

      returnSy() {

      },
      //获取列表的值
      aqcp() {

      },
      //删除
      shanchu(id) {

      },
      //添加
      showDialog() {

      },

      //导出
      exportList() {


      },
      //确定添加成员组
      submitTj(formName) {

      },

      deleteTkglBtn() {},
      //选中列表的数据
      selectRow(val) {

      },
      //列表分页--跳转页数
      handleCurrentChange(val) {

      },
      //列表分页--更改每页显示个数
      handleSizeChange(val) {

      },
      //添加重置
      resetForm() {

      },
      handleClose(done) {
        // this.resetForm();
        this.dialogVisible = false
      },
      // 弹框关闭触发
      close(formName) {
        // 清空表单校验，避免再次进来会出现上次校验的记录

      },
      //取消校验
      close1(form) {
        // 清空表单校验，避免再次进来会出现上次校验的记录

      },
      //添加时的校验
      onInputBlur(index) {

      },
      //模糊查询品牌型号
      querySearchppxh(queryString, cb) {

      },
      createFilterppxh(queryString) {

      },
      //模糊查询用途
      querySearchyt(queryString, cb) {

      },
      createFilteryt(queryString) {

      },
      //模糊查询安装位置
      querySearchazwz(queryString, cb) {

      },
      createFilterazwz(queryString) {

      },
      ppxhlist() {

      },
      cz() {
        console.log(1);
      },
      chooseFile() {},
    },
  };

</script>

<style scoped>
  .bg_con {
    width: 100%;
  }

  .dabg {
    /* margin-top: 10px; */
    box-shadow: 0px 2px 12px 0px rgba(9, 70, 255, 0.13);
    border-radius: 8px;
    width: 100%;
  }

  .daochu {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  /* /deep/.el-radio {
	display: block;
	margin-top: 10px;
	margin-bottom: 10px;
} */

  .xmlb-title {
    line-height: 60px;
    width: 100%;
    padding-left: 10px;
    height: 60px;
    background: url(../../assets/background/bg-02.png) no-repeat left;
    background-size: 100% 100%;
    text-indent: 10px;
    /* margin: 0 20px; */
    color: #0646bf;
    font-weight: 700;
  }

  .fhsy {
    display: inline-block;
    width: 120px;
    margin-top: 10px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 30px;
    padding-top: 4px;
    float: right;
    background: url(../../assets/icons/zzjg_icon1.png) no-repeat center;
    background-size: 100% 100%;
  }

  .item_button {
    height: 100%;
    float: left;
    padding-left: 10px;
    line-height: 50px;
  }

  .select_wrap {
    /* //padding: 5px; */

    .select_wrap_content {
      float: left;
      width: 100%;
      line-height: 50px;
      /* // padding-left: 20px; */
      /* // padding-right: 20px; */
      height: 100%;
      background: rgba(255, 255, 255, 0.7);

      .item_label {
        padding-left: 10px;
        height: 100%;
        float: left;
        line-height: 50px;
        font-size: 1em;
      }
    }
  }

  .mhcx1 {
    margin-top: 0px;
  }

  .widths {
    width: 6vw;
  }

  .cd {
    width: 191px;
  }

  /deep/.el-form--inline .el-form-item {
    margin-right: 9px;
  }

  /deep/.mhcx .el-form-item {
    /* margin-top: 5px; */
    margin-bottom: 5px;
  }

  .dialog-footer {
    display: block;
    margin-top: 10px;
  }

</style>
