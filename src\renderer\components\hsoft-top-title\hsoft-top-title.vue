<template>
  <div class="out">
    <div class="left">
      <div style="padding-left: 10px;">
        <slot name="left"></slot>
      </div>
    </div>
    <div v-show="showRightBool" class="right" @click="goBack">
      <img src="../../assets/icons/zzjg_icon1.png" />
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {}
  },
  props: {
    showRightBool: {
      require: false,
      default: false
    }
  },
  methods: {
    goBack () {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.out {
  /* background: red; */
  display: flex;
  /* height: 40px; */
  line-height: 24px;
  /* background-image: url(../../assets/background/bg-02.png); */
  background-size: 100% 100%;
  padding-bottom: 8px;
  margin-bottom: 10px;
  /* box-shadow: 0px 1px #e0e0e0; */
}
.out .left {
  /* background: rgba(0, 0, 255, 0.438); */
  flex: 1;
  color: #0646bf;
  font-weight: 700;
  position: relative;
}
.out .left::before {
  content: "";
  background: #0646bf;
  width: 4px;
  height: 75%;
  position: absolute;
  top: 14%;
}
.out .right {
  /* background: #f3f3f3; */
  width: 18%;
  text-align: right;
  position: relative;
  padding-right: 10px;
  cursor: pointer;
}
.out .right img {
  position: relative;
  top: 50%;
  margin-top: -20px;
}
.out .right span {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  right: 10px;
  /* background: red; */
  height: 40px;
  display: inline-block;
  line-height: 40px;
  width: 4em;
  text-align: center;
  color: #0646bf;
  font-weight: 700;
}
</style>