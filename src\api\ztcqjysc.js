import {createAPI, createFileAPI,createDown,createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//载体超期借用查询
export const seleteOutstandingZtglJydjAndZtglXdwcdj = data => createAPI(BASE_URL+"/ZtglJydj/seleteOutstandingZtglJydjAndZtglXdwcdj", 'get',data)// 涉密等级
//slid查jlid
export const getXdJyJlidBySlid = data => createAPI(BASE_URL+"/ztgl/cqjy/getXdJyJlidBySlid", 'get',data)// 涉密等级
//1、超期借用表单提交
export const submitCqjy = data => createAPI(BASE_URL+"/ztgl/cqjy/submitCqjy", 'post',data)// 涉密等级
//2、超期借用查询带分页
export const selectCqjyPage = data => createAPI(BASE_URL+"/ztgl/cqjy/selectCqjyPage", 'get',data)// 涉密等级
//3.通过jlid查询超期借用详情
export const getCqjyInfoByJlid = data => createAPI(BASE_URL+"/ztgl/cqjy/getCqjyInfoByJlid", 'get',data)// 涉密等级
//6、修改超期借用记录
export const updateCqjyByJlid = data => createAPI(BASE_URL+"/ztgl/cqjy/updateCqjyByJlid", 'post',data)// 涉密等级
//slid获取jlid
export const getJlidBySlidcq = data => createAPI(BASE_URL+"/ztgl/cqjy/getJlidBySlid", 'get',data)// 涉密等级
//5、通过slid删除超期借用记录
export const deletcqjy = data => createAPI(BASE_URL+"/ztgl/cqjy/deleteCqjy", 'post',data)// 涉密等级
//通过分类、类型、bmbh、时间查询带分页
export const updateXdJydjBySlid = data => createAPI(BASE_URL+"/ztgl/cqjy/updateXdJydjByYjlid", 'post',data)
//通过分类、类型、bmbh、时间查询带分页
export const selectXdJyJlidBySlid = data => createAPI(BASE_URL+"/ztgl/cqjy/selectXdJyJlidBySlid", 'get',data)
