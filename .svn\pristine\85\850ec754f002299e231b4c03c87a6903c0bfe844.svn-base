import {createAPI, createFileAPI,createDown, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、载体销毁表单提交
export const submitZtxh = data => createAPI(BASE_URL+"/ztgl/ztxh/submitZtxh", 'post',data)
//载体销毁查询带分页
export const selectZtxhPage = data => createAPI(BASE_URL+"/ztgl/ztxh/selectZtxhPage", 'get',data)
//通过jlid查询载体销毁详情
export const getZtxhInfoByJlid = data => createAPI(BASE_URL+"/ztgl/ztxh/getZtxhInfoByJlid", 'get',data)
//通过slid查询超期借用详情
export const getZtxhInfoBySlid = data => createAPI(BASE_URL+"/ztgl/ztxh/getZtxhInfoBySlid", 'get',data)
//通过slid删除载体销毁记录
export const deleteZtxh = data => createAPI(BASE_URL+"/ztgl/ztxh/deleteZtxh", 'post',data)
//修改载体销毁记录
export const updateZtxhByJlid = data => createAPI(BASE_URL+"/ztgl/ztxh/updateZtxhByJlid", 'post',data)

//根据原jlid查询载体清单
export const getZtqdListByYjlid = data => createAPI(BASE_URL+"/ztgl/ztqd/getZtqdListByYjlid", 'get',data)

//通过实例id 获取载体销毁的jlid
export const getSlidByjlxhid = data => createAPI(BASE_URL+"/ztgl/ztxh/getSlidByjlid", 'get',data)
//1.添加载体销毁登记记录
export const savaZtxhdjBatch = data => createAPI(BASE_URL+"/ztgl/xhdj/savaZtxhdjBatch", 'post',data)
//查询载体销毁登记带分页
export const selectZtxhdjPage = data => createAPI(BASE_URL+"/ztgl/xhdj/selectZtxhdjPage", 'get',data)
//载体销毁登记导出excel
export const exportZtxhdjData = data => createDown(BASE_URL+"/ztgl/xhdj/exportZtxhdjData", 'get',data)