import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、添加非密人员信息
export const saveFmry = data => createAPI(BASE_URL+"/rygl-fmry/saveFmry", 'post',data)
//2 、通过 ryid或者dwid 删除非密人员记录
export const removeFmry = data => createAPI(BASE_URL+"/rygl-fmry/removeFmry", 'post',data)
//3、根据 ryid或者dwid  修改非密人员信息
export const updateFmry = data => createAPI(BASE_URL+"/rygl-fmry/updateFmry", 'post',data)
// //4、通过 xm和bmmc 查询非密人员
// export const getFmry = data => createAPI(BASE_URL+"/rygl-fmry/getFmry", 'get',data)
//5、通过 ryid或者dwid批量删除非密人员
export const removeBatch = data => createAPI(BASE_URL+"/rygl-fmry/removeBatch", 'post',data)
//6、查询全部非密人员带分页
export const getFmryList = data => createAPI(BASE_URL+"/rygl-fmry/getFmryList", 'get',data)
//11.删除全部非密人员
export const deleteAllFmry = data => createAPI(BASE_URL+"/rygl-fmry/deleteAllFmry", 'post',data)



