<!--
  describe：'可添加/删除行的table'
  Created Date：2023-05-10 09:16:29
  Author: zhang<PERSON>ying
  -----
  Last Modified:
  Modified By：zhang<PERSON>ying
-->
<template>
  <div class="ad-container">
    <el-table border class="sec-el-table" :data="tableData"
      :header-cell-style="{ background: '#EEF7FF', color: '#4D91F8' }" stripe>
      <!--序号-->
      <el-table-column v-if="showIndex" type="index" label="序号" width="60" align="center"></el-table-column>
      <template v-for="(column, index) in columns">
        <!-- 输入框 -->
        <el-table-column :key="index" v-bind="column" :label="column.name" v-if="column.scopeType==='input'">
          <template slot-scope="scope">
            <el-input size="small"  v-model="scope.row[column.prop]" :disabled="scope.row.disabled || column.disabled"></el-input>
          </template>
        </el-table-column>
      </template>
      <!-- 操作列 -->
      <el-table-column align="left"
              v-bind="handleColumnProp"
              :label="handleColumnProp.label"
              v-if="handleColumn.length">
        <template slot-scope="scope">
          <div class="handle-column-wrap">
            <template v-for="(item, index) in handleColumn">
                <el-button v-if="item.prop!= ''" size="medium" type="text" @click="addRow(scope.row,item)">{{item.name}} </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'addLineTable',
  props: {
    tableData: {
      type: Array,
      require: true,
      default: []
    },
    showIndex: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array,
      require: true,
      default: []
    },
    handleColumnProp: {
      type: Object,
      default: () => ({
        label: 'global.handle'
      })
    },
    handleColumn: {
      type: Array,
      default: () => []
    },
    
  },
  data() {
    return {

    }
  },
  computed: {

  },
  mounted() {
    console.log(this.tableData)
  },
  methods: {

  },
}
</script>

<style scoped>
.sec-el-table {
  width: 100%;
  border: 1px solid #EBEEF5;
  height: calc(100% - 34px - 44px - 10px);
}

>>>.sec-el-table .el-input__inner {
  border: none !important;
  border-radius: 0;
}</style>
