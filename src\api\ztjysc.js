import {createAPI, createFileAPI,createDown,createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//添加涉密载体-载体借阅
export const addZtglJy = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/addZtglJy", 'post',data)// 涉密等级
//删除涉密载体-载体借阅
export const deleteZtglJy = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/deleteZtglJy", 'post',data)// 涉密等级
//修改涉密载体-载体借阅
export const updateZtglJy = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/updateZtglJy", 'post',data)// 涉密等级
//分页查询涉密载体-载体借阅
export const  selectZtglJyPage = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/selectZtglJyPage", 'get',data)// 涉密等级
//通过jlid查询涉密载体-载体借阅
export const  selectZtglJyByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/selectZtglJyByJlid", 'get',data)// 涉密等级
//通过jlid查询涉密载体-载体借阅登记
export const  selectZtglJydjjByJlid = data => createAPI(BASE_URL+"/ZtglJydj/seleteZtglJydjjByjlid", 'get',data)// 涉密等级
//通过slid查询涉密载体-载体借阅
export const  selectZtglJyBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/selectZtglJyBySlid", 'post',data)// 涉密等级
//查询载体
export const  selectPageZtglZtzzdj = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZtzzdj/selectPageZtglZtzzdj", 'get',data)// 涉密等级
//slid转jlid
export const  getJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/getJlidBySlid", 'get',data)// 涉密等级
//slid转jlid
export const  getJlidBySlidid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglJy/getJlidBySlid", 'get',data)// 涉密等级
//载体借阅登记
export const  addZtglJydj = data => createAPI(BASE_URL+"/ZtglJydj/addZtglJydj", 'post',data)// 涉密等级
//载体借阅登记
export const  selectZtglJydjPage = data => createAPI(BASE_URL+"/ZtglJydj/selectZtglJydjPage", 'get',data)// 涉密等级
//载体借阅登记-修改
export const  updateZtglJydj = data => createAPI(BASE_URL+"/ZtglJydj/updateZtglJydj", 'post',data)// 涉密等级
//slid查询登记载体
export const  seleteZtglJydjByslid = data => createAPI(BASE_URL+"/ZtglJydj/seleteZtglJydjByslid", 'get',data)// 涉密等级
//slid查询登记载体
export const  getCqjyslidByJlid = data => createAPI(BASE_URL+"/ztgl/cqjy/getCqjyslidByJlid", 'get',data)// 涉密等级
