import {createAPI,createDown, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''


//巡检数据初始化
export const getInit = data => createAPI(BASE_URL+"/computerRoomManagement/init", 'post',data)
//巡检机房结果提交
export const saveInspectionComputerRoom = data => createAPI(BASE_URL+"/computerRoomManagement/saveInspectionComputerRoom", 'post',data)
//机柜信息条件查询
export const queryCabinetByCondition = data => createAPI(BASE_URL+"/computerRoomManagement/queryCabinetByCondition", 'post',data)
//巡检机柜结果保存
export const saveInspectionCabinet = data => createAPI(BASE_URL+"/computerRoomManagement/saveInspectionCabinet", 'post',data)
//设备信息条件查询
export const queryEquipmentByCondition = data => createAPI(BASE_URL+"/computerRoomManagement/queryEquipmentByCondition", 'post',data)
//巡检设备结果保存
export const saveInspectionEquipment = data => createAPI(BASE_URL+"/computerRoomManagement/saveInspectionEquipment", 'post',data)
//设备迁移结果提交
export const saveMigrateEquipment = data => createAPI(BASE_URL+"/equipmentManagement/saveMigrateEquipment", 'post',data)
//设备迁移结果导出
export const exportMigrateEquipment = data => createAPI(BASE_URL+"/equipmentManagement/exportMigrateEquipment", 'post',data)
//设备销毁结果信息提交
export const saveDestructionEquipment = data => createAPI(BASE_URL+"/equipmentManagement/saveDestructionEquipment", 'post',data)
//设备销毁结果信息提交
export const exportDestructionEquipment = data => createAPI(BASE_URL+"/equipmentManagement/exportDestructionEquipment", 'post',data)
//设备故障处理初始化
export const getInitFaultHandling = data => createAPI(BASE_URL+"/equipmentManagement/getInitFaultHandling", 'post',data)
//设备故障处理结果提交
export const saveFaultHandling = data => createAPI(BASE_URL+"/equipmentManagement/saveFaultHandling", 'post',data)
//设备故障处理结果导出
export const exportFaultHandling = data => createAPI(BASE_URL+"/equipmentManagement/exportFaultHandling", 'post',data)
//设备故障处理结果导出
export const exportInspectionForm = data => createAPI(BASE_URL+"/computerRoomManagement/exportInspectionForm", 'post',data)
//机房巡检查询列表接口
export const selectInspectionIssueDetails = data => createAPI(BASE_URL+"/computerRoomManagement/selectInspectionIssueDetails", 'post',data)

//设备迁移销毁分页查询
export const getequipmentByRoom = data => createAPI(BASE_URL+"/scanCode/getequipmentByRoom", 'post',data)

//导入文件
export const uploadMessage = data => createAPI(BASE_URL+"/scanCode/uploadMessage", 'post',data)

