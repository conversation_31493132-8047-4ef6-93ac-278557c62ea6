<template>
  <div class="content">
    <div class="bgbg">
      <div
        ref="charts"
        id="main"
        class="bg"
        style="width: calc(100vw * 0.18); height: calc(100vh * 0.185)"
      ></div>
      <div class="pffb"></div>
      <div class="pf1"></div>
      <div class="pf1 pf2"></div>
      <div class="pf1 pf3"></div>
    </div>
  </div>
</template>


<script>
import * as echarts from "echarts";
import { selectPffb } from "../../../../api/dpzs";
import axios from "axios";
// import zhongguo from "@/assets/mapJson/data-city.json"
export default {
  data() {
    return {
      btList: [],
    };
  },
  created() {
    // this.$nextTick(() => {
    //   this.initCharts();
    // });
  },
  props: {
    canClick: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    this.getBt();
  },
  methods: {
    async getBt() {
      let params = {
        citycode: "232700",
      };
      let data = await selectPffb(params);
      this.btList = data;
      this.$nextTick(() => {
        this.initCharts();
      });
    },
    initCharts() {
      var chartDom = document.getElementById("main");
      var myChart = echarts.init(chartDom);
      var option;
      const trafficWay = this.btList;
      // const trafficWay = [
      //   {
      //     name: "85分以上",
      //     value: 100,
      //   },
      //   {
      //     name: "60-85分",
      //     value: 314,
      //   },
      //   {
      //     name: "60分以下",
      //     value: 513,
      //   },
      // ];
      let sum = trafficWay.reduce((cur, pre) => {
        return cur + pre.value;
      }, 0);
      let data = [];
      let legendData = [];
      var color = ["#7FCCFF", "#00BE76", "#FEB501"];
      for (var i = 0; i < trafficWay.length; i++) {
        let name = trafficWay[i].name;
        legendData.push(name);
        data.push(
          {
            value: trafficWay[i].value,
            name: name,
            itemStyle: {
              borderWidth: 0,
              borderRadius: 0,
              shadowBlur: 2,
              borderColor: color[i],
              shadowColor: color[i],
            },
          },
          {
            value: sum / 100, // 控制每个环形之间的间隙
            name: "",
            itemStyle: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              color: "rgba(0, 0, 0, 0)",
              borderColor: "rgba(0, 0, 0, 0)",
              borderWidth: 0,
            },
          }
        );
      }
      let seriesOption = [
        {
          name: "",
          type: "pie",
          clockwise: false,
          radius: ["70%", "87%"],
          center: ["27%", "46.3%"],
          emphasis: {
            scale: false,
          },
          zlevel: 1,
          label: {
            show: false,
          },
          data: data,
        },
      ];
      option = {
        tooltip: {
          show: true,
          trigger: "item",
          formatter: function (params) {
            return params.name + " : " + params.value;
          },
        },
        title: {
          text: "评分分布",
          // subtext: sum,
          textStyle: {
            color: "#fff",
            fontSize: 24,
            padding: [0, 0, 25, 0],
            fontFamily: "YouSheBiaoTiHei",
          },
          // subtextStyle: {
          //   fontSize: 28,
          //   fontWeight: "bolder",
          //   color: "#19E1E3",
          // },
          x: "13.5%",
          y: "54%",
        },
        color: color,
        legend: {
          icon: "rect",
          itemWidth: 2,
          itemHeight: 8,
          itemStyle: {
            borderWidth: 2,
          },
          orient: "vertical",
          data: legendData,
          right: "10%",
          top: "40%",
          align: "left",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontFamily: "SourceHanSansSC-Regular",
            padding: [0, 0, 0, 10],
          },
          itemGap: 25, // 图例之间的间隔
        },
        toolbox: {
          show: false,
        },
        series: seriesOption,
      };

      option && myChart.setOption(option);
      // 关键代码
      // myChart.on("legendselectchanged", (params) => {
      //   // myChart.on("legendselectchanged", function (params) {
      //   myChart.setOption({
      //     legend: { selected: { [params.name]: true } },
      //   });
      //   // let that = this;
      //   console.log("点击了", params);
      //   // do something
      //   this.$emit("valueChanged", params);
      //   // });
      // });

      myChart.off('legendselectchanged') 
      myChart.on("legendselectchanged", (params) => {
        //父组件通过click-legend事件，写真正要实现的点击事件代码
        // this.$emit("valueChanged", {
        //   series: params,
        // });
        this.$emit("valueChanged", params);

        //将默认点击事件中取消选中的legend动态设置回来
        // myChart.setOption({
        //   legend: { selected: { [params.name]: true } },
        // });
      });
    },
  },
};
</script>
<style scoped>
.bgbg {
  width: calc(100vw * 0.18);
  height: calc(100vh * 0.185);
  background: url(../img/pingfenfenbubg.png) no-repeat center;
  background-size: 100% 100%;
  position: relative;
}
.bg {
  position: absolute;
  z-index: 99;
}

.pffb {
  width: calc(100vw * 0.042);
  height: calc(100vh * 0.078);
  background: url(../img/pingfenfenbuico.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: calc(100vh * 0.025);
  left: calc(100vw * 0.027);
  z-index: 99;
}
.pf1 {
  width: calc(100vw * 0.058);
  height: calc(100vh * 0.023);
  background: url(../img/pingfenfenbutuli.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: calc(100vh * 0.073);
  left: calc(100vw * 0.111);
  z-index: 98;
}

.pf2 {
  top: calc(100vh * 0.109);
}
.pf3 {
  top: calc(100vh * 0.145);
}
</style>
