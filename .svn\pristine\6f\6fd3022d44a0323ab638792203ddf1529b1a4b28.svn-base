{"version": 3, "sources": ["webpack:///src/renderer/view/ztqk/gzcllc.vue", "webpack:///./src/renderer/view/ztqk/gzcllc.vue?4686", "webpack:///./src/renderer/view/ztqk/gzcllc.vue"], "names": ["gzcllc", "name", "components", "props", "data", "page", "pageSize", "total", "tableData", "computed", "watch", "methods", "handleCurrentChange", "handleSizeChange", "tableCellStyle", "tableHeaderCellStyle", "handleSelectionChange", "val", "handleEdit", "index", "row", "this", "$router", "push", "path", "query", "equipmentCode", "equipmentId", "equipmentSerialNumber", "handleDelete", "init", "console", "log", "$route", "cabinetCode", "equipmentName", "created", "mounted", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "activated", "ztqk_gzcllc", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "_m", "_v", "staticStyle", "width", "margin-top", "attrs", "header-cell-style", "cell-style", "max-height", "on", "selection-change", "type", "label", "align", "prop", "scopedSlots", "_u", "key", "fn", "scope", "display", "align-items", "justify-content", "click", "$event", "$index", "background", "pager-count", "current-pageNo", "pageNo-sizes", "pageNo-size", "layout", "current-change", "size-change", "staticRenderFns", "src", "__webpack_require__", "alt", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "0IAwEAA,GACAC,KAAA,GAEAC,cACAC,SACAC,KALA,WAOA,OACAC,KAAA,EACAC,SAAA,GACAC,MAAA,EACAC,eAMAC,YAEAC,SAEAC,SAEAC,oBAFA,aAGAC,iBAHA,aAIAC,eAJA,WAKA,8FAEAC,qBAPA,WAQA,kHAEAC,sBAVA,SAUAC,KACAC,WAXA,SAWAC,EAAAC,GACAC,KAAAC,QAAAC,MACAC,KAAA,YACAC,OACAC,cAAAN,EAAAO,YACAC,sBAAAR,EAAAQ,0BAIAC,aApBA,SAoBAV,EAAAC,KACAU,KArBA,WAsBAC,QAAAC,IAAAX,KAAAY,OAAAR,MAAA,sBAEA,IAAArB,GACA8B,YAAAb,KAAAY,OAAAR,MAAAS,YACAP,YAAAN,KAAAY,OAAAR,MAAAE,YACAQ,cAAAd,KAAAY,OAAAR,MAAAU,cACAP,sBAAAP,KAAAY,OAAAR,MAAAG,uBAEAP,KAAAb,UAAAe,KAAAnB,GACAiB,KAAAd,MAAA,IAIA6B,QAxDA,aA0DAC,QA1DA,WA2DAhB,KAAAS,QAGAQ,aA9DA,aAgEAC,YAhEA,aAkEAC,aAlEA,aAoEAC,QApEA,aAsEAC,cAtEA,aAwEAC,UAxEA,aA0EAC,UA1EA,cCrEeC,GADEC,OAFjB,WAA0B,IAAAC,EAAA1B,KAAa2B,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,QAAkBL,EAAAM,GAAA,GAAAN,EAAAO,GAAA,KAAAJ,EAAA,OAAkCK,aAAaC,MAAA,OAAAC,aAAA,UAAoCP,EAAA,YAAiBK,aAAaC,MAAA,QAAeE,OAAQtD,KAAA2C,EAAAvC,UAAAmD,oBAAAZ,EAAAhC,qBAAA6C,aAAAb,EAAAjC,eAAA+C,aAAA,SAAuHC,IAAKC,mBAAAhB,EAAA/B,yBAA8CkC,EAAA,mBAAwBQ,OAAOM,KAAA,YAAAR,MAAA,QAAiCT,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCQ,OAAOM,KAAA,QAAAC,MAAA,KAAAT,MAAA,KAAAU,MAAA,YAA2DnB,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCQ,OAAOS,KAAA,cAAAF,MAAA,OAAAC,MAAA,YAAsDnB,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCQ,OAAOS,KAAA,cAAAF,MAAA,OAAAC,MAAA,YAAsDnB,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCQ,OAAOS,KAAA,gBAAAF,MAAA,OAAAC,MAAA,YAAwDnB,EAAAO,GAAA,KAAAJ,EAAA,mBAAoCQ,OAAOO,MAAA,KAAAC,MAAA,UAA8BE,YAAArB,EAAAsB,KAAsBC,IAAA,UAAAC,GAAA,SAAAC,GAAiC,OAAAtB,EAAA,OAAkBK,aAAakB,QAAA,OAAAC,cAAA,SAAAC,kBAAA,YAAoEzB,EAAA,OAAYE,YAAA,SAAAU,IAAyBc,MAAA,SAAAC,GAAyB,OAAA9B,EAAA7B,WAAAsD,EAAAM,OAAAN,EAAApD,SAAiD2B,EAAAO,GAAA,gDAAsD,GAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA4BK,aAAaE,aAAA,UAAqBP,EAAA,iBAAsBQ,OAAOqB,WAAA,GAAAC,cAAA,EAAAC,iBAAAlC,EAAA1C,KAAA6E,gBAAA,YAAAC,cAAApC,EAAAzC,SAAA8E,OAAA,0CAAA7E,MAAAwC,EAAAxC,OAAyLuD,IAAKuB,iBAAAtC,EAAAnC,oBAAA0E,cAAAvC,EAAAlC,qBAA6E,UAEtlD0E,iBADjB,WAAoC,IAAavC,EAAb3B,KAAa4B,eAA0BC,EAAvC7B,KAAuC8B,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAAA,EAAA,OAAsBQ,OAAO8B,IAAMC,EAAQ,QAAiBC,IAAA,QAAlKrE,KAA8KiC,GAAA,KAAAJ,EAAA,OAA0BE,YAAA,cAAxM/B,KAAgOiC,GAAA,iBCEpQ,IAcAqC,EAdyBF,EAAQ,OAcjCG,CACE5F,EACA6C,GATF,EAVA,SAAAgD,GACEJ,EAAQ,SAaV,kBAEA,MAUeK,EAAA,QAAAH,EAAiB", "file": "js/6.b4b3d26a03ac0e97d8c8.js", "sourcesContent": ["<!--  -->\r\n<template>\r\n  <div class=\"box\">\r\n    <div class=\"top-box\">\r\n      <div>\r\n        <img src=\"./img/title.png\" alt=\"\" />\r\n      </div>\r\n      <div class=\"top-title\">故障处理流程</div>\r\n    </div>\r\n    <div style=\"width: 100%;margin-top: 20px;\">\r\n      <el-table\r\n        :data=\"tableData\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        :header-cell-style=\"tableHeaderCellStyle\"\r\n        :cell-style=\"tableCellStyle\"\r\n        max-height=\"800px\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\"> </el-table-column>\r\n        <el-table-column type=\"index\" label=\"序号\" width=\"60\" align=\"center\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"cabinetCode\" label=\"机柜编号\" align=\"center\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"equipmentId\" label=\"设备编号\" align=\"center\">\r\n        </el-table-column>\r\n        <el-table-column prop=\"equipmentName\" label=\"设备名称\" align=\"center\">\r\n        </el-table-column>\r\n        <!-- <el-table-column prop=\"gzyy\" label=\"故障原因\" align=\"center\">\r\n        </el-table-column> -->\r\n        <el-table-column label=\"操作\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              style=\"display: flex;align-items: center;justify-content: center;\"\r\n            >\r\n              <div class=\"btncz1\" @click=\"handleEdit(scope.$index, scope.row)\">\r\n                处理\r\n              </div>\r\n              <!-- <div\r\n                class=\"btncz1\"\r\n                style=\"margin-left: 20px;\"\r\n                @click=\"handleDelete(scope.$index, scope.row)\"\r\n              >\r\n                忽略\r\n              </div> -->\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div style=\"margin-top: 15px\">\r\n                <el-pagination\r\n                  background\r\n                  @current-change=\"handleCurrentChange\"\r\n                  @size-change=\"handleSizeChange\"\r\n                  :pager-count=\"5\"\r\n                  :current-pageNo=\"page\"\r\n                  :pageNo-sizes=\"[5, 10, 20, 30]\"\r\n                  :pageNo-size=\"pageSize\"\r\n                  layout=\"total, sizes, prev, pager, next, jumper\"\r\n                  :total=\"total\"\r\n                >\r\n                </el-pagination>\r\n              </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { init } from 'echarts';\r\n\r\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\r\n//例如：import 《组件名称》 from '《组件路径》';\r\n\r\nexport default {\r\n  name: \"\",\r\n  //import引入的组件需要注入到对象中才能使用\r\n  components: {},\r\n  props: {},\r\n  data() {\r\n    //这里存放数据\r\n    return {\r\n      page: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      tableData: [\r\n        \r\n      ]\r\n    };\r\n  },\r\n  //监听属性 类似于data概念\r\n  computed: {},\r\n  //监控data中的数据变化\r\n  watch: {},\r\n  //方法集合\r\n  methods: {\r\n    \r\n    handleCurrentChange(){},\r\n    handleSizeChange(){},\r\n    tableCellStyle() {\r\n      return \"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;\";\r\n    },\r\n    tableHeaderCellStyle() {\r\n      return \"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;\";\r\n    },\r\n    handleSelectionChange(val) {},\r\n    handleEdit(index, row) {\r\n      this.$router.push({\r\n        path: \"/gzcllcbz\",\r\n        query: {\r\n          equipmentCode: row.equipmentId,\r\n          equipmentSerialNumber: row.equipmentSerialNumber,\r\n        }\r\n      });\r\n    },\r\n    handleDelete(index, row) {},\r\n    init(){\r\n      console.log(this.$route.query,'this.$router.query');\r\n      \r\n      let data = {\r\n        cabinetCode:this.$route.query.cabinetCode,\r\n        equipmentId:this.$route.query.equipmentId,\r\n        equipmentName:this.$route.query.equipmentName,\r\n        equipmentSerialNumber:this.$route.query.equipmentSerialNumber,\r\n      }\r\n      this.tableData.push(data)\r\n      this.total = 1\r\n    },\r\n  },\r\n  //生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  //生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  //生命周期 - 创建之前\r\n  beforeCreate() {},\r\n  //生命周期 - 挂载之前\r\n  beforeMount() {},\r\n  //生命周期 - 更新之前\r\n  beforeUpdate() {},\r\n  //生命周期 - 更新之后\r\n  updated() {},\r\n  //生命周期 - 销毁之前\r\n  beforeDestroy() {},\r\n  //生命周期 - 销毁完成\r\n  destroyed() {},\r\n  //如果页面有keep-alive缓存功能，这个函数会触发\r\n  activated() {}\r\n};\r\n</script>\r\n<style scoped>\r\n.box {\r\n  width: 1580px;\r\n  margin: 0 auto;\r\n}\r\n.top-box {\r\n  width: 100%;\r\n  display: flex;\r\n  border-bottom: 1px solid #e5e5e5;\r\n  margin-top: 20px;\r\n}\r\n.top-title {\r\n  font-family: SourceHanSansSC-Medium;\r\n  font-size: 22px;\r\n  color: #080808;\r\n  font-weight: 500;\r\n  margin-left: 10px;\r\n}\r\n.mg20 {\r\n  margin-right: 20px;\r\n}\r\n.buttonw {\r\n  width: 72px;\r\n  height: 32px;\r\n  text-align: center;\r\n  line-height: 32px;\r\n  color: #fff;\r\n  border-radius: 4px;\r\n}\r\n.btnc1 {\r\n  background-color: #3e9efe;\r\n}\r\n.btnc2 {\r\n  background-color: #3ecafe;\r\n  margin-left: 20px;\r\n}\r\n.btncz1 {\r\n  font-family: SourceHanSansSC-Normal;\r\n  font-size: 16px;\r\n  color: #1766d1;\r\n  font-weight: 400;\r\n  cursor: pointer;\r\n}\r\n/deep/\r\n  .el-table--enable-row-hover\r\n  .el-table__body\r\n  tr:hover:nth-child(even)\r\n  > td {\r\n  background-color: #dce8fb !important;\r\n}\r\n/deep/\r\n  .el-table--enable-row-hover\r\n  .el-table__body\r\n  tr:hover:nth-child(odd)\r\n  > td {\r\n  background-color: #dce8fb !important;\r\n}\r\n/deep/ .el-table__body tr:nth-child(even) {\r\n  background-color: #dce8fb; /* 偶数行（斑马线）的默认背景色 */\r\n}\r\n/deep/ .el-pagination__total{\r\n  font-size: 16px !important;\r\n}\r\n/deep/ .el-pagination__jump{\r\n  font-size: 16px !important;\r\n}\r\n</style>\r\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/ztqk/gzcllc.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"box\"},[_vm._m(0),_vm._v(\" \"),_c('div',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"20px\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData,\"header-cell-style\":_vm.tableHeaderCellStyle,\"cell-style\":_vm.tableCellStyle,\"max-height\":\"800px\"},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"type\":\"index\",\"label\":\"序号\",\"width\":\"60\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"cabinetCode\",\"label\":\"机柜编号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"equipmentId\",\"label\":\"设备编号\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"prop\":\"equipmentName\",\"label\":\"设备名称\",\"align\":\"center\"}}),_vm._v(\" \"),_c('el-table-column',{attrs:{\"label\":\"操作\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"justify-content\":\"center\"}},[_c('div',{staticClass:\"btncz1\",on:{\"click\":function($event){return _vm.handleEdit(scope.$index, scope.row)}}},[_vm._v(\"\\n              处理\\n            \")])])]}}])})],1),_vm._v(\" \"),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('el-pagination',{attrs:{\"background\":\"\",\"pager-count\":5,\"current-pageNo\":_vm.page,\"pageNo-sizes\":[5, 10, 20, 30],\"pageNo-size\":_vm.pageSize,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.total},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}})],1)],1)])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"top-box\"},[_c('div',[_c('img',{attrs:{\"src\":require(\"./img/title.png\"),\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"top-title\"},[_vm._v(\"故障处理流程\")])])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-67975902\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/ztqk/gzcllc.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-67975902\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./gzcllc.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./gzcllc.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./gzcllc.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-67975902\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./gzcllc.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-67975902\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/gzcllc.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}