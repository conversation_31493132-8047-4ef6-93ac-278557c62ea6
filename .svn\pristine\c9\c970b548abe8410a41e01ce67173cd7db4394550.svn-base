{"version": 3, "sources": ["webpack:///src/renderer/view/ztqk/sbqyxx.vue", "webpack:///./src/renderer/view/ztqk/sbqyxx.vue?0fef", "webpack:///./src/renderer/view/ztqk/sbqyxx.vue"], "names": ["sbqyxx", "name", "components", "props", "data", "form", "jfbh", "jfdd", "sblx", "xbbh", "qysj", "qydz", "relocationCabinetCode", "relocationCabinetName", "relocationComputerRoomName", "relocationInstitution", "relocationLocation", "area", "flag", "sfdc", "computed", "watch", "methods", "queryEquipmentByCondition", "_this", "this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "console", "log", "$route", "query", "equipmentId", "Object", "jfxj", "equipmentCode", "then", "res", "stop", "savetj", "_this2", "_callee2", "_context2", "dc<PERSON><PERSON>", "_this3", "_callee3", "_context3", "$confirm", "cancelButtonClass", "confirmButtonText", "cancelButtonText", "type", "code", "$message", "success", "dom_download", "catch", "abrupt", "content", "fileName", "blob", "Blob", "url", "window", "URL", "createObjectURL", "dom", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "created", "mounted", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "activated", "ztqk_sbqyxx", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "_m", "_v", "staticStyle", "width", "margin", "margin-top", "ref", "attrs", "model", "label-width", "label-position", "label", "disabled", "value", "callback", "$$v", "$set", "expression", "on", "$event", "staticRenderFns", "src", "__webpack_require__", "alt", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "sLAqEAA,cACAC,KAAA,GAEAC,cACAC,SACAC,KALA,WAOA,OACAC,MACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,KAAA,GACAC,sBAAA,GACAC,sBAAA,GACAC,2BAAA,GACAC,sBAAA,GACAC,mBAAA,GACAC,KAAA,IAEAC,KAAA,GACAC,MAAA,IAIAC,YAEAC,SAEAC,SACAC,0BADA,WACA,IAAAC,EAAAC,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,OAAAH,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,OACAC,QAAAC,IACAZ,EAAAa,OAAAC,MAAAC,YACA,mCAGAC,OAAAC,EAAA,EAAAD,EACAE,cAAAlB,EAAAa,OAAAC,MAAAC,cACAI,KAAA,SAAAC,GACAT,QAAAC,IAAAQ,GACApB,EAAAnB,KAAAuC,EAAAxC,KAAA,GACAoB,EAAAnB,KAAAO,sBAAAY,EAAAa,OAAAC,MAAA1B,sBACAY,EAAAnB,KAAAQ,sBAAAW,EAAAa,OAAAC,MAAAzB,sBACAW,EAAAnB,KAAAS,2BAAAU,EAAAa,OAAAC,MAAAxB,2BACAU,EAAAnB,KAAAU,sBAAAS,EAAAa,OAAAC,MAAAvB,sBACAS,EAAAnB,KAAAW,mBAAAQ,EAAAa,OAAAC,MAAAtB,mBACAQ,EAAAnB,KAAAY,KAAAO,EAAAa,OAAAC,MAAArB,KACAO,EAAAN,KAAAM,EAAAa,OAAAC,MAAApB,OAjBA,wBAAAc,EAAAa,SAAAf,EAAAN,KAAAE,IAoBAoB,OArBA,WAqBA,IAAAC,EAAAtB,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAmB,IAAA,OAAArB,EAAAC,EAAAG,KAAA,SAAAkB,GAAA,cAAAA,EAAAhB,KAAAgB,EAAAf,MAAA,wBAAAe,EAAAJ,SAAAG,EAAAD,KAAArB,IACAwB,SAtBA,WAsBA,IAAAC,EAAA1B,KAAA,OAAAC,IAAAC,EAAAC,EAAAC,KAAA,SAAAuB,IAAA,OAAAzB,EAAAC,EAAAG,KAAA,SAAAsB,GAAA,cAAAA,EAAApB,KAAAoB,EAAAnB,MAAA,UACA,KAAAiB,EAAAjC,KADA,CAAAmC,EAAAnB,KAAA,eAGAiB,EAAAG,SAAA,gCACAC,kBAAA,oBACAC,kBAAA,KACAC,iBAAA,KACAC,KAAA,YAEAf,KAAA,WACAH,OAAAC,EAAA,EAAAD,CAAAW,EAAA9C,MAAAsC,KAAA,SAAAC,GACA,KAAAA,EAAAe,OACAR,EAAAS,SAAAC,QAAA,QACAV,EAAAjC,KAAA,IACAsB,OAAAC,EAAA,EAAAD,CAAAW,EAAA9C,MAAAsC,KAAA,SAAAC,GACAO,EAAAW,aAAAlB,EAAA,qBAIAmB,MAAA,cAnBAV,EAAAW,OAAA,iBAwBAxB,OAAAC,EAAA,EAAAD,CAAAW,EAAA9C,MAAAsC,KAAA,SAAAC,GACA,KAAAA,EAAAe,OACAR,EAAAS,SAAAC,QAAA,QACAV,EAAAjC,KAAA,IACAsB,OAAAC,EAAA,EAAAD,CAAAW,EAAA9C,MAAAsC,KAAA,SAAAC,GACAO,EAAAW,aAAAlB,EAAA,mBA7BA,wBAAAS,EAAAR,SAAAO,EAAAD,KAAAzB,IAoCAoC,aA1DA,SA0DAG,EAAAC,GACA,IAAAC,EAAA,IAAAC,MAAAH,IAEAI,EAAAC,OAAAC,IAAAC,gBAAAL,GACAM,EAAAC,SAAAC,cAAA,KACAxC,QAAAC,IAAA,MAAAqC,GACAA,EAAAG,MAAAC,QAAA,OACAJ,EAAAK,KAAAT,EACAI,EAAAM,aAAA,WAAAb,GACAQ,SAAAM,KAAAC,YAAAR,GACAA,EAAAS,UAIAC,QAvGA,aAyGAC,QAzGA,WA0GA3D,KAAAF,6BAGA8D,aA7GA,aA+GAC,YA/GA,aAiHAC,aAjHA,aAmHAC,QAnHA,aAqHAC,cArHA,aAuHAC,UAvHA,aAyHAC,UAzHA,eClEeC,GADEC,OAFjB,WAA0B,IAAAC,EAAArE,KAAasE,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,QAAkBL,EAAAM,GAAA,GAAAN,EAAAO,GAAA,KAAAJ,EAAA,OAAkCK,aAAaC,MAAA,MAAAC,OAAA,SAAAC,aAAA,UAAqDR,EAAA,WAAgBS,IAAA,OAAAC,OAAkBC,MAAAd,EAAAzF,KAAAwG,cAAA,QAAAC,iBAAA,UAAgEb,EAAA,OAAYK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,iBAAA6G,SAAA,SAAAC,GAA2DrB,EAAAsB,KAAAtB,EAAAzF,KAAA,mBAAA8G,IAA4CE,WAAA,4BAAqC,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,iBAAA6G,SAAA,SAAAC,GAA2DrB,EAAAsB,KAAAtB,EAAAzF,KAAA,mBAAA8G,IAA4CE,WAAA,4BAAqC,OAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAgCK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,IAAA6G,SAAA,SAAAC,GAA8CrB,EAAAsB,KAAAtB,EAAAzF,KAAA,MAAA8G,IAA+BE,WAAA,eAAwB,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,cAAA6G,SAAA,SAAAC,GAAwDrB,EAAAsB,KAAAtB,EAAAzF,KAAA,gBAAA8G,IAAyCE,WAAA,yBAAkC,OAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAgCK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,WAAiBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,mBAAA6G,SAAA,SAAAC,GAA6DrB,EAAAsB,KAAAtB,EAAAzF,KAAA,qBAAA8G,IAA8CE,WAAA,8BAAuC,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,YAAkBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,sBAAA6G,SAAA,SAAAC,GAAgErB,EAAAsB,KAAAtB,EAAAzF,KAAA,wBAAA8G,IAAiDE,WAAA,iCAA0C,OAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAgCK,aAAazB,QAAA,UAAkBoB,EAAA,gBAAqBE,YAAA,OAAAQ,OAA0BI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,2BAAA6G,SAAA,SAAAC,GAAqErB,EAAAsB,KAAAtB,EAAAzF,KAAA,6BAAA8G,IAAsDE,WAAA,sCAA+C,GAAAvB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCU,OAAOI,MAAA,UAAgBd,EAAA,YAAiBU,OAAOK,SAAA,IAAcJ,OAAQK,MAAAnB,EAAAzF,KAAA,sBAAA6G,SAAA,SAAAC,GAAgErB,EAAAsB,KAAAtB,EAAAzF,KAAA,wBAAA8G,IAAiDE,WAAA,iCAA0C,aAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAAsCK,aAAazB,QAAA,OAAA0B,MAAA,QAAAC,OAAA,SAAAC,aAAA,UAAwER,EAAA,OAAYE,YAAA,gBAAAmB,IAAgCpC,MAAA,SAAAqC,GAAyB,OAAAzB,EAAA5C,eAAwB4C,EAAAO,GAAA,gBAElsFmB,iBADjB,WAAoC,IAAazB,EAAbtE,KAAauE,eAA0BC,EAAvCxE,KAAuCyE,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAAA,EAAA,OAAsBU,OAAOc,IAAMC,EAAQ,QAAiBC,IAAA,QAAlKlG,KAA8K4E,GAAA,KAAAJ,EAAA,OAA0BE,YAAA,cAAxM1E,KAAgO4E,GAAA,iBCEpQ,IAcAuB,EAdyBF,EAAQ,OAcjCG,CACE7H,EACA4F,GATF,EAVA,SAAAkC,GACEJ,EAAQ,SAaV,kBAEA,MAUeK,EAAA,QAAAH,EAAiB", "file": "js/7.6d32dc6049ce91385b54.js", "sourcesContent": ["<!--  -->\n<template>\n  <div class=\"box\">\n    <div class=\"top-box\">\n      <div>\n        <img src=\"./img/title.png\" alt=\"\" />\n      </div>\n      <div class=\"top-title\">设备迁移信息</div>\n    </div>\n    <div style=\"width: 60%;margin: 0 auto;margin-top: 20px;\">\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        label-width=\"140px\"\n        label-position=\"left\"\n      >\n        <div style=\"display: flex;\">\n          <el-form-item label=\"机房编号\" class=\"mg20\">\n            <el-input v-model=\"form.computerRoomCode\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"机房地点\">\n            <el-input v-model=\"form.computerRoomName\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <div style=\"display: flex;\">\n          <el-form-item label=\"设备类型\" class=\"mg20\">\n            <el-input v-model=\"form.csm\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"设备编号\">\n            <el-input v-model=\"form.equipmentCode\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <div style=\"display: flex;\">\n          <el-form-item label=\"迁移所在地\" class=\"mg20\">\n            <el-input v-model=\"form.relocationLocation\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"迁移所在机构\">\n            <el-input v-model=\"form.relocationInstitution\" disabled></el-input>\n          </el-form-item>\n        </div>\n        <div style=\"display: flex;\">\n          <el-form-item label=\"迁移机房\" class=\"mg20\">\n            <el-input\n              v-model=\"form.relocationComputerRoomName\"\n              disabled\n            ></el-input>\n          </el-form-item>\n          <el-form-item label=\"迁移机柜\">\n            <el-input v-model=\"form.relocationCabinetName\" disabled></el-input>\n          </el-form-item>\n        </div>\n      </el-form>\n    </div>\n    <div style=\"display: flex;width: 160px;margin: 0 auto;margin-top: 20px;\">\n      <!-- <div class=\"buttonw btnc1\" @click=\"savetj()\">提交</div> -->\n      <div class=\"buttonw btnc2\" @click=\"dcbutton()\">提交并导出</div>\n    </div>\n  </div>\n</template>\n\n<script>\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n//例如：import 《组件名称》 from '《组件路径》';\nimport {\n  queryEquipmentByCondition,\n  saveMigrateEquipment,\n  exportMigrateEquipment\n} from \"../../../api/jfxj\";\nimport {removeFmwlsb} from \"../../../api\";\nexport default {\n  name: \"\",\n  //import引入的组件需要注入到对象中才能使用\n  components: {},\n  props: {},\n  data() {\n    //这里存放数据\n    return {\n      form: {\n        jfbh: \"\",\n        jfdd: \"\",\n        sblx: \"\",\n        xbbh: \"\",\n        qysj: \"\",\n        qydz: \"\",\n        relocationCabinetCode: \"\",\n        relocationCabinetName: \"\",\n        relocationComputerRoomName: \"\",\n        relocationInstitution: \"\",\n        relocationLocation: \"\",\n        area: \"\",\n      },\n      flag: \"\",\n      sfdc: false\n    };\n  },\n  //监听属性 类似于data概念\n  computed: {},\n  //监控data中的数据变化\n  watch: {},\n  //方法集合\n  methods: {\n    async queryEquipmentByCondition() {\n      console.log(\n        this.$route.query.equipmentId,\n        \"this.$route.query.equipmentCode\"\n      );\n\n      queryEquipmentByCondition({\n        equipmentCode: this.$route.query.equipmentId\n      }).then(res => {\n        console.log(res);\n        this.form = res.data[0];\n        this.form.relocationCabinetCode = this.$route.query.relocationCabinetCode;\n        this.form.relocationCabinetName = this.$route.query.relocationCabinetName;\n        this.form.relocationComputerRoomName = this.$route.query.relocationComputerRoomName;\n        this.form.relocationInstitution = this.$route.query.relocationInstitution;\n        this.form.relocationLocation = this.$route.query.relocationLocation;\n        this.form.area = this.$route.query.area;\n        this.flag = this.$route.query.flag;\n      });\n    },\n    async savetj() {},\n    async dcbutton() {\n      if (this.flag == '1') {\n        // 回抛退出事件给父组件，用以退出系统\n        this.$confirm('目前设备所在机柜与迁移机柜一致，是否确认提交？', '提示', {\n          cancelButtonClass: \"btn-custom-cancel\",\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning',\n          // center: true\n        }).then(() => {\n          saveMigrateEquipment(this.form).then(res => {\n            if (res.code == 10000) {\n              this.$message.success(\"提交成功\");\n              this.flag = '1';\n              exportMigrateEquipment(this.form).then(res => {\n                this.dom_download(res, \"设备迁移信息\" + \".xls\");\n              });\n            }\n          });\n        }).catch(() => {\n        })\n        return\n      }\n      else {\n        saveMigrateEquipment(this.form).then(res => {\n          if (res.code == 10000) {\n            this.$message.success(\"提交成功\");\n            this.flag = '1';\n            exportMigrateEquipment(this.form).then(res => {\n              this.dom_download(res, \"设备迁移信息\" + \".xls\");\n            });\n          }\n        });\n      }\n    },\n    //处理下载流\n    dom_download(content, fileName) {\n      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\n      //console.log(blob)\n      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象\n      let dom = document.createElement(\"a\"); //设置一个隐藏的a标签，href为输出流，设置download\n      console.log(\"dom\", dom);\n      dom.style.display = \"none\";\n      dom.href = url;\n      dom.setAttribute(\"download\", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\n      document.body.appendChild(dom);\n      dom.click();\n    }\n  },\n  //生命周期 - 创建完成（可以访问当前this实例）\n  created() {},\n  //生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n    this.queryEquipmentByCondition();\n  },\n  //生命周期 - 创建之前\n  beforeCreate() {},\n  //生命周期 - 挂载之前\n  beforeMount() {},\n  //生命周期 - 更新之前\n  beforeUpdate() {},\n  //生命周期 - 更新之后\n  updated() {},\n  //生命周期 - 销毁之前\n  beforeDestroy() {},\n  //生命周期 - 销毁完成\n  destroyed() {},\n  //如果页面有keep-alive缓存功能，这个函数会触发\n  activated() {}\n};\n</script>\n<style scoped>\n.box {\n  width: 1580px;\n  margin: 0 auto;\n}\n.top-box {\n  width: 100%;\n  display: flex;\n  border-bottom: 1px solid #e5e5e5;\n  margin-top: 20px;\n}\n.top-title {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n  margin-left: 10px;\n}\n.mg20 {\n  margin-right: 20px;\n}\n.buttonw {\n  cursor: pointer;\n  width: 123px;\n  height: 32px;\n  text-align: center;\n  line-height: 32px;\n  color: #fff;\n  border-radius: 4px;\n  cursor: pointer;\n}\n.btnc1 {\n  background-color: #3e9efe;\n}\n.btnc2 {\n  background-color: #3ecafe;\n  margin-left: 20px;\n}\n/deep/ .el-form-item__content {\n  width: 350px !important;\n}\n/deep/ .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-form--label-left .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-input.is-disabled .el-input__inner{\n  font-size: 18px;\n}\n</style>\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/ztqk/sbqyxx.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"box\"},[_vm._m(0),_vm._v(\" \"),_c('div',{staticStyle:{\"width\":\"60%\",\"margin\":\"0 auto\",\"margin-top\":\"20px\"}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"label-width\":\"140px\",\"label-position\":\"left\"}},[_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"机房编号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.computerRoomCode),callback:function ($$v) {_vm.$set(_vm.form, \"computerRoomCode\", $$v)},expression:\"form.computerRoomCode\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"机房地点\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.computerRoomName),callback:function ($$v) {_vm.$set(_vm.form, \"computerRoomName\", $$v)},expression:\"form.computerRoomName\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"设备类型\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.csm),callback:function ($$v) {_vm.$set(_vm.form, \"csm\", $$v)},expression:\"form.csm\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"设备编号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.equipmentCode),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentCode\", $$v)},expression:\"form.equipmentCode\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"迁移所在地\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.relocationLocation),callback:function ($$v) {_vm.$set(_vm.form, \"relocationLocation\", $$v)},expression:\"form.relocationLocation\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"迁移所在机构\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.relocationInstitution),callback:function ($$v) {_vm.$set(_vm.form, \"relocationInstitution\", $$v)},expression:\"form.relocationInstitution\"}})],1)],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\"}},[_c('el-form-item',{staticClass:\"mg20\",attrs:{\"label\":\"迁移机房\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.relocationComputerRoomName),callback:function ($$v) {_vm.$set(_vm.form, \"relocationComputerRoomName\", $$v)},expression:\"form.relocationComputerRoomName\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"迁移机柜\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.relocationCabinetName),callback:function ($$v) {_vm.$set(_vm.form, \"relocationCabinetName\", $$v)},expression:\"form.relocationCabinetName\"}})],1)],1)])],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"160px\",\"margin\":\"0 auto\",\"margin-top\":\"20px\"}},[_c('div',{staticClass:\"buttonw btnc2\",on:{\"click\":function($event){return _vm.dcbutton()}}},[_vm._v(\"提交并导出\")])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"top-box\"},[_c('div',[_c('img',{attrs:{\"src\":require(\"./img/title.png\"),\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"top-title\"},[_vm._v(\"设备迁移信息\")])])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-2fadf3c1\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/ztqk/sbqyxx.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-2fadf3c1\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./sbqyxx.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./sbqyxx.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./sbqyxx.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-2fadf3c1\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./sbqyxx.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-2fadf3c1\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/sbqyxx.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}