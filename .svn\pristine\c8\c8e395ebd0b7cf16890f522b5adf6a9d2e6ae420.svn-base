import {createAPI, createDown,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
// 一键生成
export const downloadZipYjcl = data => createDown(BASE_URL+"/dbgz/exportYjgzZip", 'post',data)
// 定密事项上报数据导出
export const getAllNowYearLengthDatas = data => createAPI(BASE_URL+"/dmb/yjgz/getYjgzCount", 'get',data)
// 迎检工作自检评分数据
export const getAllZjpfStatus = data => createAPI(BASE_URL+"/yjgz/zjpf/getZjpf", 'get',data)
// 获取全部保密制度
export const getAllBmzd = data => createAPI(BASE_URL+"/bmzt/djb/getAllBmzd", 'get',data)
// 更新状态以及分数
export const updateStatusScores = data => createDown(BASE_URL+"/yjgz/zjpf/updateZjpf", 'post',data)
// 获取涉密人员的培训课时
export const getPxKsBySmryId = data => createAPI(BASE_URL+"/jypx/rypxdj/getPxksById", 'get',data)

// 获取迎检工作保密制度检查项
export const getBmzdCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifyBmzd", 'get',data)
// 获取迎检工作组织机构检查项
export const getZzjgCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifyZzjg", 'get',data)
// 获取迎检工作涉密人员检查项
export const getSmryCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifySmry", 'get',data)
// 获取迎检工作涉密场所检查项
export const getSmcsCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifySmcs", 'get',data)
// 获取迎检工作涉密设备检查项
export const getSmsbCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifySmsb", 'get',data)
// 获取迎检工作涉密载体检查项
export const getSmztCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifySmzt", 'get',data)
// 获取迎检工作教育培训检查项
export const getJypxCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/verifyJypx", 'get',data)
// 获取迎检工作初始化总分数
export const getAllcountsCheckdatas = data => createAPI(BASE_URL+"/yjgz/zjpf/computeTotalFs", 'get',data)
// 获取迎检工作单项分数
export const getSingleScore = data => createAPI(BASE_URL+"/yjgz/zjpf/updateFs", 'get',data)
// 获取迎检工作总分数
export const getYjgzfs = data => createAPI(BASE_URL+"/yjgz/zjpf/getYjgzfs", 'get',data)
// 获取迎检工作忽略全部
export const getIgnoreAllDatas = data => createAPI(BASE_URL+"/yjgz/zjpf/IgnoreAll", 'get',data)
// 获取迎检工作忽略单条
export const getSingleIgnore = data => createAPI(BASE_URL+"/yjgz/zjpf/IgnoreOne", 'get',data)