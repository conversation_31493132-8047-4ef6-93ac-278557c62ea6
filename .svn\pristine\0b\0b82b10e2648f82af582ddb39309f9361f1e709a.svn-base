
//处理下载流
 export function dom_download(content,fileName){
	const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
  //console.log(blob)
	const url = window.URL.createObjectURL(blob)//URL.createObjectURL(object)表示生成一个File对象或Blob对象
	let dom = document.createElement('a')//设置一个隐藏的a标签，href为输出流，设置download
	dom.style.display = 'none'
	dom.href = url
	dom.setAttribute('download',fileName)//指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
	document.body.appendChild(dom)
	dom.click()
}

//处理预览PDF
 export function dom_preview(content){
	let blob = new Blob([content], {
		type: 'application/pdf;chartset=UTF-8'
	})
  console.log(blob)
	let fileURL= window.URL.createObjectURL(blob)
	window.open(fileURL)
}

//处理预览图片
 export function dom_picture(content){
	let blob = new Blob([content], { type:"image/png"})
  console.log(blob)
	let fileURL= window.URL.createObjectURL(blob)
	window.open(fileURL)
}
