import {createAPI, createFileAPI,createDown,createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
// 通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
// 根据原jlid查询设备清单
export const getSbqdListByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/getSbqdListByYjlid", 'get',data)
// 设备清单批量添加
export const savaSbqdBatch = data => createAPI(BASE_URL+"/sbgl/sbqd/savaSbqdBatch", 'post',data)
// 根据原jlid删除原jlid下的设备清单
export const deleteSbqdByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/deleteSbqdByYjlid", 'post',data)

//日常工作
//添加设备携带外出
export const submitSbwx = data => createAPI(BASE_URL+"/sbgl-sbwx/submitSbwx", 'post',data)
//修改设备维修
export const updateSbwx = data => createAPI(BASE_URL+"/sbgl-sbwx/updateSbwx", 'post',data)
// 查找带分页
export const selectSbwxPage = data => createAPI(BASE_URL+"/sbgl-sbwx/selectSbwxPage", 'get',data)
//删除
export const removeSbwx = data => createAPI(BASE_URL+"/sbgl-sbwx/removeSbwx", 'post',data)
// 根据jlid查询
export const getSbwxInfo = data => createAPI(BASE_URL+"/sbgl-sbwx/getSbwxInfo", 'get',data)
// 查询所有不带分页
export const getAllSbwx = data => createAPI(BASE_URL+"/sbgl-sbwx/getAllSbwx", 'get',data)
// 根据slid查询计算机系统维护
export const getSbwxInfoBySlid = data => createAPI(BASE_URL+"/sbgl-sbwx/getSbwxInfoBySlid", 'get',data)
// 根据slid获取jlid
export const getJlid = data => createAPI(BASE_URL+"/sbgl-sbwx/getJlid", 'get',data)
export const getsbwxJlid = data => createAPI(BASE_URL+"/sbgl-sbwx/getJlid", 'get',data)

//登记表
// 添加设备维修登记
export const submitSbwxdj = data => createAPI(BASE_URL+"/sbgl-sbwxdj/submitSbwxdj", 'post',data)
//设备维修的登记查询带分页
export const selectSbwxdjPage = data => createAPI(BASE_URL+"/sbgl-sbwxdj/selectSbwxdjPage", 'get',data)
// 根据jlid获取设备维修登记信息详情
export const getSbwxdjInfo = data => createAPI(BASE_URL+"/sbgl-sbwxdj/getSbwxdjInfo", 'get',data)
// 修改维修记录
export const updateSbwxdj = data => createAPI(BASE_URL+"/sbgl-sbwxdj/updateSbwxdj", 'post',data)

//下载设备维修保密协议登记书
export const download = data => createDown(BASE_URL+"/sbgl-sbwx/download", 'post',data)

