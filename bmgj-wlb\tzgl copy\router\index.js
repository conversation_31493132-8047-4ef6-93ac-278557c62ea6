export default [
  {
    name: 'tzgltabs',
    path: '/tzgltabs',
    component: () => import('../tzgltabs.vue'),
    meta: {
      name: '台账管理',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true,
      menuList: ['/bmzd', '/pxqd','/smgwgl', '/aqcp']
    }
  },
  {
    name: 'aqcp',
    path: '/aqcp',
    component: () => import('../aqcp.vue'),
    meta: {
      name: '安全产品',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'bmzd',
    path: '/bmzd',
    component: () => import('../bmzd.vue'),
    meta: {
      name: '保密制度',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'zzjg',
    path: '/zzjg',
    component: () => import('../zzjg.vue'),
    meta: {
      name: '机构管理',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
  {
    name: 'smgwgl',
    path: '/smgwgl',
    component: () => import('../smgwgl.vue'),
    meta: {
      name: '涉密岗位',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: true
    }
  },
]
// export default [
//   {
//     name: 'tzglsy',
//     path: '/tzglsy',
//     component: () => import('../tzgltabs.vue'),
//     meta: {
//       name: '台账管理',
//       icon: 'aaa',
//       hidden: false,
//       showHeaderMenu: true,
//       showAsideMenu: true,
//       menuList: ['/bmzd', '/dmzrr','/dmsq','/zzjg','/zzgl', '/pxqd','/smgwgl', '/smry', '/ryxz', '/gwbg', '/lglz','/smwlsb','/fmwlsb', '/csgl', '/csbg','/smjsj','/fsmjsj','/smydccjz','/smbgzdhsb','/fsmbgzdhsb','/aqcp',"/smzttz",'/gjmmsx','/dmpx','/dmqkndtj','/bmqsxqdqk','/zfcgxmqk',]
      
//     }
//   },
// ]