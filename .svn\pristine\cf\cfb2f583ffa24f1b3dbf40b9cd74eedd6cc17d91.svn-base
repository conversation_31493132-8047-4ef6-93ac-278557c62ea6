<template>
	<div>
		<div class="header-wrapper">
			<div class="h-main clearfix" style="height: 67px;">
				<img src="./images/logo60-60.png" style="height: 60px;margin: -5px 0 0 10px">
				<div style="letter-spacing: 5px;font-size: 28px; color: #484848; font-weight: bold; margin:-50px 0px 0px 80px;">人类遗传资源信息备份平台</div>
			</div>
		</div>
		<form name="LoginForm" id="LoginForm" action="" method="post">
			<div class="indexLogo">
				<div class="indexR" style="height: 270px;">

					<div class="content">
						<div class="regform-use" id="userId">
							
							<div class="clearkit" style="height:38px;"></div>
							<div class="zsdlinput">
								<span class="fl"><img src="./images/person.png" width="21" height="23" align="absmiddle" style="padding:8px 10px 0 7px;"></span>
								<span class="fl se_wai"><input type="text" v-model="loginName" name="loginName" id="loginName" class="dlinput" placeholder="用户名"></span></div>
							<div class="clearkit" style="height:28px;"></div>
							<div class="zsdlinput">
								<span class="fl"><img src="./images/suo.png" width="20" height="24" align="absmiddle" style="padding:8px 10px 0 7px;"></span>
								<span class="fl se_wai"><input type="password" v-model="password" name="password" id="password" class="dlinput" placeholder="密码" autocomplete="off" @keyup.enter="loginIt"></span>
							</div>
							<!-- <div class="clearkit" style="height:28px;"></div>
							<div class="zsdlinput">
								<span class="fl"><img src="./images/yanzhengma.png" width="20" height="24" align="absmiddle" style="padding:8px 10px 0 7px;"></span>
								<span class="fl se_wai" style="width:120px;">
									<input type="text" style="width:120px;" name="validateCode" id="validateCode" class="dlinput" placeholder="验证码" onkeydown="javascript:butOnClick();">
								</span>
								<img id="kaptchaImage" onclick="getKaptcha()" src="/am/validate.code" style="float:right;cursor: pointer;" alt="看不清，换一张" title="看不清，换一张">
							</div> -->
							<div class="qing269" style="margin-top:10px;">
								<span class="fl" style="margin-left: 10px;">
									<a href="javascript:void(0)">忘记密码？</a>
								</span>
								<span class="fr" style="margin-right: 10px;">
									<router-link to="register">点击注册</router-link>
								</span>
							</div>
							<div class="dlinputd"><input style="margin-top:10px;" type="button" value="登录"  @click="loginIt" ></div>
							<div v-if="errorMsg" class="msg" style="margin-top:10px;text-align: center;"><a href="javascript:void(0)">{{errorMsg}}</a></div>
						</div>
					</div>
				</div>
			</div>
		</form>
		<div class="footer-wrapper">
			<div class="f-footer clearfix">
				
				<div class="f-f-des">
					<div class="f-links">
						</div>
					<div class="f-zhuban">
						<span>业务电话：010-88225151</span> <span>技术支持热线：101-88654022</span> <span>电子邮箱：<EMAIL></span>
					</div>
					<div class="f-zhuban">
						<span>主办：中国生物技术发展中心</span> <span>国家互联网应急中心</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	import {reqLogin} from '../../api/index.js'
	
	export default {
		data() {
			return {
				loginName : '',
				password : '',
				errorMsg: '',
			};
		},
	  methods: {
	    async loginIt() {
			
			if (!this.loginName || !this.password){
				this.errorMsg = "用户名或密码不能为空!"
				return;
			}

			const data = await reqLogin(this.loginName,this.password)
			
			if (data.success == 0){
				this.errorMsg = "用户名或密码错误!"
			}else{
				sessionStorage.setItem('accessToken' ,new Date().getTime())
				sessionStorage.setItem('xm' , data.user.xm)
				sessionStorage.setItem('dw' , data.user.dw)
				this.$router.push("main")
			}
			//
		}
	  },
	  mounted () {
	    
	  },
	  components: {
	    
	  }
	}
</script>

<style scoped="scoped">
.header-wrapper {
    width: 100%;
    background: #fff;
    border-top: 1px solid #fff;
}
.header-wrapper .h-main {
    width: 1000px;
    margin: 24px auto 8px;
}
.clearfix:after, .clearfix:before {
    display: table;
    line-height: 0;
    content: "";
}
.clearfix:after {
    clear: both;
}
.indexLogo {
    width: 100%;
    min-width: 1000px;
    height: 480px;
    /* background: url(./images/BG2.png) no-repeat center top; */
    /**透明色**/
    background: rgba(255, 0, 0, 0);
    background-size: cover;
    clear: both;
}
.indexR {
    width: 365px;
    height: 382px;
    margin: 86px 200px 0 0;
    float: right;
    background: rgba(255,255,255,0.8);
    background: #fff\9;
}
.content {
    float: left;
    padding-left: 52px;
    width: 343px;
}
.regform-use {
    border: 0 none;
    float: right;
    width: 274px;
    height: 363px;
    margin: 0 77px 0 0;
}
.clearkit {
    clear: both;
    font-size: 0px;
    line-height: 0px;
    height: 0px;
}
.zsdlinput {
    border: #d3d5d6 1px solid;
    width: 271px;
    height: 35px;
    background: #fff;
}
span.fl {
    float: left;
    display: inline-block;
    padding-top: 2px;
}
.fr{
	float: right;
}


.se_wai {
    border: 0;
    border-left: 0px;
    height: 34px;
    width: 230px;
    overflow: hidden;
    background: #fff\9;
}
.dlinput {
    border: 0;
    height: 40px;
    line-height: 40px;
    width: 228px;
}
.qing269 {
    color: #2695fe;
}
.qing269 a {
    color: #316ba2;
}
.msg a {
    color: #D30B15;
}
.dlinputd input {
    border: 0px;
    background: #b02323;
    width: 274px;
    height: 40px;
    line-height: 40px;
    color: #fff;
    margin-top: 20px;
    cursor: pointer;
}
.footer-wrapper {
    border-top: 8px solid #e61b21;
    background: #fff;
    border-bottom: 1px solid #fff;
}
.footer-wrapper .f-footer {
    width: 1000px;
    margin: 14px auto;
	margin-top: 40px;
}
.clearfix {
    zoom: 1;
}
.footer-wrapper .f-footer>div {
    text-align: center;
}
.footer-wrapper .f-footer .f-f-icon a:first-child {
    margin-top: 14px;
    margin-left: 40px;
}
.footer-wrapper .f-footer .f-f-icon a {
    display: block;
    float: left;
    margin: 0;
}
.footer-wrapper .f-footer .f-links ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.footer-wrapper .f-footer .f-links ul li {
    list-style: none;
    float: left;
    margin: 0;
    padding: 5px 0;
}
.footer-wrapper .f-footer .f-links ul li a {
    color: #282828;
    text-decoration: none;
    font-weight: 700;
    padding: 0 13px;
    font-size: 14px;
    position: relative;
}
.footer-wrapper .f-footer .f-zhuban {
    font-size: 12px;
    padding: 5px;
}
.footer-wrapper .f-footer .f-zhuban:last-child {
    color: #3c6f99;
}
.footer-wrapper .f-footer .f-zhuban span {
    margin: 0 8px;
    height: 20px;
    line-height: 20px;
}
.footer-wrapper .f-footer .f-zhuban span {
    margin: 0 8px;
    height: 20px;
    line-height: 20px;
}

</style>
