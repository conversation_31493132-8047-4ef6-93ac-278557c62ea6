const FS = require('fs')

// 不可取方法
// import { createDirectoryWindows } from '../../utils/pathUtil'
// createDirectoryWindows('C:\\hsoft\\zczp.json')

let path = 'C:\\hsoft\\data\\database\\system.json'
let pathArr
let separator = '\\'
pathArr = path.split(separator)
// console.log('pathArr', pathArr)
// 获取最后一个拆分的字符串，判断是否应该丢弃
let last = pathArr[pathArr.length - 1]
if (last == '' || last.indexOf('.') != -1) {
  pathArr.length -= 1
}
// console.log('pathArr', pathArr)
let cumulativePath = ''
pathArr.some((item) => {
  cumulativePath += item + separator
  // console.log(cumulativePath, FS.existsSync(cumulativePath))
  if (FS.existsSync(cumulativePath)) {
    // console.log(cumulativePath, '存在')
  } else {
    // console.log(cumulativePath, '不存在')
    try {
      FS.mkdirSync(cumulativePath)
    } catch (error) {
      // console.log(error)
      // 发生异常，结束循环
      throw errorProcessor(
        error,
        '请使用更高权限运行程序或手动创建目录：\n' + path
      )
    }
  }
})

