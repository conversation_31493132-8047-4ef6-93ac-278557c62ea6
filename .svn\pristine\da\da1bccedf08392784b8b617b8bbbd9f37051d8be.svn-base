export default [
  {
    name: 'ztqksy',
    path: '/ztqksy',
    component: () => import('../ztqktabs.vue'),
    meta: {
      name: '总体情况',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'sbqyxx',
    path: '/sbqyxx',
    component: () => import('../sbqyxx.vue'),
    meta: {
      name: '设备迁移信息',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'sbxhxx',
    path: '/sbxhxx',
    component: () => import('../sbxhxx.vue'),
    meta: {
      name: '设备销毁信息',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'gzcllc',
    path: '/gzcllc',
    component: () => import('../gzcllc.vue'),
    meta: {
      name: '故障处理流程',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'gzcllcbz',
    path: '/gzcllcbz',
    component: () => import('../gzcllcbz.vue'),
    meta: {
      name: '故障处理流程步骤',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'xjlc',
    path: '/xjlc',
    component: () => import('../xjlc.vue'),
    meta: {
      name: '巡检流程步骤',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'xjlclb',
    path: '/xjlclb',
    component: () => import('../xjlclb.vue'),
    meta: {
      name: '巡检流程异常',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  },
  {
    name: 'ckjg',
    path: '/ckjg',
    component: () => import('../ckjg.vue'),
    meta: {
      name: '查看结果',
      icon: 'aaa',
      hidden: false,
      showHeaderMenu: true,
      showAsideMenu: false,
      requireAuth: true
    }
  }
]