import {
  createAPI,
  createDown,
  createFileAPI,
  createUploadAPI,
  BASE_URL
} from "./request";
// var BASE_URL = '/api'
// var BASE_URL = ''

//导出excel表格例子
// export const daochuExcel = data => createDown(BG_URL + '/getRbExcel', 'post', data);

//涉密岗位信息导出excel
export const exportSmgwData = data => createDown(BASE_URL + "/rygl/gwdj/exportSmgwData", "get", data);
//在岗涉密人员信息导出excel
export const exportSmryData = data => createDown(BASE_URL + "/rygl/yhxx/exportSmryData", "get", data);
//密级变更记录导出excel
export const exportMjbgData = data => createDown(BASE_URL + "/rygl/mjbg/exportMjbgData", "get", data);
//密级变更记录导出excel
export const exportMjbgNewData = data => createDown(BASE_URL + "/rygl-djgwbg/exportMjbgData", "get", data);
//离职离岗记录导出excel
export const exportLghzData = data => createDown(BASE_URL + "/api/gzl_01_01/gzlLzlg/exportLzlgData", "get", data);
//教育培训清单导出execl
export const exportPxdjData = data => createDown(BASE_URL + "/jypx/pxdj/exportPxdjData", "get", data);
//场所管理信息导出excel
export const exportCsdjData = data => createDown(BASE_URL + "/csgl/csdj/exportCsdjData", "get", data);
//非授权人员信息导出excel
export const exportWsqryjr = data => createDown(BASE_URL + "/csgl_wsqjrqd/exportWsqryjr", "get", data);
//场所变更记录导出excel
export const exportCsbgData = data => createDown(BASE_URL + "/csgl/csbgdj/exportCsbgData", "get", data);
//涉密计算机信息导出excel
export const exportSmjsjData = data => createDown(BASE_URL + "/sbgl/smjsj/exportSmjsjData", "get", data);
//非涉密计算机信息导出excel
export const exportFmjsjData = data => createDown(BASE_URL + "/sbgl/fmjsj/exportFmjsjData", "get", data);
//涉密移动存储介质信息导出excel
export const exportSmydccjzData = data => createDown(BASE_URL + "/sbgl/ydccjz/exportSmydccjzData", "get", data);
//涉密办公自动化设备信息导出Excel
export const exportSmxxsbData = data => createDown(BASE_URL + "/sbgl/smxxsb/exportSmxxsbData", "get", data);
//非涉密办公自动化设备导出excel
export const exportFmxxsbData = data => createDown(BASE_URL + "/sbgl/fmxxsb/exportFmxxsbData", "get", data);
//涉密网络设备数据导出excel
export const exportSmwlsbData = data => createDown(BASE_URL + "/sbgl/smwlsb/exportSmwlsbData", "get", data);
//非密网络设备信息导出excel
export const exportFmwlsbData = data => createDown(BASE_URL + "/sbgl/fmwlsb/exportFmwlsbData", "get", data);
//安全产品信息导出excel
export const exportXxsbData = data => createDown(BASE_URL + "/sbgl/xxsb/exportXxsbData", "get", data);
//载体管理导出excel
export const exportZtData = data => createDown(BASE_URL + "/ztgl/zt/exportZtData", "get", data);
//载体制作登记管理导出excel
export const exportExcelZtglZtzzdj = data => createDown(BASE_URL + "/api/gzl_01_01/ZtglZtzzdj/exportExcelZtglZtzzdj", "get", data);
//载体制作登记管理导出excel
export const exportFzdjData = data => createDown(BASE_URL + "/ztgl-fzdj/exportFzdjData", "get", data);
//载体借阅登记管理导出excel
export const getZtglJydjExcel = data => createDown(BASE_URL + "/ZtglJydj/getZtglJydjExcel", "get", data);
//定密责任人信息导出excel
export const exportDmzrrData = data => createDown(BASE_URL + "/dmgl/dmzrr/exportDmzrrData", "get", data);
//定密授权记录导出execl
export const exportDmsqData = data => createDown(BASE_URL + "/dmgl/dmsq/exportDmsqData", "get", data);
//国家秘密事项导出excel
export const exportGjmmsxData = data => createDown(BASE_URL + "/dmgl/gjmmsx/exportGjmmsxData", "get", data);
//定密培训信息导出excel
export const exportDmpxData = data => createDown(BASE_URL + "/dmgl/dmpx/exportDmpxData", "get", data);
//定密情况年度统计记录导出excel
export const exportNdtjData = data => createDown(BASE_URL + "/dmgl/ndtj/exportNdtjData", "get", data);
//不明确事项确定情况数据导出
export const exportBmqsxqdqkData = data => createDown(BASE_URL + "/dmgl/bmqsxqdqk/exportBmqsxqdqkData", "get", data);
//政府采购项目情况导出excel
export const exportSmzfcgxmqkData = data => createDown(BASE_URL + "/dmgl/smzfcgxmqk/exportSmzfcgxmqkData", "get", data);


// 历史台账数据导出
export const exportLsSmgwData = data => createDown(BASE_URL + "/lstz/rygl/gwdj/exportSmgwData", "get", data);
export const exportLsSmryData = data => createDown(BASE_URL + "/lstz/rygl/yhxx/exportSmryData", "get", data);
export const exportLsGwbgData = data => createDown(BASE_URL + "/lstz/rygl/djgwbg/exportMjbgData", "get", data);
export const exportLsLglzData = data => createDown(BASE_URL + "/lstz-rygl-lzlg/exportLzlgData", "get", data);
export const exportLsCsdjData = data => createDown(BASE_URL + "/lstz/csgl/csdj/exportCsdjData", "get", data);
export const exportLsCsbgData = data => createDown(BASE_URL + "/lstz/csgl/csbgdj/exportCsbgData", "get", data);


export const exportLsSmjsjData = data => createDown(BASE_URL + "/lstz/sbgl/smjsj/exportSmjsjData", "get", data);
export const exportLsFmjsjData = data => createDown(BASE_URL + "/lstz/sbgl/fmjsj/exportFmjsjData", "get", data);
export const exportLsSmydccjzData = data => createDown(BASE_URL + "/lstz/sbgl/ydccjz/exportSmydccjzData", "get", data);
export const exportLsSmbgzdhsbData = data => createDown(BASE_URL + "/lstz/sbgl/smxxsb/exportSmxxsbData", "get", data);
export const exportLsFsmydccjzData = data => createDown(BASE_URL + "/lstz/sbgl/fmxxsb/exportFmxxsbData", "get", data);
export const exportLsSmwlsbData = data => createDown(BASE_URL + "/lstz/sbgl/smwlsb/exportSmwlsbData", "get", data);
export const exportLsFsmwlsbData = data => createDown(BASE_URL + "/lstz/sbgl/fmwlsb/exportFmwlsbData", "get", data);
export const exportLsAqcpData = data => createDown(BASE_URL + "/lstz/sbgl/xxsb/exportXxsbData", "get", data);
export const exportLsSmzttzData = data => createDown(BASE_URL + "/lstz/ztgl/zt/exportZtData", "get", data);
export const exportLsDmzrrData = data => createDown(BASE_URL + "/lstz/dmgl/dmzrr/exportDmzrrData", "get", data);
export const exportLsDmsqData = data => createDown(BASE_URL + "/lstz/dmgl/dmsq/exportDmsqData", "get", data);
export const exportLsGjmmsxData = data => createDown(BASE_URL + "/lstz/dmgl/gjmmsx/exportGjmmsxData", "get", data);
export const exportLsDmpxData = data => createDown(BASE_URL + "/lstz/dmgl/dmpx/exportDmpxData", "get", data);
export const exportLsNdtjData = data => createDown(BASE_URL + "/lstz/dmgl/ndtj/exportNdtjData", "get", data);
export const exportLsBmqsxqdqkData = data => createDown(BASE_URL + "/lstz/dmgl/bmqsxqdqk/exportBmqsxqdqkData", "get", data);
export const exportLsSmzfcgxmqkData = data => createDown(BASE_URL + "/lstz/dmgl/smzfcgxmqk/exportSmzfcgxmqkData", "get", data);
//载体外发导出
export const getZtglWfcddjExcel = data => createDown(BASE_URL + "/ZtglWfcddj/getZtglWfcddjExcel", "get", data);
//8.携带外出登记记录导出excel
export const exportZtglXdwcDjData = data => createDown(BASE_URL + "/ztgl_xdwcdj/exportZtglXdwcDjData", "get", data);
//9.接收传递登记记录导出excel
export const exportZtglJscdDjData = data => createDown(BASE_URL + "/ztgl-jscddj/exportZtglJscdDjData", "get", data);
//设备报废登记记录 导出成excel表
export const exportExcelSbglSbbfdj = data => createDown(BASE_URL + "/api/gzl_01_01/sbgl_Sbbfdj/exportExcelSbglSbbfdj", "get", data);
//设备报废登记记录 导出成excel表
export const exportExcelSbglSbxhdj = data => createDown(BASE_URL + "/api/gzl_01_01/sbgl_Sbxhdj/exportExcelSbglSbxhdj", "get", data);
//设备定密导出
export const getSbglDmdjExcel = data => createDown(BASE_URL + "/SbglDmdj/getSbglDmdjExcel", "get", data);
//导出涉密设备密级变更审批登记表
export const getSbglMjbgdjExcel = data => createDown(BASE_URL + "/SbglMjbgdj/getSbglMjbgdjExcel", "get", data);
//导出涉密设备责任人变更审批登记表
export const getSbglZrrbgdjExcel = data => createDown(BASE_URL + "/SbglZrrbgdj/getSbglZrrbgdjExcel", "get", data);
//导出涉密设备责任人变更审批登记表
export const exportSbjydjExcel = data => createDown(BASE_URL + "/sbgl/jydj/exportSbjydjExcel", "get", data);
//导出涉密设备责任人变更审批登记表
export const exportSbXxdrDj = data => createDown(BASE_URL + "/sbgl_xxdrdj/exportSbXxdrDj", "get", data);
//导出涉密设备责任人变更审批登记表
export const exportSmKeyExcel = data => createDown(BASE_URL + "/sbgl/key/exportSmKeyExcel", "get", data);
//设备携带外出登记导出
export const exportSbxdwcdj = data => createDown(BASE_URL + "/sbgl_sbxdwcdj/exportSbxdwcdj", "get", data);
//设备维修登记导出excel
export const exportSbwxdjData = data => createDown(BASE_URL + "/sbgl-sbwxdj/exportSbwxdjData", "get", data);
//导出涉密设备密级变更审批登记表
export const exportXtwhdjData = data => createDown(BASE_URL + "/sbgl-xtwhdj/exportXtwhdjData", "get", data);
//非密人员导出
export const exportFmrydj = data => createDown(BASE_URL + "/rygl-fmry/exportFmrydj", "get", data);
export const exportFmzdry = data => createDown(BASE_URL + "/rygl-zdry/exportFmzdry", "get", data);

