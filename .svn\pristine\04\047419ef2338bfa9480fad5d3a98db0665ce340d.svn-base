<template>
  <div id="container" class="large">
    <!-- 在此界面开发保密工作整体情况可视化大屏，要求自适应，可仿照安全态势自适应写 -->
    <div class="con">
      <div class="dpTitle">
        <div class="dpTitleTime">{{ currentTime }}</div>
        <div class="dpTitleLogo"></div>
        <!-- <div class="dpTitleZtqk">佳木斯市保密工作整体情况</div> -->
        <!-- <div class="dpTitleZtqk">绥化市保密工作整体情况</div> -->
        <!-- <div class="dpTitleZtqk">牡丹江市保密工作整体情况</div> -->
        <div class="dpTitleZtqk">大兴安岭保密工作整体情况</div>
        <div class="dpTitleDyfb">机关单位地域分布</div>
        <div class="dpTitleFh" @click="fh">
          <img src="./img/icon_fh.png" alt="" />
          返回
        </div>
      </div>
      <div class="dpLeft">
        <div class="dpLeftTop">
          <div class="dpJgdw">
            <div class="dpJgdwSz">{{ jgdw }}</div>
            <div class="dpJgdwdw">家</div>
          </div>
          <div class="dpBmxz">
            <div class="dpJgdwSz">{{ bmxzgldw }}</div>
            <div class="dpJgdwdw">家</div>
          </div>
        </div>
        <div class="dpLeftSm">
          <div class="dpQNum">{{ smryObj.total }}</div>
          <div class="dpQWz">总人数</div>
          <div class="dpKNum1">
            <div class="dpKNum1For" v-if="this.smryObj.hx.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smryObj.hx" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx">核心</div>
          <div class="dpKNum1 dpKNum2">
            <div class="dpKNum1For" v-if="this.smryObj.zy.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smryObj.zy" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQZy">重要</div>
          <div class="dpKNum1 dpKNum3">
            <div class="dpKNum1For" v-if="this.smryObj.yb.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smryObj.yb" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQYb">一般</div>
        </div>
        <div class="dpLeftSm dpLeftSmcs">
          <div class="dpQNum">{{ smcsObj.total }}</div>
          <div class="dpQWz">总场所</div>
          <div class="dpKNum1">
            <div class="dpKNum1For" v-if="this.smcsObj.hx.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smcsObj.hx" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx">核心</div>
          <div class="dpKNum1 dpKNum2">
            <div class="dpKNum1For" v-if="this.smcsObj.zy.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smcsObj.zy" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQZy">重要</div>
          <div class="dpKNum1 dpKNum3">
            <div class="dpKNum1For" v-if="this.smcsObj.yb.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smcsObj.yb" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQYb">一般</div>
        </div>
        <div class="dpLeftSm dpLeftSmsb">
          <div class="dpQNum">{{ smsbObj.total }}</div>
          <div class="dpQWz">总设备</div>
          <div class="dpKNum1">
            <div class="dpKNum1For" v-if="this.smsbObj.hx.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smsbObj.hx" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx">核心</div>
          <div class="dpKNum1 dpKNum2">
            <div class="dpKNum1For" v-if="this.smsbObj.zy.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smsbObj.zy" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQZy">重要</div>
          <div class="dpKNum1 dpKNum3">
            <div class="dpKNum1For" v-if="this.smsbObj.yb.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smsbObj.yb" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQYb">一般</div>
        </div>
        <div class="dpLeftSm dpLeftSmzt">
          <div class="dpQNum">{{ smztObj.total }}</div>
          <div class="dpQWz">总载体</div>
          <div class="dpKNum1">
            <div class="dpKNum1For" v-if="this.smztObj.hx.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smztObj.hx" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx">核心</div>
          <div class="dpKNum1 dpKNum2">
            <div class="dpKNum1For" v-if="this.smztObj.zy.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smztObj.zy" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQZy">重要</div>
          <div class="dpKNum1 dpKNum3">
            <div class="dpKNum1For" v-if="this.smztObj.yb.length == 1">0</div>
            <div class="dpKNum1For" v-for="(item, index) in this.smztObj.yb" :key="index">
              {{ item }}
            </div>
          </div>
          <div class="dpQHx dpQYb">一般</div>
        </div>
      </div>
      <div class="dpMap">
        <dt1 ref="dt"></dt1>
      </div>
      <div class="dpInput">
        <el-input v-model="dwpmqk" class="dpInputM" placeholder="单位排名情况"></el-input>
        <!-- <input type="text" placeholder="单位排名情况"> -->
        <div class="dpSsAn" @click="search">搜索</div>
      </div>
      <div class="dpTableBtn">
        <div class="dpTableTitle">
          <ul>
            <li>序号</li>
            <li>单位名称</li>
            <li>分数</li>
            <li>组织机构</li>
            <li>涉密岗位</li>
            <li>涉密人员</li>
            <li>涉密场所</li>
            <li>涉密设备</li>
            <li>涉密载体</li>
            <li>教育培训</li>
          </ul>
        </div>
        <div class="dpTableCon" v-if="this.dwData.length != 0">
          <div class="dpTableConMh" v-for="(item, index) in dwData" :key="index">
            <span class="table-text">
              {{ index < 9 ? 0 : "" }}{{ index + 1 }} </span>
                <p class="tb-item tb-cu" :title="item.dwmc" @click="rClick(item)">
                  {{ item.dwmc }}
                </p>
                <p class="tb-item" :title="item.fs">{{ item.fs }}</p>
                <p class="tb-item" :title="item.zzjg">{{ item.zzjg }}</p>
                <p class="tb-item" :title="item.smgw">{{ item.smgw }}</p>
                <p class="tb-item" :title="item.smry">{{ item.smry }}</p>
                <p class="tb-item" :title="item.smcs">{{ item.smcs }}</p>
                <p class="tb-item" :title="item.smsb">{{ item.smsb }}</p>
                <p class="tb-item" :title="item.smzt">{{ item.smzt }}</p>
                <p class="tb-item" :title="item.jypx">{{ item.jypx }}</p>
          </div>
        </div>
        <div v-if="this.dwData.length == 0" class="dpTableConZwsj">
          暂无数据
        </div>
      </div>
      <div class="dpTableRight">
        <div class="dpTableRightTitle">
          <ul>
            <li>序号</li>
            <li>县区</li>
            <li>单位数量</li>
          </ul>
        </div>
        <div class="dpTableRightCon" v-if="this.xqData.length != 0">
          <div class="dpTableRightConMh" v-for="(item, index) in xqData" :key="index">
            <span class="table-text1">
              {{ index < 9 ? 0 : "" }}{{ index + 1 }} </span>
                <p class="tb-item2" :title="item.name">{{ item.name }}</p>
                <p class="tb-item2" :title="item.count">{{ item.count }}</p>
          </div>
        </div>
        <div class="dpTableRightConZwsj" v-if="this.xqData.length == 0">
          暂无数据
        </div>
      </div>
      <div class="dpBingTu">
        <bing @valueChanged="handleValueChanged"></bing>
      </div>
      <div class="dpPfpm">
        <div class="dpPfpmCon">
          <div class="dpDwphb" v-for="(item, index) in pfData" :key="index">
            <div class="dpJdtTitle">
              <div class="dpJdtTitleLeft" title="item.dwmc">
                {{ item.dwmc }}
              </div>
              <div class="dpJdtTitleRight dpJdtTitleRight5" v-if="item.fs > 95">
                {{ item.fs }}%
              </div>
              <div class="dpJdtTitleRight dpJdtTitleRight4" v-if="item.fs > 92 && item.fs <= 95">
                {{ item.fs }}%
              </div>
              <div class="dpJdtTitleRight dpJdtTitleRight3" v-if="item.fs > 90 && item.fs <= 92">
                {{ item.fs }}%
              </div>
              <div class="dpJdtTitleRight dpJdtTitleRight2" v-if="item.fs > 89 && item.fs <= 90">
                {{ item.fs }}%
              </div>
              <div class="dpJdtTitleRight dpJdtTitleRight1" v-if="item.fs > 86 && item.fs <= 89">
                {{ item.fs }}%
              </div>
              <div class="dpJdtTitleRight" v-if="item.fs <= 86">
                {{ item.fs }}%
              </div>
            </div>
            <div class="dpJdtTx">
              <el-progress v-if="item.fs > 95" :text-inside="true" :percentage="item.fs"
                class="custom-progress custom-progress5"></el-progress>
              <el-progress :text-inside="true" :percentage="item.fs" v-if="item.fs > 92 && item.fs <= 95"
                class="custom-progress custom-progress4"></el-progress>
              <el-progress :text-inside="true" v-if="item.fs > 90 && item.fs <= 92" :percentage="item.fs"
                class="custom-progress custom-progress3"></el-progress>
              <el-progress :text-inside="true" v-if="item.fs > 89 && item.fs <= 90" :percentage="item.fs"
                class="custom-progress custom-progress2"></el-progress>
              <el-progress :text-inside="true" :percentage="item.fs" v-if="item.fs > 86 && item.fs <= 89"
                class="custom-progress custom-progress1"></el-progress>
              <el-progress :text-inside="true" v-if="item.fs <= 86" :percentage="item.fs"
                class="custom-progress"></el-progress>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dt1 from "./components/dt1.vue";
import bing from "./components/bing.vue";
import {
  getAllCount,
  getDwCount,
  getPfList,
  selectDwCount,
  toDwInterface,
  selectPffb,
} from "../../../api/dpzs";
import store from '../../store'

import {
  // 判断单位是否已经注册
  getUserInfo,
} from '../../../api/dwzc'
import { dateFormatLs, dateFormat } from "@/utils/moment.js";
export default {
  data() {
    return {
      dwpmqk: "",
      dwData: [],
      xqData: [],
      pfData: [],
      bmxzgldw: "",
      jgdw: "",
      smryObj: {},
      smcsObj: {},
      smsbObj: {},
      smztObj: {},
      currentTime: "",
    };
  },
  components: {
    dt1,
    bing,
  },
  computed: {},
  created() { },
  mounted() {
    // setInterval(function () {
    //   this.time = dateFormat(new Date());
    // }, 1000);
    setInterval(() => {
      this.updateTime();
    }, 1000);

    this.getLeftNum();
    this.getQxMap();
    this.getBtnTable();
    this.getPfPhb();
  },
  methods: {
    async handleValueChanged(data) {
      console.log("接收到的值:", data.selected);
      let values = Object.values(data.selected);
      console.log(values);
      let params = {
        citycode: "232700",
        map: data.name,
        gybw: values[0], //高于八五
        lsbw: values[1],  //六十八五
        lsyx: values[2], //六十以下
      };

      let res = await getPfList(params);
      this.pfData = res;
    },
    fh() {
      const PubSub = require('pubsub-js')
      PubSub.publish('dataFh', 'fh')
      this.$router.push("/ztqksy");
    },
    async rClick(val) {
      console.log(val);
      let parmas = {
        dwid: val.bmid
      }
      let data = await toDwInterface(parmas)
      if (data.code == 10000) {
        //利用localstorage存储到本地
        // store.dispatch("addToken", res.data);
        store.commit('addNewToken', data.data)
        // localStorage.setItem("user-token", res.data)
        // this.userToken = "Bearer " + res.data;
        let dataLogin = await getUserInfo()
        console.log(dataLogin)
        const PubSub = require('pubsub-js')
        PubSub.publish('data', dataLogin)
        PubSub.publish('dataNext', 'next')
        this.$router.push({
          path: "/ztqksy",
          query: {
            dwmc: val.dwmc,
            fs: val.fs,
          },
        });
        localStorage.setItem("dwmc", val.dwmc);
        localStorage.setItem("dwjy", 1);
        store.commit('addAllScore', val.fs)
      }
      // return

      // this.$router.push({
      //   path: "/ztqksy",
      //   query: {
      //     dwmc: val.dwmc,
      //     fs: val.fs,
      //     bmid: val.bmid,
      //   },
      // });
      // localStorage.setItem("dwmc", val.dwmc);
      // localStorage.setItem("fs", val.fs);
      // localStorage.setItem("bmid", val.bmid);
    },
    search() {
      this.getBtnTable();
    },
    updateTime() {
      const now = new Date();
      this.currentTime = dateFormatLs(now);
    },
    async getLeftNum() {
      let params = {
        citycode: "232700",
      };
      let data = await getAllCount(params);
      this.jgdw = data.jgdw;
      this.bmxzgldw = data.bmxzgldw;
      this.smryObj = data.smry;
      this.smryObj.hx = [...String(this.smryObj.hx)].map(Number);
      this.smryObj.zy = [...String(this.smryObj.zy)].map(Number);
      this.smryObj.yb = [...String(this.smryObj.yb)].map(Number);
      this.smcsObj = data.smcs;
      this.smcsObj.hx = [...String(this.smcsObj.hx)].map(Number);
      this.smcsObj.zy = [...String(this.smcsObj.zy)].map(Number);
      this.smcsObj.yb = [...String(this.smcsObj.yb)].map(Number);
      this.smsbObj = data.smsb;
      this.smsbObj.hx = [...String(this.smsbObj.hx)].map(Number);
      this.smsbObj.zy = [...String(this.smsbObj.zy)].map(Number);
      this.smsbObj.yb = [...String(this.smsbObj.yb)].map(Number);
      this.smztObj = data.smzt;
      this.smztObj.hx = [...String(this.smztObj.hx)].map(Number);
      this.smztObj.zy = [...String(this.smztObj.zy)].map(Number);
      this.smztObj.yb = [...String(this.smztObj.yb)].map(Number);
    },
    async getQxMap() {
      let params = {
        citycode: "232700",
      };
      let data = await getDwCount(params);
      this.xqData = data;
    },
    async getBtnTable() {
      let params = {
        dwmc: this.dwpmqk,
        citycode: "232700",
      };
      let data = await selectDwCount(params);
      this.dwData = data;
    },
    async getPfPhb() {
      let params = {
        citycode: "232700",
      };
      let data = await getPfList(params);
      this.pfData = data;
    },
  },
  watch: {},
};
</script>
<style scoped>
.large {
  width: calc(100vw * 1920 / 1920);
  height: calc(100vh * 1080 / 1080);
  background: url(./img/bg.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  left: 0px;
  /* background-position-x: -55px; */
  top: 0;
}

.con {
  position: relative;
}

.dpTitle {
  width: calc(100vw * 1920 / 1920);
  height: calc(100vh * 0.143);
  background: url(./img/head.png) no-repeat center;
  background-size: 100% 100%;
}

.dpTitleTime {
  width: calc(100vw * 0.094);
  height: calc(100vh * 0.022);
  position: absolute;
  top: calc(100vh * 0.008);
  left: calc(100vw * 0.018);
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 24 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-shadow: 0 2px 6px rgba(0, 50, 159, 0.9);
  font-weight: 700;
}

.dpTitleLogo {
  width: calc(100vw * 0.028);
  height: calc(100vh * 0.039);
  position: absolute;
  top: calc(100vh * 0.006);
  left: calc(100vw * 0.362);
  background: url(./img/baomibiaozhi.png) no-repeat center;
  background-size: 100% 100%;
}

.dpTitleZtqk {
  width: calc(100vw * 0.241);
  height: calc(100vh * 0.045);
  line-height: calc(100vh * 0.045);
  position: absolute;
  top: calc(100vh * 0.004);
  left: calc(100vw * 0.395);
  font-size: calc(100vw * 36 / 1920);
  color: #ffffff;
  /* border: 1px solid rgba(198,242,255,1); */
  font-family: SourceHanSansSC-Medium;
  /* background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 34%, #4D8AFE 75%); */
  /* background-image: radial-gradient(circle at 50% 25%, #FFFFFF 0%, #FFFFFF 34%, #4D8AFE 75%); */
  /* -webkit-background-clip: text; */
  /* color: transparent; */
  letter-spacing: calc(100vw * 2 / 1920);
  text-align: center;
  text-shadow: 0 2px 5px rgba(4, 25, 63, 0.57);
  font-weight: 700;
}

.dpTitleDyfb {
  width: calc(100vw * 0.115);
  height: calc(100vh * 0.029);
  line-height: calc(100vh * 0.029);
  position: absolute;
  top: calc(100vh * 0.097);
  left: calc(100vw * 0.443);
  font-family: YouSheBiaoTiHei;
  font-size: calc(100vw * 24 / 1920);
  text-align: center;
  color: #02fdf8;
  letter-spacing: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

.dpTitleFh {
  width: calc(100vw * 0.041);
  height: calc(100vh * 0.027);
  position: absolute;
  top: calc(100vh * 0.006);
  right: calc(100vw * 0.043);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 20 / 1920);
  color: #ffffff;
  letter-spacing: calc(100vw * 0.5 / 1920);
  text-align: center;
  text-shadow: 0 2px 6px rgba(0, 50, 157, 0.9);
  font-weight: 500;
  cursor: pointer;
}

.dpLeft {
  width: calc(100vw * 0.234);
  height: calc(100vh * 1008 / 1080);
  position: absolute;
  top: calc(100vh * 0.049);
  left: calc(100vw * 0.01);
}

.dpLeftTop {
  width: calc(100vw * 0.234);
  height: calc(100vh * 0.304);
  margin-bottom: calc(100vh * 0.011);
  display: flex;
}

.dpJgdw {
  width: calc(100vw * 0.112);
  height: calc(100vh * 0.304);
  margin-right: calc(100vh * 0.016);
  background: url(./img/jiguandanweibg.png);
  background-size: 100% 100%;
  position: relative;
}

.dpJgdwSz {
  width: calc(100vw * 0.032);
  height: calc(100vh * 0.037);
  position: absolute;
  top: calc(100vh * 0.087);
  left: calc(100vw * 0.03);
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 40 / 1920);
  color: #fbde95;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
}

.dpJgdwdw {
  width: calc(100vw * 0.008);
  height: calc(100vh * 0.021);
  position: absolute;
  top: calc(100vh * 0.098);
  left: calc(100vw * 0.067);
  font-size: calc(100vw * 15.58 / 1920);
  font-family: SourceHanSansSC-Regular;
  color: #fbdc8d;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.dpBmxz {
  width: calc(100vw * 0.112);
  height: calc(100vh * 0.304);
  background: url(./img/baomixingzhengguanlibg.png);
  background-size: 100% 100%;
  position: relative;
}

.dpLeftSm {
  width: calc(100vw * 0.234);
  height: calc(100vh * 0.146);
  margin-bottom: calc(100vh * 0.011);
  background: url(./img/shemirenyuanbg.png) no-repeat center;
  background-size: 100% 100%;
  position: relative;
}

.dpLeftSmcs {
  background: url(./img/shemichangsuobg.png) no-repeat center;
  background-size: 100% 100%;
}

.dpLeftSmsb {
  background: url(./img/shemishebeibg.png) no-repeat center;
  background-size: 100% 100%;
}

.dpLeftSmzt {
  background: url(./img/shemizaitibg.png) no-repeat center;
  background-size: 100% 100%;
  margin-bottom: 0;
}

.dpQNum {
  width: calc(100vw * 0.018);
  height: calc(100vh * 0.02);
  position: absolute;
  top: calc(100vh * 0.074);
  left: calc(100vw * 0.022);
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 22 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  text-shadow: 0 2px 5px #00173a;
  font-weight: 700;
}

.dpQWz {
  width: calc(100vw * 0.022);
  height: calc(100vh * 0.019);
  position: absolute;
  top: calc(100vh * 0.095);
  left: calc(100vw * 0.022);
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  text-shadow: 0 2px 5px #00173a;
  font-weight: 700;
}

.dpKNum1 {
  width: calc(100vw * 0.03);
  height: calc(100vh * 0.038);
  position: absolute;
  top: calc(100vh * 0.075);
  left: calc(100vw * 0.06);
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 36 / 1920);
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  text-shadow: 0 2px 5px #00173a;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.dpKNum2 {
  left: calc(100vw * 0.114);
}

.dpKNum3 {
  left: calc(100vw * 0.168);
}

.dpKNum1For {
  width: calc(100vw * 0.03);
  height: calc(100vh * 0.038);
  background: url(./img/img_505.png) no-repeat center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dpQHx {
  width: calc(100vw * 0.0345);
  height: calc(100vh * 0.0165);
  position: absolute;
  top: calc(100vh * 0.096);
  left: calc(100vw * 0.073);
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 14 / 1920);
  background: url(./img/left-rygl-sz-mc.png) no-repeat center;
  background-size: 100% 100%;
  color: #ffffff;
  letter-spacing: 0;
  text-align: right;
  text-shadow: 0 2px 5px #00173a;
  font-weight: 700;
  padding-right: calc(100vw * 0.0035);
  padding-bottom: calc(100vh * 0.0015);
}

.dpQZy {
  left: calc(100vw * 0.127);
}

.dpQYb {
  left: calc(100vw * 0.181);
}

.dpMap {
  width: calc(100vw * 0.487);
  height: calc(100vh * 0.51);
  position: absolute;
  top: calc(100vh * 0.153);
  left: calc(100vw * 0.257);
}

.dpInput {
  width: calc(100vw * 0.224);
  height: calc(100vh * 0.047);
  background: url(./img/sousuo.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: calc(100vh * 0.681);
  left: calc(100vw * 0.255);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.dpInputM {
  width: calc(100vw * 0.14);
  height: calc(100vh * 0.032);
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #b0d2ff;
  letter-spacing: 0;
  font-weight: 400;
}

/* ::v-deep(.el-input__inner) { */
/deep/ .el-input__inner {
  /* .dpInputM .el-input__inner { */
  background-color: transparent !important;
  border: 0px !important;
  height: calc(100vh * 0.036) !important;
  line-height: calc(100vh * 0.036) !important;
  padding: 0 0 !important;
  margin-left: calc(100vw * 0.006);
  color: #fff !important;
  font-family: SourceHanSansSC-Regular;
}

.dpSsAn {
  width: calc(100vw * 0.07);
  height: calc(100vh * 0.042);
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 16 / 1920);
  color: #eeeeff;
  letter-spacing: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.dpTableBtn {
  width: calc(100vw * 0.557);
  height: calc(100vh * 0.222);
  position: absolute;
  top: calc(100vh * 0.749);
  left: calc(100vw * 0.26);
  overflow: hidden;
}

.dpTableTitle {
  width: 100%;
  height: calc(100vh * 0.037);
  background-image: linear-gradient(180deg,
      rgba(8, 28, 78, 0) 0%,
      #0085ff 100%);
  margin-bottom: calc(100vh * 0.001);
}

.dpTableTitle ul li {
  float: left;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 14 / 1920);
  color: #ddeeff;
  font-weight: 500;
  line-height: calc(100vh * 0.035);
  text-align: center;
  list-style-type: none;
}

.dpTableRightTitle {
  width: 100%;
  height: calc(100vh * 0.037);
  background-image: linear-gradient(180deg,
      rgba(8, 28, 78, 0) 0%,
      #0085ff 100%);
  margin-bottom: calc(100vh * 0.001);
}

.dpTableRightTitle ul li {
  float: left;
  font-family: SourceHanSansSC-Medium;
  font-size: calc(100vw * 14 / 1920);
  color: #ddeeff;
  font-weight: 500;
  line-height: calc(100vh * 0.035);
  text-align: center;
  list-style-type: none;
}

.dpTableRightTitle ul li:nth-child(1) {
  width: calc(100vw * 0.02);
}

.dpTableRightTitle ul li:nth-child(2) {
  width: calc((100% - (100vw * 0.02)) / 2);
}

.dpTableRightTitle ul li:nth-child(3) {
  width: calc((100% - (100vw * 0.02)) / 2);
}

.dpTableTitle ul li:nth-child(1) {
  width: calc(100vw * 0.033);
}

.dpTableTitle ul li:nth-child(2) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(3) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(4) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(5) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(6) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(7) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(8) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(9) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableTitle ul li:nth-child(10) {
  width: calc((100% - (100vw * 0.048)) / 9);
}

.dpTableCon {
  width: 100%;
  height: calc(100vh * 0.189);
  overflow-y: scroll;
}

.dpTableConZwsj {
  width: 100%;
  height: calc(100vh * 0.189);
  background: rgba(0, 51, 119, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: calc(100vw * 14 / 1920);
  font-family: SourceHanSansSC-Bold;
}

.dpTableConMh {
  width: 100%;
  height: calc(100vh * 0.037);
  background: rgba(0, 51, 119, 0.5);
  margin-bottom: calc(100vh * 0.001);
}

.dpTableRightCon {
  width: 100%;
  height: calc(100vh * 0.37);
  overflow-y: scroll;
}

.dpTableRightConZwsj {
  width: 100%;
  height: calc(100vh * 0.37);
  background: rgba(0, 51, 119, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: calc(100vw * 14 / 1920);
  font-family: SourceHanSansSC-Bold;
}

.dpTableRightConMh {
  width: 100%;
  height: calc(100vh * 0.037);
  background: rgba(0, 51, 119, 0.5);
  margin-bottom: calc(100vh * 0.001);
}

.table-text1 {
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 14 / 1920);
  display: inline-block;
  float: left;
  line-height: calc(100vh * 0.037);
  text-align: center;
  width: calc(100vw * 0.02);
  color: #fff;
}

.table-text {
  font-family: SourceHanSansSC-Bold;
  font-size: calc(100vw * 14 / 1920);
  display: inline-block;
  float: left;
  line-height: calc(100vh * 0.037);
  text-align: center;
  width: calc(100vw * 0.033);
  color: #fff;
}

.tb-item {
  float: left;
  width: calc((100% - (100vw * 0.048)) / 9);
  line-height: calc(100vh * 0.037);
  font-size: calc(100vw * 14 / 1920);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  white-space: nowrap;
  font-family: SourceHanSansSC-Regular;
}

.tb-cu {
  cursor: pointer;
}

.tb-item2 {
  float: left;
  width: calc((100% - (100vw * 0.02)) / 2);
  line-height: calc(100vh * 0.037);
  font-size: calc(100vw * 14 / 1920);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  white-space: nowrap;
  font-family: SourceHanSansSC-Regular;
}

.text-color1 {
  color: #fff05f;
}

.text-color2 {
  color: #6cffd6;
}

.text-color3 {
  color: #00cbe9;
}

.dpTableRight {
  width: calc(100vw * 0.089);
  height: calc(100vh * 0.407);
  position: absolute;
  top: calc(100vh * 0.313);
  left: calc(100vw * 0.735);
}

.dpBingTu {
  width: calc(100vw * 0.18);
  height: calc(100vh * 0.185);
  position: absolute;
  top: calc(100vh * 0.044);
  right: calc(100vw * 0.01);
}

.dpPfpm {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.655);
  background: url(./img/pingfenpaimingwaikuang.png) no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  top: calc(100vh * 0.248);
  right: calc(100vw * 0.01);
  padding: calc(100vh * 0.06) calc(100vw * 0.009) calc(100vh * 0.019) calc(100vw * 0.01);
}

.dpPfpmCon {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.655);
  overflow-y: scroll;
}

.dpDwphb {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.028);
  margin-bottom: calc(100vh * 0.013);
}

.dpJdtTitle {
  width: calc(100vw * 0.137);
  height: calc(100vh * 0.019);
  background-image: linear-gradient(270deg,
      rgba(238, 238, 238, 0) 0%,
      rgba(0, 109, 252, 0.5) 100%);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.dpJdtTitleLeft {
  font-family: SourceHanSansSC-Regular;
  font-size: calc(100vw * 14 / 1920);
  color: #ffffff;
  text-shadow: 0 2px 2px rgba(11, 29, 62, 0.5);
  font-weight: 400;
  margin-left: calc(100vw * 0.002);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dpJdtTitleRight {
  font-family: LetsgoDigital-Regular;
  font-size: calc(100vw * 18 / 1920);
  color: #6bf0f9;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
}

.dpJdtTitleRight1 {
  color: #89fa56;
}

.dpJdtTitleRight2 {
  color: #25d06b;
}

.dpJdtTitleRight3 {
  color: #f9bf20;
}

.dpJdtTitleRight4 {
  color: #f66505;
}

.dpJdtTitleRight5 {
  color: #c62732;
}

/deep/.el-progress {
  line-height: 0;
}

.custom-progress>>>.el-progress-bar__outer {
  height: calc(100vh * 0.009) !important;
  /* 修改为你想要的高度 */
  background: rgba(4, 43, 103, 0.51) !important;
  border-radius: 0;
}

.custom-progress>>>.el-progress-bar__inner {
  border-radius: 0px 10px 10px 0;
  background-image: linear-gradient(90deg, #39b1ff 0%, #68ffe9 100%);
  line-height: 0;
}

.custom-progress1>>>.el-progress-bar__inner {
  background-image: linear-gradient(270deg, #8cff54 0%, #0f41aa 100%);
}

.custom-progress2>>>.el-progress-bar__inner {
  background-image: linear-gradient(270deg, #25d469 0%, #0f41aa 100%);
}

.custom-progress3>>>.el-progress-bar__inner {
  background-image: linear-gradient(270deg, #fec21e 0%, #0f41aa 100%);
}

.custom-progress4>>>.el-progress-bar__inner {
  background-image: linear-gradient(270deg, #ff6700 0%, #0f41aa 100%);
}

.custom-progress5>>>.el-progress-bar__inner {
  background-image: linear-gradient(270deg, #d42529 0%, #0f41aa 100%);
}

.custom-progress>>>.el-progress-bar__innerText {
  display: none;
}
</style>