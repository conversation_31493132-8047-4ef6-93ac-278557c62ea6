import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//涉密等级
export const getAllSmdj = data => createAPI(BASE_URL+"/dmb/smdj/getAllSmdj", 'get',data)
//获取岗位确定依据
export const getAllGwqdyj = data => createAPI(BASE_URL+"/dmb/gwqdyj/getAllGwqdyj", 'get',data)
//获取最高学历
export const getAllXl = data => createAPI(BASE_URL+"/dmb/xl/getAllXl", 'get',data)
//获取级别职称
export const getAllJbzc = data => createAPI(BASE_URL+"/dmb/jbzc/getAllJbzc", 'get',data)
//获取用人形式
export const getAllYsxs = data => createAPI(BASE_URL+"/dmb/csmx/getAllYsxs", 'get',data)
//获取身份类型
export const getAllSflx = data => createAPI(BASE_URL+"/dmb/csmx/getAllSflx", 'get',data)
//获取涉密设备密级
export const getAllSmsbmj = data => createAPI(BASE_URL+"/dmb/smsbmj/getAllSmsbmj", 'get',data)
//获取涉密设备密级
export const getSmcsmj = data => createAPI(BASE_URL+"/dmb/csgl/getSmcsmj", 'get',data)
//获取涉密设备类型
export const getAllSmsblx = data => createAPI(BASE_URL+"/dmb/smsblx/getAllSmsblx", 'get',data)
//获取涉密设备-安全产品类型
export const getAllAqcplx = data => createAPI(BASE_URL+"/dmb/smsbcplx/getAllAqcplx", 'get',data)
//8.获取涉密设备使用情况
export const getAllSyqk = data => createAPI(BASE_URL+"/dmb/smsbqk/getAllSyqk", 'get',data)
//获取涉密载体生成原因
export const getAllSmztYy = data => createAPI(BASE_URL+"/dmb/smzt/getAllSmztYy", 'get',data)
//获取涉密载体状态
export const getSmztZt = data => createAPI(BASE_URL+"/dmb/smzt/getSmztZt", 'get',data)
//获取涉密载体类型
export const getSmztlx = data => createAPI(BASE_URL+"/dmb/smztlx/getSmztlx", 'get',data)
//获取涉密办公自动化类型
export const getZdhsblx = data => createAPI(BASE_URL+"/dmb/smsb/getZdhsblx", 'get',data)
//获取涉密网络设备类型
export const getsmwlsblx = data => createAPI(BASE_URL+"/dmb/smsb/getsmwlsblx", 'get',data)
//获取定密密级
export const getmj = data => createAPI(BASE_URL+"/dmb/smsx/getmj", 'get',data)
//获取定密类型
export const getXmzl = data => createAPI(BASE_URL+"/dmb/zfcgxmqk/getXmzl", 'get',data)
//获取培训类型
export const getJypxlx = data => createAPI(BASE_URL+"/dmb/jypx/getJypxlx", 'get',data)
//获取培训形式
export const getJypxxs = data => createAPI(BASE_URL+"/dmb/jypx/getJypxxs", 'get',data)
//获取培训形式
export const getLzlglx = data => createAPI(BASE_URL+"/dmb/lghz/getLzlglx", 'get',data)
//查询全部key类型
export const getKeylx = data => createAPI(BASE_URL+"/dmb/sbgl/getKeylx", 'get',data)
//查询sm移动存储介质类型
export const getSmydcclx = data => createAPI(BASE_URL+"/dmb/smsb/getSmydcclx", 'get',data)