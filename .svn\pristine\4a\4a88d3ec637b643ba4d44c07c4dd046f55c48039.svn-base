webpackJsonp([4],{"/A8I":function(e,t){},AGnZ:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=o("Xxa5"),a=o.n(n),r=o("exGp"),i=o.n(r),s=o("XY/r"),l={name:"",components:{},props:{},data:function(){return{form:{jfbh:"",jfdd:"",sblx:"",sbbh:"",sbxh:"",sbpp:"",sbxlh:"",xhsj:"",zrr:"",czr:""},flag:"",sfdc:!1}},computed:{},watch:{},methods:{queryEquipmentByCondition:function(){var e=this;return i()(a.a.mark(function t(){return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:console.log(e.$route.query.equipmentId,"this.$route.query.equipmentCode"),Object(s.h)({equipmentCode:e.$route.query.equipmentId}).then(function(t){console.log(t),e.form=t.data[0],e.form.relocationCabinetName=e.$route.query.relocationCabinetName,e.form.relocationComputerRoomName=e.$route.query.relocationComputerRoomName,e.form.relocationInstitution=e.$route.query.relocationInstitution,e.form.relocationLocation=e.$route.query.relocationLocation,e.form.area=e.$route.query.area;var o=new Date,n=o.getFullYear()+"-"+String(o.getMonth()+1).padStart(2,"0")+"-"+String(o.getDate()).padStart(2,"0");e.form.xhsj=n});case 2:case"end":return t.stop()}},t,e)}))()},dcbutton:function(){var e=this;return i()(a.a.mark(function t(){return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if("1"!=e.flag){t.next=5;break}return e.$confirm("设备已销毁，是否确认提交？","提示",{cancelButtonClass:"btn-custom-cancel",confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(s.i)(e.form).then(function(t){1e4==t.code&&(e.$message.success("提交成功"),e.flag="1",Object(s.a)(e.form).then(function(t){e.dom_download(t,"设备销毁信息.xls")}))})}).catch(function(){}),t.abrupt("return");case 5:Object(s.i)(e.form).then(function(t){1e4==t.code&&(e.$message.success("提交成功"),e.flag="1",Object(s.a)(e.form).then(function(t){e.dom_download(t,"设备销毁信息.xls")}))});case 6:case"end":return t.stop()}},t,e)}))()},dom_download:function(e,t){var o=new Blob([e]),n=window.URL.createObjectURL(o),a=document.createElement("a");console.log("dom",a),a.style.display="none",a.href=n,a.setAttribute("download",t),document.body.appendChild(a),a.click()}},created:function(){},mounted:function(){this.queryEquipmentByCondition()},beforeCreate:function(){},beforeMount:function(){},beforeUpdate:function(){},updated:function(){},beforeDestroy:function(){},destroyed:function(){},activated:function(){}},c={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"box"},[e._m(0),e._v(" "),o("div",{staticStyle:{width:"60%",margin:"0 auto","margin-top":"20px"}},[o("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px","label-position":"left"}},[o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"机房编号"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.computerRoomCode,callback:function(t){e.$set(e.form,"computerRoomCode",t)},expression:"form.computerRoomCode"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"机房地点"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.computerRoomName,callback:function(t){e.$set(e.form,"computerRoomName",t)},expression:"form.computerRoomName"}})],1)],1),e._v(" "),o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"设备类型"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.csm,callback:function(t){e.$set(e.form,"csm",t)},expression:"form.csm"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"设备编号"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.equipmentCode,callback:function(t){e.$set(e.form,"equipmentCode",t)},expression:"form.equipmentCode"}})],1)],1),e._v(" "),o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"设备型号"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.equipmentModel,callback:function(t){e.$set(e.form,"equipmentModel",t)},expression:"form.equipmentModel"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"销毁时间"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.xhsj,callback:function(t){e.$set(e.form,"xhsj",t)},expression:"form.xhsj"}})],1)],1),e._v(" "),o("div",{staticStyle:{display:"flex"}},[o("el-form-item",{staticClass:"mg20",attrs:{label:"设备序列号"}},[o("el-input",{attrs:{disabled:""},model:{value:e.form.equipmentSerialNumber,callback:function(t){e.$set(e.form,"equipmentSerialNumber",t)},expression:"form.equipmentSerialNumber"}})],1)],1)])],1),e._v(" "),o("div",{staticStyle:{display:"flex",width:"160px",margin:"0 auto","margin-top":"20px"}},[o("div",{staticClass:"buttonw btnc2",on:{click:function(t){return e.dcbutton()}}},[e._v("提交并导出")])])])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"top-box"},[t("div",[t("img",{attrs:{src:o("hsSF"),alt:""}})]),this._v(" "),t("div",{staticClass:"top-title"},[this._v("设备销毁信息")])])}]};var u=o("VU/8")(l,c,!1,function(e){o("/A8I")},"data-v-c4afe38a",null);t.default=u.exports}});
//# sourceMappingURL=4.060802b70bb234ba69cf.js.map