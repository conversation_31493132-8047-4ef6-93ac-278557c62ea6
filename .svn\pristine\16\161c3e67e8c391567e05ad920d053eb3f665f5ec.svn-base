import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、添加涉密设备-密级变更
export const addSbglMjbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglMjbg/addSbglMjbg", 'post',data)
//2、修改涉密设备-密级变更
export const updateSbglMjbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglMjbg/updateSbglMjbg", 'post',data)
//4、分页查询涉密设备-密级变更
export const selectSbglMjbgPage = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglMjbg/selectSbglMjbgPage", 'get',data)
//3、删除涉密设备-密级变更
export const deleteSbglMjbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglMjbg/deleteSbglMjbg", 'get',data)
//5、通过jlid查询涉密设备-密级变更
export const selectSbglMjbgByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglMjbg/selectSbglMjbgByJlid", 'get',data)
//8、通过slid查询涉密设备-设备定密的jlid
export const selectJlidBySliddmbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglMjbg/selectJlidBySlid", 'get',data)
//1、添加涉密设备密级变更审批登记表
export const addSbglMjbgdj = data => createAPI(BASE_URL+"/SbglMjbgdj/addSbglMjbgdj", 'post',data)
//4、分页查询涉密设备密级变更审批登记表
export const selectSbglMjbgdjPage = data => createAPI(BASE_URL+"/SbglMjbgdj/selectSbglMjbgdjPage", 'get',data)
