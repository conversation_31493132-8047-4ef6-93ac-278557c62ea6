import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1.添加涉密载体-载体外发传递
export const addZtglWfcd = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/addZtglWfcd", 'post',data)
//2.删除涉密载体-载体外发传递
export const deleteZtglWfcd = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/deleteZtglWfcd", 'post',data)
//3.修改涉密载体-载体外发传递
export const updateZtglWfcd = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/updateZtglWfcd", 'post',data)
//4.分页查询涉密载体-载体外发传递
export const selectZtglWfcdPage = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/selectZtglWfcdPage", 'get',data)
//5.通过jlid查询涉密载体-载体外发传递
export const selectZtglWfcdByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/selectZtglWfcdByJlid", 'get',data)
//根据原jlid查询载体清单
export const getZtqdListByYjlid = data => createAPI(BASE_URL+"/ztgl/ztqd/getZtqdListByYjlid", 'get',data)

export const selectZtglZtqdPage = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/selectZtglZtqdPage", 'get',data)
//6.通过slid查询涉密载体-载体外发传递
export const getZtJscdBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/selectZtglWfcdBySlid", 'get',data)
export const getJlidBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/getJlidBySlid", 'get',data)
export const getJlidBySlid1 = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglWfcd/getJlidBySlid", 'get',data)
export const addZtglWfcddj = data => createAPI(BASE_URL+"/ZtglWfcddj/addZtglWfcddj", 'post',data)
export const selectZtglWfcddjBySlid = data => createAPI(BASE_URL+"/ZtglWfcddj/selectZtglWfcddjBySlid", 'get',data)