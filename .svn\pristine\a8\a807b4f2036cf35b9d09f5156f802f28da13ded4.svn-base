<template>
	<div style="height:calc(100% - 32px - 10px);">
		<div class="zdwb">

			<div class="zzjg-nr">
				<div class="nr-sxt">
					<div class="nr-dwxx" @click="todwmc">
						<img src="../../assets/icons/icon-04.png" alt="" />
						&nbsp;&nbsp;{{ dwmc }}
					</div>
					<div class="organization_configuration">
						<el-tree :data="data" ref="tree" node-key="id" :default-checked-keys="[1]" highlight-current 
							@check-change="handleCheckChange" @node-click="clickNode"></el-tree>
					</div>
				</div>
				<div class="nr-tabs">

					<el-tabs v-model="activeName" @tab-click="handleClick" style="height:100%;">
						<el-tab-pane label="下属组织机构" name="third" style="height:100%;">
							<div>
								<el-button type="success" size="medium" @click="xgdialogVisible = true">新增下属机构
								</el-button>
								&nbsp;
								<el-button type="danger" size="medium" @click="yuchu">移 除</el-button>
							</div>
							<div style="height: 100%; margin-top: 10px">
								<el-table :data="xsjgList" border @selection-change="selectRow" :header-cell-style="{
									background: '#EEF7FF',
									color: '#4D91F8',
								}" style="width: 100%; border: 1px solid #ebeef5" height="calc(100% - 55px - 36px - 10px - 34px - 9px)" stripe>
									<el-table-column type="selection" width="55" align="center">
									</el-table-column>
									<el-table-column type="index" width="60" label="序号" align="center"></el-table-column>
									<el-table-column prop="label" label="名称"></el-table-column>
									<el-table-column prop="" label="操作">
										<template slot-scope="scope">
											<el-button size="medium" type="text" :disabled="scope.$index == 0"
												@click="moveUpward(scope.row, scope.$index)">↑
											</el-button>
											<el-button size="medium" type="text"
												:disabled="(scope.$index + 1) == xsjgList.length"
												@click="moveDown(scope.row, scope.$index)">↓
											</el-button>
										</template>
									</el-table-column>
								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5">
									<el-pagination background @current-change="xshandleCurrentChange"
										@size-change="xshandleSizeChange" :pager-count="5" :current-page="xspage"
										:page-sizes="[5, 10, 20, 30]" :page-size="xspageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="xstotal">
									</el-pagination>
								</div>
							</div>
						</el-tab-pane>
						<el-tab-pane label="组织机构详情" name="first" style="height:100%;">
							<div>
								<el-button v-show="!disabledEdit" type="primary" size="medium" @click="zzjgbj">编 辑
								</el-button>
								<el-button v-show="disabledEdit" type="primary" size="medium" @click="zzjgbc">保 存
								</el-button>
							</div>
							<div style="margin-top: 20px">
								<el-form ref="form1" :model="zzjgxqform" size="mini" label-width="200px"
									:disabled="!disabledEdit">
									<!-- <el-form-item label="组织机构号">
										<el-input v-model="zzjgxqform.zzjgh"></el-input>
									</el-form-item> -->
									<el-form-item label="名称">
										<el-input v-model="zzjgxqform.label"></el-input>
									</el-form-item>
									<el-form-item label="是否为涉密部门" prop="bmflag">
										<el-radio-group v-model="zzjgxqform.bmflag">
											<el-radio label="是" value="1"></el-radio>
											<el-radio label="否" value="0"></el-radio>
										</el-radio-group>
									</el-form-item>
								</el-form>
							</div>
						</el-tab-pane>
						<el-tab-pane label="组织机构用户" name="second" style="height:100%;">
							<div>
								<!-- <el-button type="success" size="medium" @click="dialogVisible = true">新增机构用户</el-button>
								&nbsp;
								<el-button type="danger" size="medium" @click="shanchu">移 除</el-button> -->
							</div>
							<div style="height: 100%; margin-top: 10px">
								<el-table :data="yhList" border @selection-change="selectRow" :header-cell-style="{
									background: '#EEF7FF',
									color: '#4D91F8',
								}" style="width: 100%; border: 1px solid #ebeef5" height="calc(100% - 55px  - 34px - 10px - 9px)" stripe>
									<el-table-column type="selection" width="55" align="center">
									</el-table-column>
									<el-table-column prop="xm" label="姓名"></el-table-column>
									<el-table-column prop="xb" label="性别"></el-table-column>
									<el-table-column prop="zw" label="职务"></el-table-column>
									<el-table-column prop="yrxs" label="任职方式"></el-table-column>

								</el-table>

								<!-- -------------------------分页区域---------------------------- -->
								<div style="border: 1px solid #ebeef5">
									<el-pagination background @current-change="handleCurrentChange"
										@size-change="handleSizeChange" :pager-count="5" :current-page="page"
										:page-sizes="[5, 10, 20, 30]" :page-size="pageSize"
										layout="total, prev, pager, sizes,next, jumper" :total="total">
									</el-pagination>
								</div>
							</div>
						</el-tab-pane>
					</el-tabs>
				</div>
			</div>
		</div>

		<el-dialog title="新增下属组织机构" :visible.sync="xgdialogVisible" width="22%" @close="close('form')">
			<el-form ref="form" :model="xglist" :rules="rules" label-width="125px" size="mini">
				<el-form-item label="部门名称" prop="label" class="one-line">
					<el-input placeholder="部门名称" v-model="xglist.label" clearable @blur="onIndexBlur" style="width:100%;"></el-input>
				</el-form-item>
				<el-form-item label="是否为涉密部门" prop="bmflag" >
					<el-radio-group v-model="xglist.bmflag">
						<el-radio label="是" value="1"></el-radio>
						<el-radio label="否" value="0"></el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="xszzjgxz('form')">保 存</el-button>
				<el-button type="warning" @click="xgdialogVisible = false">关 闭</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			// 控制编辑按钮的转换
			pdm: 0,
			disabledEdit: false,
			//
			tjlist: {
				xm: '',
				xb: '',
				zw: '',
				rzfs: '',

			},
			xglist: {
				// zzjgh: '',
				label: '',
				// pxh: '',
				bmflag: '否',
			},
			value: '',
			dialogVisible: false,
			xgdialogVisible: false,
			yhList: [],
			xsjgList: [],
			page: 1,
			pageSize: 10,
			total: 0,
			xspage: 1,
			xspageSize: 10,
			xstotal: 0,
			zzjgxqform: {},
			data: [],
			oldArr: [],
			newArr: [],
			count: 1,
			defaultProps: {
				children: "children",
				label: "label",
			},
			activeName: "third",
			selectlistRow: [], //列表的值
			rules: {
				xm: [{
					required: true,
					message: '请输入姓名',
					trigger: 'blur'
				},],
				xb: [{
					required: true,
					message: '请选择性别',
					trigger: 'blur'
				},],
				zw: [{
					required: true,
					message: '请输入职务',
					trigger: 'blur'
				},],
				rzfs: [{
					required: true,
					message: '请选择组织机构号',
					trigger: 'blur'
				},],
				zzjgh: [{
					required: true,
					message: '请输入培训主题',
					trigger: 'blur'
				},],
				label: [{
					required: true,
					message: '请输入组织机构名称',
					trigger: 'blur'
				},],
				bmjb: [{
					required: true,
					message: '请输入部门级别',
					trigger: 'blur'
				},],
				bmflag: [{
					required: true,
					message: '请选择保密行政管理单位',
					trigger: 'blur'
				},],
			},
			dwmc: '',
			olddata: {},
			bmm: '',
			tsxx: '',
		};
	},
	mounted() {
	

	},
	methods: {
		//编辑
		zzjgbj() {
			
		},
		zzjgbc() {
			this.disabledEdit = false
		},
		filters(arr) {
			
		},
		fun() {
			
		},
		todwmc() {
			// this.zzjgxqform.label = getJgxx()[0].label
		
		},
		dwxx() {
			
		},


		//删除
		shanchu(id) {
			
		},
		//用户分页
		//列表分页--跳转页数
		handleCurrentChange(val) {
		
		},
		//列表分页--更改每页显示个数
		handleSizeChange(val) {
			
		},
		clickNode(data) {
			//点击节点触发,不同层级的level事件不同
			//可对应界面变化，比如通过v-if控制模块显隐

			
		},
		//组织机构用户初始化成员列表
		zzjgyh() {
			
		},
		//下属组织机构初始化成员列表
		xszzjg() {
		
		},
		moveUpward(row, index) {
			
		},
		moveDown(row, index) {
			
		},

		//下属组织机构新增
		xszzjgxz(form) {
			
		},
		handleCheckChange() {
			
		},

		//删除
		yuchu(id) {
			
		},
		//下属组织机构
		//列表分页--跳转页数
		xshandleCurrentChange(val) {
			
		},
		//列表分页--更改每页显示个数
		xshandleSizeChange(val) {
			
		},
		selectRow(val) {
			
		},
		handleClose(done) {
			this.resetForm()
			this.dialogVisible = false
		},
		//添加重置
		resetForm() {
			
		},
		resetForm1() {
			
		},
		handleNodeClick(data) {
		
		},
		handleClick(tab, event) {
			
		},
		// 弹框关闭触发
		close(formName) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[formName].resetFields();
		},
		close1(form) {
			// 清空表单校验，避免再次进来会出现上次校验的记录
			this.$refs[form].resetFields();
		},
		onIndexBlur() {
		
		}
	},
};
</script>

<style scoped>
.zdwb {
	width: 100%;
	height: 100%;
	/* box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.1); */
	/* background: url(../../assets/background/table_bg.png) no-repeat center; */
	/* background-size: 100% 100%; */
}

/* 
.mk_bt {
	width: 100%;
	height: 7%;
	border-bottom: 2px solid rgba(216, 216, 216, 1);
} */

.mk_btl {
	display: flex;
	align-items: center;
	margin-left: 20px;
	font-size: 0.9vw;
	height: 100%;
	font-weight: 400;
}

.zzjg-nr {
	width: 100%;
	height: 100%;
	display: flex;
}

.nr-sxt {
	width: 25%;
	height: 100%;
	/* background-color: #523fff; */
	padding: 1% 1.5% 0% 1.5%;
	border-right: 2px solid rgba(216, 216, 216, 1);
}

.organization_configuration{
	cursor:pointer;
	height: 95%;
    overflow-y: scroll;
}
.nr-dwxx {
	width: 100%;
	height: 5%;
	display: flex;
	align-items: center;
	border-bottom: 2px solid rgba(216, 216, 216, 1);
	font-size: 14px;
	color: #657089;
	letter-spacing: 0;
	line-height: 19.6px;
	font-weight: 400;
	cursor:pointer;
}

.nr-tabs {
	width: 75%;
	height: 100%;
	padding: 0.75% 1.5% 0% 1.5%;
}

/deep/.el-tree {
	background: none;
}

/deep/.el-tabs__nav-wrap::after {
	background-color: rgba(216, 216, 216, 1);
}

/deep/.el-form-item__label {
	text-align: left;
}

/deep/.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
	margin-bottom: 15px !important;
}

/deep/ .el-tree-node:focus>.el-tree-node__content {
	background: rgb(217, 236, 255);
}

:deep(.el-tree-node:focus>.el-tree-node__content) {
	background: rgb(217, 236, 255);
}

:deep(.el-tree-node__content:hover, .el-upload-list__item:hover) {
	background: rgb(217, 236, 255);

}

/* .organization_configuration {} */
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
	/* // 设置颜色 */
	background: rgb(217, 236, 255);
	/* // 透明度为0.2的skyblue，作者比较喜欢的颜色  */
	color: #409eff;
	/* // 节点的字体颜色 */
	font-weight: bold;
	/* // 字体加粗 */
}
/deep/ .el-tabs__content{
	height: 100%;
}
/deep/.el-dialog__body .el-form>div .el-form-item__label {
	width: 125px !important;
}
</style>
