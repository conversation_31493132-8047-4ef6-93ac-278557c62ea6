import {createAPI, createFileAPI,createDown,createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//添加涉密载体-载体制作
export const saveZtglZtzz = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZz/saveZtglZtzz", 'post',data)// 涉密等级
//修改涉密载体-载体制作
export const updateZtglZtzz = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZz/updateZtglZtzz", 'post',data)// 涉密等级
//查询涉密载体-载体制作
export const selectPageZtglZtzz = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZz/selectPageZtglZtzz", 'get',data)// 涉密等级
//通过jlid查询涉密载体-载体制作
export const selectByIdZtglZtzz = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZz/selectByIdZtglZtzz", 'get',data)// 涉密等级
//通过实例id删除涉密载体-载体制作
export const deleteZtglZtzz = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZz/deleteZtglZtzz", 'post',data)// 涉密等级
//载体制作台账登记
export const saveZtglZtzzdj = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZtzzdj/saveZtglZtzzdj", 'post',data)// 涉密等级
//查询载体
export const selectPageZtglZtzzdj = data => createAPI(BASE_URL+"/api/gzl_01_01/ZtglZtzzdj/selectPageZtglZtzzdj", 'get',data)// 涉密等级
//查询设备红盘
export const getBmbhByJzmc = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getBmbhByJzmc", 'get',data)// 涉密等级
//查询单导盒
export const getDdhBmbh = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getDdhBmbh", 'get',data)// 涉密等级
//查询输出机
export const getSmjsjBmbh = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_xxdr/getSmjsjBmbh", 'get',data)// 涉密等级
