// 导出为excel
import XLSX, { WorkSheet } from 'xlsx'
import XLSXStyle from 'xlsx-style'

import FS from 'fs'

import { checkArr } from '../utils/utils'

export function exportExcel(filename, data, merges, styles, config) {
  //data：[["列1名称","列2名称","列3名称"],["列11","值21","值31"],["列12","值22","值32"]]
  var sheet_name = 'Sheet1' //Excel第一个sheet的名称
  var work_book = XLSX.utils.book_new()
  var sheet = XLSX.utils.aoa_to_sheet(data)
  // 单元格合并
  if (merges) {
    console.log('单元格合并', merges)
    sheet['!merges'] = merges
  }
  XLSX.utils.book_append_sheet(work_book, sheet, sheet_name) //将数据添加到工作薄
  // sheet页数据补全(补空字符)
  sheetDataCompletion(work_book, config)
  //
  XLSXStyle.writeFile(parseExcelStyle(work_book, styles), filename) //导出Excel
}

/**
 * 解析 sheet 样式
 * styles接收一个json对象，包括 all字段对象和cell数组字段
 * all：全局样式
 * cell：单元格样式集合
 */
export const parseExcelStyle = (workbook, styles) => {
  console.log('解析work_book', workbook, styles)
  if (!styles) {
    return workbook
  }
  // 获取所有sheet页
  console.log(workbook)
  let sheets = workbook.Sheets
  //
  let all = styles.all
  let cell = styles.cell
  let cols = styles.cols
  let rows = styles.rows
  //
  let sheetKeys = Object.keys(sheets)
  console.log(sheetKeys)
  //
  sheetKeys.forEach((key, index) => {
    //
    if (cols) {
      if (cols.scoped == -1) {
        sheets[key]['!cols'] = cols.style
        console.log('第' + index + 'sheet(全)', sheets[key])
      } else {
        if (cols.scoped == index) {
          sheets[key]['!cols'] = cols.style
          console.log('第' + index + 'sheet(单)', sheets[key])
        }
      }
    }
    if (rows) {
      if (rows.scoped == -1) {
        sheets[key]['!rows'] = rows.style
        console.log('第' + index + 'sheet(全row)', sheets[key])
      } else {
        if (rows.scoped == index) {
          sheets[key]['!rows'] = rows.style
          console.log('第' + index + 'sheet(单row)', sheets[key])
        }
      }
    }
    //
    Object.keys(sheets[key]).forEach((cellKey) => {
      // 解析样式
      if (cellKey.indexOf('!') == -1) {
        // 加上全局样式
        sheets[key][cellKey].s = JSON.parse(JSON.stringify(all))
        // 加上局部样式
        cell.forEach((cellStyleItem) => {
          // console.log('>>>>>',cellStyleItem, all)
          if (cellStyleItem.scoped == -1) {
            // console.log('全sheet页生效样式', cellStyleItem.scoped, cellStyleItem)
            // console.log('判断生效单元格', cellStyleItem.index == cellKey)
            if (cellStyleItem.index == cellKey) {
              // console.log(
              //   '找到生效单元格',
              //   cellKey,
              //   sheets[key][cellKey],
              //   JSON.parse(JSON.stringify(sheets[key][cellKey].s)),
              //   JSON.parse(JSON.stringify(cellStyleItem.style))
              // )
              if (cellStyleItem.style.fill) {
                Object.assign(
                  sheets[key][cellKey].s.fill,
                  JSON.parse(JSON.stringify(cellStyleItem.style.fill))
                )
              }
              if (cellStyleItem.style.font) {
                Object.assign(
                  sheets[key][cellKey].s.font,
                  JSON.parse(JSON.stringify(cellStyleItem.style.font))
                )
              }
              if (cellStyleItem.style.numFmt) {
                Object.assign(
                  sheets[key][cellKey].s.numFmt,
                  JSON.parse(JSON.stringify(cellStyleItem.style.numFmt))
                )
              }
              if (cellStyleItem.style.alignment) {
                Object.assign(
                  sheets[key][cellKey].s.alignment,
                  JSON.parse(JSON.stringify(cellStyleItem.style.alignment))
                )
              }
              if (cellStyleItem.style.border) {
                Object.assign(
                  sheets[key][cellKey].s.border,
                  JSON.parse(JSON.stringify(cellStyleItem.style.border))
                )
              }
            }
          } else {
            // console.log(
            //   '单sheet页生效样式',
            //   cellStyleItem.scoped,
            //   cellStyleItem
            // )
            if (
              index == cellStyleItem.scoped &&
              cellStyleItem.index == cellKey
            ) {
              if (cellStyleItem.style.fill) {
                Object.assign(
                  sheets[key][cellKey].s.fill,
                  JSON.parse(JSON.stringify(cellStyleItem.style.fill))
                )
              }
              if (cellStyleItem.style.font) {
                Object.assign(
                  sheets[key][cellKey].s.font,
                  JSON.parse(JSON.stringify(cellStyleItem.style.font))
                )
              }
              if (cellStyleItem.style.numFmt) {
                Object.assign(
                  sheets[key][cellKey].s.numFmt,
                  JSON.parse(JSON.stringify(cellStyleItem.style.numFmt))
                )
              }
              if (cellStyleItem.style.alignment) {
                Object.assign(
                  sheets[key][cellKey].s.alignment,
                  JSON.parse(JSON.stringify(cellStyleItem.style.alignment))
                )
              }
              if (cellStyleItem.style.border) {
                Object.assign(
                  sheets[key][cellKey].s.border,
                  JSON.parse(JSON.stringify(cellStyleItem.style.border))
                )
              }
            }
          }
        })
        // console.log('>>', sheets[key][cellKey])
      }
    })
  })
  //
  return workbook
}

/**
 * sheet页数据补全方法(补空字符)
 * config.rowIndexArrIgnore: 数组，忽略行索引(从1开始)
 */
export const sheetDataCompletion = (workbook, config) => {
  //
  let sheets = workbook.Sheets
  let rowIndexArrIgnore = []
  if (config) {
    if (config.rowIndexArrIgnore) {
      rowIndexArrIgnore = config.rowIndexArrIgnore
    }
  }
  //
  let sheetKeys = Object.keys(sheets)
  console.log(sheetKeys)
  sheetKeys.forEach((key, index) => {
    // 获取本sheet页行、列最大值
    let refStr = sheets[key]['!ref']
    console.log('refStr', refStr, refStr.split(':'))
    // 获取数据右下角位置（即数据最后一个元素的位置）
    let rbSheetPos = refStr.split(':')[1]
    // 解析最大列数
    let maxColNum = rbSheetPos.match(/\d+/g)[0]
    // 最大列前缀
    let prefixCol = rbSheetPos.match(/[A-z]+/g)[0]
    console.log('最大列数', maxColNum, '最大列前缀', prefixCol)
    // 数据补全，有缺省的数据内置一个空字符串
    console.log('sheets[key]', JSON.parse(JSON.stringify(sheets[key])))
    let sheetKeyArr = Object.keys(sheets[key])
    let cellKeyCheck
    sheetKeyArr.some((cellKey) => {
      if (cellKey.startsWith('!')) {
        return true
      }
      // 第一列
      for (let i = 1; i <= maxColNum; i++) {
        if (rowIndexArrIgnore.indexOf(i) != -1) {
          continue
        }
        cellKeyCheck = cellKey.match(/[A-z]+/g)[0] + i
        if (sheetKeyArr.indexOf(cellKeyCheck) == -1) {
          // 补一个空数据
          sheets[key][cellKeyCheck] = { t: 's', v: '' }
        }
      }
    })
  })
}

// 多sheet页excel导出方法
export function exportExcelNumerousSheet(filename, exportList, styles, config) {
  let sheet_name
  let work_book = XLSX.utils.book_new()
  let sheet
  exportList.forEach((item) => {
    sheet_name = item.sheetName
    sheet = XLSX.utils.aoa_to_sheet(item.data)
    if (item.merges) {
      console.log('单元格合并', item.merges)
      sheet['!merges'] = item.merges
    }
    XLSX.utils.book_append_sheet(work_book, sheet, sheet_name) //将数据添加到工作薄
  })
  console.log(sheet)
  console.log(work_book)
  console.log('准备加工多个sheet页')
  sheetDataCompletion(work_book, config)
  XLSXStyle.writeFile(parseExcelStyle(work_book, styles), filename) //导出Excel
}

// 将workbook装化成blob对象
export function workbook2blob(workbook) {
  // 生成excel的配置项
  var wopts = {
    // 要生成的文件类型
    bookType: 'xlsx',
    // // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
    bookSST: false,
    type: 'binary',
  }
  var wbout = XLSX.write(workbook, wopts)
  // 将字符串转ArrayBuffer
  function s2ab(s) {
    var buf = new ArrayBuffer(s.length)
    var view = new Uint8Array(buf)
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
    return buf
  }
  var blob = new Blob([s2ab(wbout)], {
    type: 'application/octet-stream',
  })
  return blob
}

// 将workbook装化成ArrayBuffer对象
export function workbook2ArrayBuffer(workbook) {
  // 生成excel的配置项
  var wopts = {
    // 要生成的文件类型
    bookType: 'xlsx',
    // // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
    bookSST: false,
    type: 'binary',
  }
  var wbout = XLSXStyle.write(workbook, wopts)
  // 将字符串转ArrayBuffer
  function s2ab(s) {
    var buf = new ArrayBuffer(s.length)
    var view = new Uint8Array(buf)
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
    return buf
  }
  return s2ab(wbout)
}

// 将blob对象创建bloburl，然后用a标签实现弹出下载框
export function openDownloadDialog(blob, fileName) {
  if (typeof blob == 'object' && blob instanceof Blob) {
    blob = URL.createObjectURL(blob) // 创建blob地址
  }
  var aLink = document.createElement('a')
  aLink.href = blob
  // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，有时候 file:///模式下不会生效
  aLink.download = fileName || ''
  var event
  if (window.MouseEvent) event = new MouseEvent('click')
  //   移动端
  else {
    event = document.createEvent('MouseEvents')
    event.initMouseEvent(
      'click',
      true,
      false,
      window,
      0,
      0,
      0,
      0,
      0,
      false,
      false,
      false,
      false,
      0,
      null
    )
  }
  aLink.dispatchEvent(event)
}
