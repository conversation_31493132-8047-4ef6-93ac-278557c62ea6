import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//涉密人员-出国出境-审批记录表单提交
export const addRyglCgcj = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/addRyglCgcj", 'post',data)
//修改涉密人员-出国出境-审批记录表单
export const updateRyglCgcj = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/updateRyglCgcj", 'post',data)
//删除涉密人员-出国出境-审批记录表单
export const deleteRyglCgcj = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/deleteRyglCgcj", 'post',data)
//分页涉密人员-出国出境-审批记录表单
export const selectRyglCgcjPage = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/selectRyglCgcjPage", 'get',data)
//通过rwid涉密人员-出国出境-审批记录表单
export const selectRyglCgcjByRwId = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/selectRyglCgcjByRwId", 'get',data)
//通过rwid涉密人员-出国出境-审批记录表单
export const selectRyglCgcjBySlId = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/selectRyglCgcjBySlId", 'get',data)
//根据申请人id查询最新的涉密人员-出国出境-审批记录
export const getCurRyglCgcj = data => createAPI(BASE_URL+"/api/gzl_01_01/RyglCgcj/getCurRyglCgcj", 'get',data)