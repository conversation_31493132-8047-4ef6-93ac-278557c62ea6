import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//人员管理查询分页
export const getYhxxListByPage = data => createAPI(BASE_URL+"/user/getYhxxListByPage", 'get',data)
//人员管理添加
export const saveYhxx = data => createAPI(BASE_URL+"/user/saveYhxx", 'post',data)
//人员管理删除
export const deleteYhxxByIds = data => createAPI(BASE_URL+"/user/deleteYhxxByIds", 'post',data)
//人员管理密码重置
export const resetPasswordById = data => createAPI(BASE_URL+"/user/resetPasswordById", 'post',data)
//用户名校验
export const selectYhmXx = data => createAPI(BASE_URL+"/user/selectYhmXx", 'post',data)

//用户名校验
export const updateYhlxAndJs = data => createAPI(BASE_URL+"/user/updateYhlxAndJs", 'post',data)
