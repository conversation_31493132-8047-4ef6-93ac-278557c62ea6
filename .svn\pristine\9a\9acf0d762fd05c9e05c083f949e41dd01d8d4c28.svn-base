<!--  -->
<template>
  <div class="box">
    <div class="top-box">
      <div>
        <img src="./img/title.png" alt="" />
      </div>
      <div class="top-title">巡检流程</div>
    </div>

    <div style="width: 1500px;margin: 0 auto;margin-top: 50px;">
      <div class="bt-title" style="text-align: center;">
        巡检异常情况明细表
      </div>
      <div
        style="display: flex;justify-content: space-between;margin-top: 30px"
      >
        <div class="fbtbox">
          <div class="labels">巡检机房名称</div>
          <div class="values">{{ tableData[0].computerRoomName }}</div>
        </div>
        <div class="fbtbox">
          <div class="labels">巡检时间</div>
          <div class="values">{{ tableData[0].inspectionTime }}</div>
        </div>
        <div class="fbtbox">
          <div class="labels">巡检人员</div>
          <div class="values">{{ tableData[0].createByName }}</div>
        </div>
      </div>
      <div style="width: 100%;margin-top: 20px;">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="tableHeaderCellStyle"
          :cell-style="tableCellStyle"
          max-height="800px"
        >
          <el-table-column prop="equipmentName" label="设备名称" align="center">
          </el-table-column>
          <el-table-column prop="equipmentModel" label="型号" align="center">
          </el-table-column>
          <el-table-column
            prop="equipmentTypeName"
            label="设备类型"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="equipmentSerialNumber"
            label="序列号"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="description"
            label="故障情况描述"
            align="center"
          >
            <template slot-scope="scope">
              <el-input v-model="scope.row.description"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="treatment" label="处理方法" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.treatment"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="recoveryTime" label="恢复时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.recoveryTime"
                type="date"
                placeholder="选择日期"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 15px">
          <el-pagination
            background
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :pager-count="5"
            :current-pageNo="page"
            :pageNo-sizes="[5, 10, 20, 30]"
            :pageNo-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div
      style="display: flex;width: 500px;justify-content: center;
margin: 0 auto;margin-top: 60px;"
    >
      <div class="buttonw btnc1" @click="dcbtn">提交并导出</div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  selectInspectionIssueDetails,
  saveInspectionEquipment,
  exportInspectionForm
} from "../../../api/jfxj";
export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: []
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    async listInspectionIssueDetails() {
      selectInspectionIssueDetails({
        pageNum:this.page,
        pageSize:this.pageSize,
        computerRoomInspectionId: this.$route.query.id
      }).then(res => {
        console.log(res);
        if (res.code == 10000) {
          this.tableData = res.data.records;
          this.total = res.data.total
        }
      });
    },
    async dcbtn() {
      saveInspectionEquipment({
        scanCode: this.$route.query.scanCode,
        inspectionEquipmentList: this.tableData,
        computerRoomInspectionId: this.$route.query.id,
        equipmentMainType: "2"
      }).then(res => {
        if (res.code == 10000) {
          exportInspectionForm({
            computerRoomInspectionId: this.$route.query.id
          }).then(res => {
            this.$message({
          message: '导出成功',
          type: 'success'
        });
          });
        }
      });
    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
    handleCurrentChange(val) {
      this.page = val;
    },
    handleSizeChange(val) {
      this.pageSize = val;
    },
    tableCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;";
    },
    tableHeaderCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;";
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    console.log(this.$route.query.id);
    this.listInspectionIssueDetails();
  },
  //生命周期 - 创建之前
  beforeCreate() {},
  //生命周期 - 挂载之前
  beforeMount() {},
  //生命周期 - 更新之前
  beforeUpdate() {},
  //生命周期 - 更新之后
  updated() {},
  //生命周期 - 销毁之前
  beforeDestroy() {},
  //生命周期 - 销毁完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {}
};
</script>
<style scoped>
.box {
  width: 1580px;
  margin: 0 auto;
}
.top-box {
  width: 100%;
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 20px;
}
.top-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
  margin-left: 10px;
}
.bt-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
}
.label-title {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
}
.buttonw {
  /* width: 72px;
  height: 32px; */
  padding: 0px 20px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.btnc {
  background-color: #3ecbfe;
  margin-right: 20px;
}
.btnc1 {
  background-color: #3e9efe;
}
.btnc2 {
  background-color: #20bdd1;
  margin-left: 20px;
}
.tszt {
  font-family: KaiTi;
  font-weight: 700;
}
.cxbtn {
  width: 72px;
  height: 32px;
  background: #3e9efe;
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0.07px;
  font-weight: 400;
  text-align: center;
  line-height: 32px;
  margin-left: 20px;
  margin-top: 14px;
}
.smzt {
  font-size: 12px;
}
/deep/ .el-step__icon {
  width: 36px;
  height: 36px;
}
/deep/ .el-step.is-horizontal .el-step__line {
  top: 18px;
  left: 45px;
  right: 12px;
}
/deep/ .el-step__head.is-process {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__head.is-wait {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__title.is-wait {
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  color: #080808;
  font-weight: 500;
}
/deep/ .el-step__title.is-process {
}
/deep/ .el-step__icon.is-text {
  border: 2px solid;
  border-color: #0077ff;
}
/deep/ .el-step__head.is-success .is-text {
  background-color: #0077ff;
}
/deep/ .el-step__head.is-success {
  color: #fff;
  border-color: #0077ff;
}
/deep/ .el-step__title.is-success {
  font-family: SourceHanSansSC-Medium;
  font-size: 16px;
  color: #080808;
  font-weight: 500;
}
.inputcss /deep/ .el-input__inner {
  width: 210px !important;
  height: 32px;
  border-radius: 4px;
}
.inputcss /deep/ .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
}
/deep/ .el-radio__inner {
  width: 20px;
  height: 20px;
  border-radius: 2px !important;
}

/deep/ .el-radio__input.is-checked .el-radio__inner::after {
  content: "";
  width: 8px;
  height: 3px;
  border: 1px solid #0077ff;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 5px;
  left: 4px;
  transform: rotate(-45deg);
  border-radius: 0px;
  background: none;
}
/deep/ .el-radio__input.is-checked .el-radio__inner {
  background: #fff;
}
/deep/ .el-form-item {
  margin: 10px 0;
}
.values {
  font-family: SourceHanSansSC-Normal;
  font-size: 14px;
  color: #333333;
  font-weight: 400;
  margin-left: 24px;
}
.labels {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
}
.fbtbox {
  display: flex;
  align-items: center;
  width: 350px;
}
/deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover:nth-child(even)
  > td {
  background-color: #dce8fb !important;
}
/deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover:nth-child(odd)
  > td {
  background-color: #dce8fb !important;
}
/deep/ .el-table__body tr:nth-child(even) {
  background-color: #dce8fb; /* 偶数行（斑马线）的默认背景色 */
}
/deep/ .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 178px;
}
/deep/ .el-pagination__total{
  font-size: 16px !important;
}
/deep/ .el-pagination__jump{
  font-size: 16px !important;
}
</style>
