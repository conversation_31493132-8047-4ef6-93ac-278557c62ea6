/**
 * 埋点
 * 用户行为采集
 */
import { getWindowLocation } from '../utils/windowLocation'
import { writeLog } from '../utils/logUtils'

export default {
  setPoint: function (etJson) {
    console.log('etJson', etJson)
    const params = {
      // 业务数据
      bussData: {},
      // 系统数据
      sysData: {},
    }
    Object.assign(params.bussData, etJson)
    // // doument对象
    // if (document) {
    //   params.bussData.title = document.title || ''
    // }
    // 分辨率
    if (window && window.screen) {
      // 屏幕尺寸
      params.sysData.screenHeight = window.screen.height || 0
      params.sysData.screenWidth = window.screen.width || 0
      // 颜色深度
      params.sysData.colorDepth = window.screen.colorDepth || 0
    }
    // 设备信息
    if (navigator) {
      params.sysData.lang = navigator.language || 0
      params.sysData.userAgent = navigator.userAgent || 0
      // document加载时长
      let performanceNavigationTiming =
        window.performance.getEntriesByType('navigation')[0]
      params.sysData.domContentLoadTime =
        performanceNavigationTiming.domContentLoadedEventEnd -
        performanceNavigationTiming.domContentLoadedEventStart
      // 页面路由
      params.sysData.pageRoute = performanceNavigationTiming.name
    }
    params.bussData.account = getWindowLocation().yhm
    console.log('用户行为数据', params)
    // 数据收集完成，准备写入log文件
    writeLog(params)
  },
}
