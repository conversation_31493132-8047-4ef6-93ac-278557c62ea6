// 校验是否为数组
export const checkArr = (data) => {
  return '[object Array]' == Object.prototype.toString.call(data)
}

// 校验是否为字符串
export const checkString = (data) => {
  return '[object String]' == Object.prototype.toString.call(data)
}

// 校验是否为数字
export const checkNumber = (data) => {
  console.log(Object.prototype.toString.call(data))
  return '[object Number]' == Object.prototype.toString.call(data)
}

// 校验是否为空
export const checkObjIsBlank = (data, fieldArr) => {
  if (fieldArr === undefined) {
    throw new Error('校验字段为空')
  }
  if (!checkArr(fieldArr)) {
    throw new Error('校验字段不是数组格式')
  }
  fieldArr.some((item) => {
    let value = getObjValue(data, item.field)
    if (
      value === undefined ||
      value == null ||
      value == 'null' ||
      value == ''
    ) {
      throw new Error('请输入' + item.fieldCH)
    }
    if (checkString(value) && value.trim() == '') {
      throw new Error(item.fieldCH + '为空')
    }
  })
  return true
}

// 获取对象属性值
export const getObjValue = (obj, field) => {
  let value = obj[field]
  if (value === undefined || value == null) {
    value = undefined
  }
  if (checkString(value)) {
    if (value.trim() == '') {
      value = undefined
    }
  }
  return value
}

// 加工发送对象的参数
export const machineSendParams = (obj, fieldArr) => {
  let sendParams = {}
  fieldArr.some((item) => {
    sendParams[item] = getObjValue(obj, item)
  })
  return sendParams
}

// 判断是否存在中文
export const checkHaveChinese = (obj) => {
  return escape(obj).indexOf('%u') != -1
}

// 判断是否为纯数字
export const checkAllNumber = (obj) => {
  var reg = /^\d+$/
  return reg.test(obj)
}

// 校验账号
export const checkAccount = (account) => {
  if (account === undefined) {
    throw new Error('未能检测到账号')
  }
  if (!checkString(account)) {
    throw new Error('检测账号为非字符串')
  }
  account = account.trim()
  if (checkHaveChinese(account)) {
    throw new Error('请不要设置中文账号')
  }
  return account
}

// 校验密码
export const checkPassword = (password) => {
  if (password === undefined) {
    throw new Error('未能检测到密码')
  }
  if (!checkString(password)) {
    throw new Error('检测密码为非字符串')
  }
  password = password.trim()
  if (checkHaveChinese(password)) {
    throw new Error('请不要设置中文密码')
  }
  let len = password.length
  if (len < 8) {
    throw new Error('密码长度过短，请设置8-18位长度密码')
  }
  if (len > 18) {
    throw new Error('密码长度过长，为避免遗忘，请设置8-18位长度密码')
  }
  if (checkAllNumber(password)) {
    throw new Error('请使用字母+数字组合的密码')
  }
  return password
}

// 校验手机号码
export const checkSjh = (phoneNumber) => {
  let reg = /^1(3|4|5|6|7|8|9)\d{9}$/
  return reg.test(phoneNumber)
}
// 校验电子邮箱
export const checkDzyx = (email) => {
  let reg = /^^([\s\S]*)+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  return reg.test(email)
}

// 校验是否存在数据，不存在则置为 undefined
export const checkToUndefined = (obj) => {
  if (checkString(obj) && obj.trim() == '') {
    obj = undefined
  }
  return obj
}

// // 获取assets静态资源
// export const getAssetsFile = (url) => {
//   return new URL(`../renderer/assets/${url}`).href
// }

/**
 * 获取数字类型px值(以第一个为准)
 * 12313112aaaapxaaaavv3333333aapxpx
 */
export const getIntPixel = (str) => {
  let reg = /[1-9][0-9]*(?=px.*)/g
  let arr = str.match(reg)
  if (arr) {
    return arr[0]
  }
  console.log('[' + str + ']', '中未找到px像素', arr)
  return 0
}

let chineseNumberArr = ['一', '二', '三', '四']

// 生成当前年份的季度
export const generatorCurrentYearQuarter = () => {
  let date = new Date()
  let year = date.getFullYear()
  let month = date.getMonth() + 1
  //
  let resObj = {
    currentQuarter: 0,
    resList: [
      {
        jcjdid: 1,
        jcjdmc: year + '年第一季度',
      },
      {
        jcjdid: 2,
        jcjdmc: year + '年第二季度',
      },
      {
        jcjdid: 3,
        jcjdmc: year + '年第三季度',
      },
      {
        jcjdid: 4,
        jcjdmc: year + '年第四季度',
      },
    ],
  }
  switch (month) {
    case 1:
      resObj.currentQuarter = 1
      break
    case 2:
      resObj.currentQuarter = 1
      break
    case 3:
      resObj.currentQuarter = 1
      break
    case 4:
      resObj.currentQuarter = 2
      break
    case 5:
      resObj.currentQuarter = 2
      break
    case 6:
      resObj.currentQuarter = 2
      break
    case 7:
      resObj.currentQuarter = 3
      break
    case 8:
      resObj.currentQuarter = 3
      break
    case 9:
      resObj.currentQuarter = 3
      break
    case 10:
      resObj.currentQuarter = 4
      break
    case 11:
      resObj.currentQuarter = 4
      break
    case 12:
      resObj.currentQuarter = 4
      break
  }
  return resObj
}

/**
 * 任意输入获取时间戳，非法抛出异常
 * Invalid date
 * 2022年12月20日
 * 2022.12月20号
 * 2022/12月20/
 * 2022/12-20/
 *
 * 2022/12-20/11:22:32
 * 2022年12-20号11:22:32
 *
 * 2022年12-20号 11时22:32
 *
 * 2022年12-20号11:22:32.123
 */
export const getDateTime = (time) => {
  if(!time) {
    throw new Error('无法解析该格式日期[' + time+']')
  }
  let timeType = Object.prototype.toString.call(time)
  if (timeType == '[object Date]') {
    return time.getTime()
  }
  if (timeType == '[object String]') {
    // 判断是否存在汉字，存在则直接替换成/
    let timeArr = time.split(' ')
    // console.log('getDateTime', timeArr)
    let timeStr = timeArr[0].replace(/[\u4e00-\u9fa5]/g, '/')
    if (timeArr[1]) {
      timeStr += timeArr[1].replace(/[\u4e00-\u9fa5]/g, ':')
    }
    let resDate = new Date(timeStr)
    if (resDate == 'Invalid Date') {
      throw new Error('无法解析该格式日期[' + time+']')
    }
    return resDate.getTime()
  }
}

// excel sheet 列字母索引（只有大写）
export const colsLetterArr = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z']
/**
 * 计算sheet页两个索引之间的距离(例：A1-DQP7)
 * messageCol不为空则表示存在异常情况
*/
export const computeDistanceIndex = (startIndex, endIndex) => {
  let resObj = {}
  // 提取数字计算行距
  resObj.rowDistance = endIndex.match(/\d+/g)[0] - startIndex.match(/\d+/g)[0]
  // 提取字符计算列距
  let colPrefixLetterStartArr = startIndex.match(/[A-z]/g)
  let colPrefixLetterEndArr = endIndex.match(/[A-z]/g)
  // 计算列间距（不需要考虑行的问题，去除行数字只比较前缀即将开始和结束放在一行进行比较）
  console.log(colPrefixLetterStartArr.length <= 1 && colPrefixLetterEndArr.length <= 1)
  if(colPrefixLetterStartArr.length <= 1 && colPrefixLetterEndArr.length <= 1) {
    // 都是A-Z的情况(A1-Z1)
    resObj.colDistance = colsLetterArr.indexOf(colPrefixLetterEndArr[0])-colsLetterArr.indexOf(colPrefixLetterStartArr[0])
  } else {
    // 超过26个英文字母(AA1-AAB1)
    // // 数组倒置（后续计算间距时好计算一些）
    // colPrefixLetterStartArr.reverse()
    // colPrefixLetterEndArr.reverse()
    /**
     * 判断前缀位数是否相同
     * 1相同：
     *   1-1从首字母开始依次判断是否相同，找到不同计算间距
     * 2不同：
     *   2-1计算区间
    */
    // 正序不同索引
    let diffIndexStart
    let diffIndexEnd
    // 倒序不同索引
    let diffIndexReverseStart
    let diffIndexReverseEnd
    //
    if(colPrefixLetterStartArr.length == colPrefixLetterEndArr.length) {
      //
      colPrefixLetterStartArr.some((startItem, startIndex) => {
        if(startItem != colPrefixLetterEndArr[startIndex]) {
          diffIndexStart = startIndex
          diffIndexEnd = startIndex
          return true
        }
      })
      diffIndexReverseStart = colPrefixLetterStartArr.length - (diffIndexStart)
      diffIndexReverseEnd = colPrefixLetterEndArr.length - (diffIndexEnd)
      console.log('diffIndexStart',diffIndexStart,diffIndexReverseStart,'diffIndexEnd',diffIndexEnd,diffIndexReverseEnd)
    }
    // 计算间距
    // 数组倒置，好计算一些
    colPrefixLetterStartArr.reverse()
    colPrefixLetterEndArr.reverse()
    // 总列数
    let colTotalNumStart = 0
    let colTotalNumEnd = 0
    // 起点总列数
    colPrefixLetterStartArr.some((item, index) => {
      if(diffIndexReverseStart == index) {
        return true
      }
      if(index == 0) {
        colTotalNumStart += colsLetterArr.indexOf(item)+1
      } else {
        // 26的次方
        let power26 = 1
        for(var i=0;i<index;i++) {
          power26 *= 26
        }
        colTotalNumStart += power26*(colsLetterArr.indexOf(item)+1)
      }
      // console.log('总列数(起点)', colTotalNumStart)
    })
    // 终点总列数
    colPrefixLetterEndArr.some((item, index) => {
      if(diffIndexReverseEnd == index) {
        return true
      }
      if(index == 0) {
        colTotalNumEnd += colsLetterArr.indexOf(item)+1
      } else {
        // 26的次方
        let power26 = 1
        for(var i=0;i<index;i++) {
          power26 *= 26
        }
        colTotalNumEnd += power26*(colsLetterArr.indexOf(item)+1)
      }
      // console.log('总列数(终点)', colTotalNumEnd)
    })
    // 计算列间距
    resObj.colDistance = colTotalNumEnd - colTotalNumStart
  }
  // 只计算距离，即两个索引之间的间隔，故行和列需要再减一
  let rowDistanceTemp = resObj.rowDistance
  if(rowDistanceTemp != 0) {
    rowDistanceTemp = rowDistanceTemp>=0?(rowDistanceTemp-1):(rowDistanceTemp+1)
  }
  let colDistanceTemp = resObj.colDistance
  if(colDistanceTemp != 0) {
    colDistanceTemp = colDistanceTemp>=0?(colDistanceTemp-1):(colDistanceTemp+1)
  }
  resObj.rowDistance = rowDistanceTemp
  resObj.colDistance = colDistanceTemp
  // 最后返回结果校验（防止出现NaN）
  if(isNaN(resObj.rowDistance)) {
    resObj.rowDistance = 0
    resObj.messageRow = '行间距为NaN'
  }
  if(isNaN(resObj.colDistance)) {
    resObj.colDistance = 0
    resObj.messageCol = '列间距为NaN'
  }
  return resObj
}
