import {createAPI, createFileAPI,createDown,createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
// 通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
// 根据原jlid查询设备清单
export const getSbqdListByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/getSbqdListByYjlid", 'get',data)
// 设备清单批量添加
export const savaSbqdBatch = data => createAPI(BASE_URL+"/sbgl/sbqd/savaSbqdBatch", 'post',data)
// 根据原jlid删除原jlid下的设备清单
export const deleteSbqdByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/deleteSbqdByYjlid", 'post',data)

//日常工作
//添加计算机系统维护
export const submitJsj = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/submitJsj", 'post',data)
//修改计算机系统维护
export const updateJsj = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/updateJsj", 'post',data)
// 查找带分页
export const selectJsjPage = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/selectJsjPage", 'get',data)
//删除
export const removeJsj = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/removeJsj", 'post',data)
// 根据记录id查询
export const getJsjInfo = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/getJsjInfo", 'get',data)
// 查询所有计算机系统维护（不带分页）
export const getAllJsj = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/sbgl_sbxdwcdj/getAllJsj", 'get',data)
// 根据slid查询计算机系统维护
export const getJsjInfoBySlid = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/getJsjInfoBySlid", 'get',data)
// 根据slid获取jlid
export const getJsjJlid = data => createAPI(BASE_URL+"/sbgl-jsjxtwh/getJlid", 'get',data)

//登记表
//添加系统维护登记表
export const submitXtwhdj = data => createAPI(BASE_URL+"/sbgl-xtwhdj/submitXtwhdj", 'post',data)
//分页查询系统维护登记
export const selectXtwhdjPage = data => createAPI(BASE_URL+"/sbgl-xtwhdj/selectXtwhdjPage", 'get',data)
//根据jlid获取系统维护登记信息详情
export const getXtwhdjInfo = data => createAPI(BASE_URL+"/sbgl-xtwhdj/getXtwhdjInfo", 'get',data)