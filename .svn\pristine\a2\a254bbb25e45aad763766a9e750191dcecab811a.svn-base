{"name": "bmgj-wlb", "version": "1.0.0", "description": "A Vue.js project", "author": "tanzhenjie <<EMAIL>>", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider node build/build.js", "fix-memory-limit": "cross-env LIMIT=8096 increase-memory-limit"}, "dependencies": {"adm-zip": "^0.5.10", "axios": "1.5.0", "babel-plugin-dynamic-import-node": "^2.3.3", "crypto-js": "^4.1.1", "docx-preview": "^0.3.0", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "electron": "^22.1.0", "element-ui": "^2.15.12", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "md5": "^2.3.0", "moment": "^2.29.4", "original-fs": "^1.2.0", "pdfjs-dist-sign": "^2.5.208", "pubsub-js": "^1.9.4", "readline": "^1.3.0", "vue": "^2.5.2", "vue-axios": "^3.5.2", "vue-pdf": "4.0.7", "vue-router": "^3.0.1", "vuex": "^3.6.2", "vuex-along": "^1.2.13", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "cross-env": "^7.0.3", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "increase-memory-limit": "^1.0.7", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}