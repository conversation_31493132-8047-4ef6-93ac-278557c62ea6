<!--  -->
<template>
  <div class="box" v-loading="pageloading">
    <div class="top-box">
      <div>
        <img src="./img/title.png" alt="" />
      </div>
      <div class="top-title">设备迁移信息</div>
    </div>
    <!-- <div style="width: 60%;margin: 0 auto;margin-top: 20px;"> -->
    <!-- <el-form
        ref="form"
        :model="form"
        label-width="140px"
        label-position="left"
      >
        <div style="display: flex;">
          <el-form-item label="机房编号" class="mg20">
            <el-input v-model="form.computerRoomCode" disabled></el-input>
          </el-form-item>
          <el-form-item label="机房地点">
            <el-input v-model="form.computerRoomName" disabled></el-input>
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item label="设备类型" class="mg20">
            <el-input v-model="form.csm" disabled></el-input>
          </el-form-item>
          <el-form-item label="设备编号">
            <el-input v-model="form.equipmentCode" disabled></el-input>
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item label="迁移所在地" class="mg20">
            <el-input v-model="form.relocationLocation" disabled></el-input>
          </el-form-item>
          <el-form-item label="迁移所在机构">
            <el-input v-model="form.relocationInstitution" disabled></el-input>
          </el-form-item>
        </div>
        <div style="display: flex;">
          <el-form-item label="迁移机房" class="mg20">
            <el-input
              v-model="form.relocationComputerRoomName"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="迁移机柜">
            <el-input v-model="form.relocationCabinetName" disabled></el-input>
          </el-form-item>
        </div>
      </el-form> -->

    <div>
      <el-form
        class="inputcss"
        ref="form"
        :model="form"
        label-width="100px"
        label-position="left"
        style="margin-top: 20px"
      >
        <div style="display: flex">
          <el-form-item label="设备名称" style="margin-left: 20px">
            <el-input v-model="form.sbmc"></el-input>
          </el-form-item>
          <el-form-item label="设备编号" style="margin-left: 20px">
            <el-input v-model="form.sbbh"></el-input>
          </el-form-item>
          <div class="cxbtn" @click="getequipmentByRoomList">查询</div>
          <div class="buttonw btnc3" @click="batchOpenCabinet">批量开柜门</div>
          <div class="buttonw btnc2" @click="dcbutton()">提交并导出</div>
        </div>
      </el-form>
      <!-- <div class="buttonw btnc1" @click="savetj()">提交</div> -->
    </div>

    <div style="width: 100%; margin-top: 20px">
      <el-table
        ref="table"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="tableHeaderCellStyle"
        :cell-style="tableCellStyle"
        max-height="800px"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center">
        </el-table-column>
        <el-table-column prop="cabinetCode" label="机柜编号" align="center">
        </el-table-column>
        <el-table-column prop="equipmentCode" label="设备编号" align="center">
        </el-table-column>
        <el-table-column prop="equipmentName" label="设备名称" align="center">
        </el-table-column>
        <el-table-column
          prop="relocationLocation"
          label="迁移所在地"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="relocationInstitution"
          label="迁移所在机构"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="relocationComputerRoomName"
          label="迁移机房"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="relocationCabinetName"
          label="迁移机柜"
          align="center"
        >
        </el-table-column>
        <el-table-column label="柜门状态" align="center" width="100">
          <template slot-scope="scope">
            <span
              :style="{ color: scope.row.flag === 1 ? '#67C23A' : '#F56C6C' }"
            >
              {{ scope.row.flag === 1 ? "开启" : "关闭" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <el-button
                type="primary"
                size="mini"
                :disabled="scope.row.flag === 1"
                @click="openSingleCabinet(scope.row)"
              >
                开柜门
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 15px">
        <el-pagination
          background
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :pager-count="5"
          :current-pageNo="page"
          :pageNo-sizes="[5, 10, 20, 30]"
          :pageNo-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- </div> -->
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  queryEquipmentByCondition,
  saveMigrateEquipment,
  exportMigrateEquipment,
  getequipmentByRoom,
  openCabinetByPort,
} from "../../../api/jfxj";
import { removeFmwlsb } from "../../../api";
export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      form: {
        jfbh: "",
        jfdd: "",
        sblx: "",
        xbbh: "",
        qysj: "",
        qydz: "",
        sbmc: "",
        sbbh: "",
        relocationCabinetCode: "",
        relocationCabinetName: "",
        relocationComputerRoomName: "",
        relocationInstitution: "",
        relocationLocation: "",
        area: "",
      },
      flag: "",
      sfdc: false,
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
      pageloading: false,
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    handleCurrentChange() {},
    handleSizeChange() {},
    tableCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;";
    },
    tableHeaderCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;";
    },

    async getequipmentByRoomList() {
      this.pageloading = true;
      try {
        const params = {
          scanCode: this.$route.query.scanCode,
          flag: 1,
          equipmentName: this.form.sbmc,
          equipmentCode: this.form.sbbh,
          pageNum: this.page,
          pageSize: this.pageSize,
        };
        const response = await getequipmentByRoom(params);
        const { code, data } = response;
        if (code === 10000) {
          this.tableData = data.records;
          this.total = data.total;
          this.pageloading = false;
        }
      } catch (error) {
        console.log(error);
      }
    },

    // async queryEquipmentByCondition() {
    //   console.log(
    //     this.$route.query.equipmentId,
    //     this.$route.query.equipmentCode,
    //   );

    //   queryEquipmentByCondition({
    //     equipmentCode: this.$route.query.equipmentId
    //   }).then(res => {
    //     console.log(res);
    //     this.form = res.data[0];
    //     this.form.relocationCabinetCode = this.$route.query.relocationCabinetCode;
    //     this.form.relocationCabinetName = this.$route.query.relocationCabinetName;
    //     this.form.relocationComputerRoomName = this.$route.query.relocationComputerRoomName;
    //     this.form.relocationInstitution = this.$route.query.relocationInstitution;
    //     this.form.relocationLocation = this.$route.query.relocationLocation;
    //     this.form.area = this.$route.query.area;
    //     this.flag = this.$route.query.flag;
    //   });
    // },
    async savetj() {},
    // async dcbutton() {
    //   if (this.flag == '1') {
    //     // 回抛退出事件给父组件，用以退出系统
    //     this.$confirm('目前设备所在机柜与迁移机柜一致，是否确认提交？', '提示', {
    //       cancelButtonClass: "btn-custom-cancel",
    //       confirmButtonText: '确定',
    //       cancelButtonText: '取消',
    //       type: 'warning',
    //       // center: true
    //     }).then(() => {
    //       saveMigrateEquipment(this.form).then(res => {
    //         if (res.code == 10000) {
    //           this.$message.success("提交成功");
    //           this.flag = '1';
    //           exportMigrateEquipment(this.form).then(res => {
    //             this.dom_download(res, "设备迁移信息" + ".xls");
    //           });
    //         }
    //       });
    //     }).catch(() => {
    //     })
    //     return
    //   }
    //   else {
    //     saveMigrateEquipment(this.form).then(res => {
    //       if (res.code == 10000) {
    //         this.$message.success("提交成功");
    //         this.flag = '1';
    //         exportMigrateEquipment(this.form).then(res => {
    //           this.dom_download(res, "设备迁移信息" + ".xls");
    //         });
    //       }
    //     });
    //   }
    // },

    async dcbutton() {
      try {
        // 获取用户选中的行
        const selectedRows = this.$refs.table.selection; // 假设你的 el-table 的 ref 是 "table"
        const formDataArray = [];

        // 如果有选中的行，只传递选中的行
        if (selectedRows.length > 0) {
          selectedRows.forEach((row) => {
            formDataArray.push({
              area: row.area,
              jfbh: row.jfbh,
              jfdd: row.jfdd,
              qydz: row.qydz,
              qysj: row.qysj,
              relocationCabinetCode: row.relocationCabinetCode,
              relocationCabinetName: row.relocationCabinetName,
              relocationComputerRoomName: row.relocationComputerRoomName,
              relocationInstitution: row.relocationInstitution,
              relocationLocation: row.relocationLocation,
              sbbh: row.sbbh,
              sblx: row.sblx,
              sbmc: row.sbmc,
              xbbh: row.xbbh,
              equipmentCode: row.equipmentCode,
              flag: 1,
            });
          });
        } else {
          // 如果没有选中的行，提示用户是否导出全部数据
          await this.$confirm("未选中任何行，是否导出全部数据？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          });

          // 用户确认导出全部数据
          this.tableData.forEach((row) => {
            formDataArray.push({
              area: row.area,
              jfbh: row.jfbh,
              jfdd: row.jfdd,
              qydz: row.qydz,
              qysj: row.qysj,
              relocationCabinetCode: row.relocationCabinetCode,
              relocationCabinetName: row.relocationCabinetName,
              relocationComputerRoomName: row.relocationComputerRoomName,
              relocationInstitution: row.relocationInstitution,
              relocationLocation: row.relocationLocation,
              sbbh: row.sbbh,
              sblx: row.sblx,
              sbmc: row.sbmc,
              xbbh: row.xbbh,
              equipmentCode: row.equipmentCode,
              flag: 1,
            });
          });
        }

        // 如果没有数据可供导出，直接返回
        if (formDataArray.length === 0) {
          this.$message.warning("没有数据可供导出");
          return;
        }

        // 调用保存接口
        const saveResponse = await saveMigrateEquipment(formDataArray);
        if (saveResponse.code !== 10000) {
          this.$message.error("保存失败：" + saveResponse.message);
          return;
        }

        // 从保存接口的响应中获取 data 数组
        const idsFromSave = saveResponse.data;

        // 构建导出接口需要的参数数组
        const exportParams = idsFromSave.map((id) => ({ id: id }));

        // 调用导出接口，传递 id 参数数组
        const exportResponse = await exportMigrateEquipment(exportParams);
        if (exportResponse.code !== 10000) {
          this.$message.error(exportResponse.message);
          return;
        }

        // 导出接口返回的是文件流，直接处理下载
        this.$message.success("导出成功");
      } catch (error) {
        if (error === "cancel") {
          this.$message.info("已取消导出操作");
        } else {
          console.error("导出失败：", error);
          this.$message.error("导出失败，请稍后重试");
        }
      }
      this.getequipmentByRoomList();
    },

    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      console.log("dom", dom);
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },

    // 单个开柜门
    async openSingleCabinet(row) {
      if (row.flag === 1) {
        this.$message.warning("柜门已开启，无需重复操作");
        return;
      }

      if (!row.port) {
        this.$message.error("该设备缺少端口信息，无法开启柜门");
        return;
      }

      this.pageloading = true;

      let params = {
        port: [row.port],
        cabinetCode: [row.cabinetCode],
      };

      try {
        const response = await openCabinetByPort(params);
        if (response.code === 10000) {
          this.$message.success("开柜门成功");
          // 刷新列表以更新柜门状态
          this.getequipmentByRoomList();
          this.pageloading = false;
        } else {
          this.$message.error(response.message || "开柜门失败");
          this.pageloading = false;
        }
      } catch (error) {
        this.pageloading = false;
        console.error("开柜门失败：", error);
        this.$message.error("开柜门失败，请稍后重试");
      }
    },

    // 批量开柜门
    async batchOpenCabinet() {
      const selectedRows = this.$refs.table.selection;

      if (selectedRows.length === 0) {
        this.$message.warning("请先选择要开启柜门的设备");
        return;
      }

      // 过滤出可以开启柜门的设备（柜门状态为关闭且有port字段）
      const validRows = selectedRows.filter(
        (row) => row.flag !== 1 && row.port
      );

      if (validRows.length === 0) {
        this.$message.warning("所选设备的柜门均已开启或缺少端口信息");
        return;
      }

      // 如果有部分设备不能开启，提示用户
      if (validRows.length < selectedRows.length) {
        const invalidCount = selectedRows.length - validRows.length;
        const confirmResult = await this.$confirm(
          `所选设备中有 ${invalidCount} 个设备的柜门已开启或缺少端口信息，是否继续开启其余 ${validRows.length} 个设备的柜门？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        ).catch(() => false);

        if (!confirmResult) {
          return;
        }
      }

      try {
        const ports = validRows.map((row) => row.port);
        const cabinetCodes = validRows.map((row) => row.cabinetCode);

        this.pageloading = true;

        let params = {
          port: ports,
          cabinetCode: cabinetCodes,
        };
        const response = await openCabinetByPort(params);

        if (response.code === 10000) {
          this.$message.success(`成功开启 ${validRows.length} 个柜门`);
          // 刷新列表以更新柜门状态
          this.getequipmentByRoomList();

          this.pageloading = false;
        } else {
          this.$message.error(response.message || "批量开柜门失败");
          this.pageloading = false;
        }
      } catch (error) {
        console.error("批量开柜门失败：", error);
        this.$message.error("批量开柜门失败，请稍后重试");
        this.pageloading = false;
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.queryEquipmentByCondition();

    this.getequipmentByRoomList();
  },
  //生命周期 - 创建之前
  beforeCreate() {},
  //生命周期 - 挂载之前
  beforeMount() {},
  //生命周期 - 更新之前
  beforeUpdate() {},
  //生命周期 - 更新之后
  updated() {},
  //生命周期 - 销毁之前
  beforeDestroy() {},
  //生命周期 - 销毁完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {},
};
</script>
<style scoped>
.box {
  width: 1580px;
  margin: 0 auto;
}

.cxbtn {
  width: 72px;
  height: 32px;
  background: #3e9efe;
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0.07px;
  font-weight: 400;
  text-align: center;
  line-height: 32px;
  margin-left: 20px;
  margin-top: 5px;
}

/* .inputcss /deep/ .el-input__inner {
  width: 210px !important;
  height: 32px;
  border-radius: 4px;
}
.inputcss /deep/ .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #080808;
  font-weight: 400;
} */
.top-box {
  width: 100%;
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 20px;
}

.top-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
  margin-left: 10px;
}

.mg20 {
  margin-right: 20px;
}

.buttonw {
  cursor: pointer;
  width: 123px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.btnc1 {
  background-color: #3e9efe;
}

.btnc2 {
  background-color: #3ecafe;
  margin-left: 20px;
  margin-top: 5px;
}

.btnc3 {
  background-color: #67c23a;
  margin-left: 20px;
  margin-top: 5px;
}

::v-deep .el-form-item__content {
  width: 350px !important;
}

::v-deep .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

::v-deep .el-form--label-left .el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 20px;
  color: #080808;
  font-weight: 400;
}

::v-deep .el-input.is-disabled .el-input__inner {
  font-size: 18px;
}
</style>
