import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、设备借用表单提交
export const submitSbjy = data => createAPI(BASE_URL+"/sbgl/sbjy/submitSbjy", 'post',data)
//2.日常工作设备借用查询
export const selectSbjyPage = data => createAPI(BASE_URL+"/sbgl/sbjy/selectSbjyPage", 'get',data)
//4.通过记录id查询设备借用详情
export const getSbjyInfoByJlid = data => createAPI(BASE_URL+"/sbgl/sbjy/getSbjyInfoByJlid", 'get',data)
//6.修改设备借用表
export const updateSbjyByJlid = data => createAPI(BASE_URL+"/sbgl/sbjy/updateSbjyByJlid", 'post',data)
//5.通过记录id删除设备借用记录
export const deleteSbjy = data => createAPI(BASE_URL+"/sbgl/sbjy/deleteSbjy", 'post',data)
//7.通过实例id获取设备借用表的jlid
export const getSbjyJlidBySlid = data => createAPI(BASE_URL+"/sbgl/sbjy/getSbjyJlidBySlid", 'get',data)
//3.通过实例id查询设备借用详情
export const getSbjyInfoBySlid = data => createAPI(BASE_URL+"/sbgl/sbjy/getSbjyInfoBySlid", 'get',data)
//1.批量添加设备借用登记记录
export const savaSbjydjBatch = data => createAPI(BASE_URL+"/sbgl/jydj/savaSbjydjBatch", 'post',data)
//2.设备借用登记条件查询带分页
export const selectSbjydjPage = data => createAPI(BASE_URL+"/sbgl/jydj/selectSbjydjPage", 'get',data)
//4.通过slid修改设备借用登记表记录
export const updateSbjydj = data => createAPI(BASE_URL+"/sbgl/jydj/updateSbjydj", 'post',data)
//4.通过slid修改设备借用登记表记录
export const getSbjydjByJlid = data => createAPI(BASE_URL+"/sbgl/jydj/getSbjydjByJlid", 'get',data)
export const getSbCqjySlidByJlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getSbCqjySlidByJlid", 'get',data)


