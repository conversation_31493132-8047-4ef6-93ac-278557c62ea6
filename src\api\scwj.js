import {createAPI, createFileAPI, createUploadAPI,createDownloadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

//上传国家秘密事项一览表
export const saveDmsxfj = data => createAPI(BASE_URL+"/dmgl/dmsxylb/fj/saveDmsxfj", 'post',data)
//下载国家秘密事项一览表附件
export const downloadDmsxylbFile = data => createDownloadAPI(BASE_URL+"/dmgl/dmsxylb/fj/downloadDmsxylbFile", 'get',data)


//导出下载保密制度附件
export const downloadDmzdFile = data => createDownloadAPI(BASE_URL+"/bmzt/djb/downloadDmzdFile", 'get',data)
//下载接口例子
// export const exportWord = data => createDownloadAPI(BG_URL + '/exportWord', 'post', data);