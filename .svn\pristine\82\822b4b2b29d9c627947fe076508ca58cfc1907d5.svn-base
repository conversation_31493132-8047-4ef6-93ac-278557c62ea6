import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex)
const store = new Vuex.Store({
    state: {
        //公共
        comm: {
            loading: false,
            login: {
                memberId: '',
                userData: ''
            },
            indexConf: {
                isTab: false, //是否显示tab页
                isBack: false, //是否显示返回
                title: '' //标题
            }
        }
    },
    mutations: {
        /*
         * 修改header的信息
         *比如是否有回退按钮，标题显示内容
         * */
        changeIndexConf: (state, data) => {

            Object.assign(state.comm.indexConf, data);
        }
    },
    actions: {     
    },
    getter: {
    }
});
export default store