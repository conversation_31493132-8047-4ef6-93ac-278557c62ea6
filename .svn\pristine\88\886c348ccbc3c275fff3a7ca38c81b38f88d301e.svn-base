{"version": 3, "sources": ["webpack:///src/renderer/view/ztqk/gzcllcbz.vue", "webpack:///./src/renderer/view/ztqk/gzcllcbz.vue?a56b", "webpack:///./src/renderer/view/ztqk/gzcllcbz.vue"], "names": ["gzcllcbz", "name", "components", "props", "data", "active", "form", "id", "reportOrganiaztion", "manufacturerName", "<PERSON><PERSON><PERSON>", "manufacturerPhone", "replaceFlag", "equipmentSerialNumber", "this", "$route", "query", "equipmentSerialNumberNew", "maintenanceStatus", "equipmentCode", "reason", "faultHandlingTime", "assignee", "computed", "watch", "methods", "init", "_this", "asyncToGenerator_default", "regenerator_default", "a", "mark", "_callee", "wrap", "_context", "prev", "next", "Object", "jfxj", "sent", "code", "stop", "xyb", "_this2", "_callee2", "_context2", "then", "res", "syb", "dc<PERSON><PERSON>", "_this3", "_callee3", "_context3", "dom_download", "$message", "message", "type", "content", "fileName", "blob", "Blob", "url", "window", "URL", "createObjectURL", "dom", "document", "createElement", "console", "log", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "created", "mounted", "now", "Date", "formattedDate", "getFullYear", "String", "getMonth", "padStart", "getDate", "beforeCreate", "beforeMount", "beforeUpdate", "updated", "<PERSON><PERSON><PERSON><PERSON>", "destroyed", "activated", "ztqk_gzcllcbz", "render", "_vm", "_h", "$createElement", "_c", "_self", "staticClass", "_m", "_v", "staticStyle", "width", "margin", "margin-top", "attrs", "finish-status", "title", "margin-left", "label", "model", "value", "callback", "$$v", "$set", "expression", "_e", "position", "placement", "trigger", "margin-bottom", "color", "top", "right", "slot", "justify-content", "ref", "label-width", "label-position", "disabled", "text-align", "on", "staticRenderFns", "src", "__webpack_require__", "alt", "Component", "normalizeComponent", "ssrContext", "__webpack_exports__"], "mappings": "oLAsNAA,GACAC,KAAA,GAEAC,cACAC,SACAC,KALA,WAOA,OACAC,OAAA,EACAC,MACAC,GAAA,GACAC,mBAAA,IACAC,iBAAA,GACAC,aAAA,GACAC,kBAAA,GACAC,YAAA,IACAC,sBAAAC,KAAAC,OAAAC,MAAAH,sBACAI,yBAAA,GACAC,kBAAA,GACAC,cAAAL,KAAAC,OAAAC,MAAAG,cACAC,OAAA,GACAC,kBAAA,GACAC,SAAA,MAKAC,YAEAC,SAEAC,SACAC,KADA,WACA,IAAAC,EAAAb,KAAA,OAAAc,IAAAC,EAAAC,EAAAC,KAAA,SAAAC,IAAA,IAAA5B,EAAA,OAAAyB,EAAAC,EAAAG,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,EACAC,OAAAC,EAAA,EAAAD,CAAAV,EAAArB,MADA,OAEA,MADAF,EADA8B,EAAAK,MAEAC,MACApC,OAAAG,KAEAoB,EAAArB,KAAAF,QALA,wBAAA8B,EAAAO,SAAAT,EAAAL,KAAAC,IASAc,IAVA,WAUA,IAAAC,EAAA7B,KAAA,OAAAc,IAAAC,EAAAC,EAAAC,KAAA,SAAAa,IAAA,OAAAf,EAAAC,EAAAG,KAAA,SAAAY,GAAA,cAAAA,EAAAV,KAAAU,EAAAT,MAAA,OACAO,EAAArC,KAAAY,kBAAAyB,EAAAtC,OACAgC,OAAAC,EAAA,EAAAD,CAAAM,EAAArC,MAAAwC,KAAA,SAAAC,GACA,KAAAA,EAAAP,OACAG,EAAArC,KAAAC,GAAAwC,EAAA3C,QAGAuC,EAAAtC,SAPA,wBAAAwC,EAAAJ,SAAAG,EAAAD,KAAAf,IASAoB,IAnBA,WAoBAlC,KAAAT,UAEA4C,SAtBA,WAsBA,IAAAC,EAAApC,KAAA,OAAAc,IAAAC,EAAAC,EAAAC,KAAA,SAAAoB,IAAA,OAAAtB,EAAAC,EAAAG,KAAA,SAAAmB,GAAA,cAAAA,EAAAjB,KAAAiB,EAAAhB,MAAA,OACAc,EAAA5C,KAAAY,kBAAAgC,EAAA7C,OACAgC,OAAAC,EAAA,EAAAD,CAAAa,EAAA5C,MAAAwC,KAAA,SAAAC,GACA,KAAAA,EAAAP,OACAU,EAAA5C,KAAAC,GAAAwC,EAAA3C,KACAiC,OAAAC,EAAA,EAAAD,CAAAa,EAAA5C,MAAAwC,KAAA,SAAAC,GACAG,EAAAG,aAAAN,EAAA,cACAG,EAAAI,UACAC,QAAA,OACAC,KAAA,iBATA,wBAAAJ,EAAAX,SAAAU,EAAAD,KAAAtB,IAgBAyB,aAtCA,SAsCAI,EAAAC,GACA,IAAAC,EAAA,IAAAC,MAAAH,IAEAI,EAAAC,OAAAC,IAAAC,gBAAAL,GACAM,EAAAC,SAAAC,cAAA,KACAC,QAAAC,IAAA,MAAAJ,GACAA,EAAAK,MAAAC,QAAA,OACAN,EAAAO,KAAAX,EACAI,EAAAQ,aAAA,WAAAf,GACAQ,SAAAQ,KAAAC,YAAAV,GACAA,EAAAW,UAIAC,QAnFA,aAqFAC,QArFA,WAuFAhE,KAAAY,OAEA,IAAAqD,EAAA,IAAAC,KAQAC,EALAF,EAAAG,cAKA,IAJAC,OAAAJ,EAAAK,WAAA,GAAAC,SAAA,OAIA,IAHAF,OAAAJ,EAAAO,WAAAD,SAAA,OAIAvE,KAAAR,KAAAe,kBAAA4D,EACAnE,KAAAR,KAAAa,cAAAL,KAAAC,OAAAC,MAAAG,cACAL,KAAAR,KAAAO,sBAAAC,KAAAC,OAAAC,MAAAH,uBAGA0E,aAvGA,aAyGAC,YAzGA,aA2GAC,aA3GA,aA6GAC,QA7GA,aA+GAC,cA/GA,aAiHAC,UAjHA,aAmHAC,UAnHA,cCxMeC,GADEC,OAbjB,WAA0B,IAAAC,EAAAlF,KAAamF,EAAAD,EAAAE,eAA0BC,EAAAH,EAAAI,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,QAAkBL,EAAAM,GAAA,GAAAN,EAAAO,GAAA,QAAAP,EAAA1F,KAAAM,YAAAuF,EAAA,OAA8DK,aAAaC,MAAA,MAAAC,OAAA,SAAAC,aAAA,UAAqDR,EAAA,YAAiBS,OAAOvG,OAAA2F,EAAA3F,OAAAwG,gBAAA,aAA+CV,EAAA,WAAgBS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,UAAe,OAAAX,EAAA,OAAoBK,aAAaC,MAAA,MAAAC,OAAA,SAAAC,aAAA,UAAqDR,EAAA,YAAiBS,OAAOvG,OAAA2F,EAAA3F,OAAAwG,gBAAA,aAA+CV,EAAA,WAAgBS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,SAAed,EAAAO,GAAA,KAAAJ,EAAA,WAA4BS,OAAOE,MAAA,UAAe,OAAAd,EAAAO,GAAA,SAAAP,EAAA3F,OAAA8F,EAAA,OAAmDK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,aAAuBL,EAAAO,GAAA,4CAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA2EK,aAAajC,QAAA,OAAAoC,aAAA,UAAsCR,EAAA,OAAYE,YAAA,gBAA0BL,EAAAO,GAAA,iCAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAgEK,aAAaO,cAAA,UAAsBZ,EAAA,YAAiBS,OAAOI,MAAA,KAAYC,OAAQC,MAAAlB,EAAA1F,KAAA,mBAAA6G,SAAA,SAAAC,GAA6DpB,EAAAqB,KAAArB,EAAA1F,KAAA,qBAAA8G,IAA8CE,WAAA,6BAAuCtB,EAAAO,GAAA,SAAAP,EAAAO,GAAA,KAAAJ,EAAA,YAA6CS,OAAOI,MAAA,KAAYC,OAAQC,MAAAlB,EAAA1F,KAAA,mBAAA6G,SAAA,SAAAC,GAA6DpB,EAAAqB,KAAArB,EAAA1F,KAAA,qBAAA8G,IAA8CE,WAAA,6BAAuCtB,EAAAO,GAAA,SAAAP,EAAAO,GAAA,KAAAJ,EAAA,YAA6CS,OAAOI,MAAA,KAAYC,OAAQC,MAAAlB,EAAA1F,KAAA,mBAAA6G,SAAA,SAAAC,GAA6DpB,EAAAqB,KAAArB,EAAA1F,KAAA,qBAAA8G,IAA8CE,WAAA,6BAAuCtB,EAAAO,GAAA,mBAAAP,EAAAuB,KAAAvB,EAAAO,GAAA,SAAAP,EAAA3F,OAAA8F,EAAA,OAA8EK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,WAAAG,aAAoCgB,SAAA,cAAuBxB,EAAAO,GAAA,mDAAAJ,EAAA,cAA6ES,OAAOa,UAAA,QAAAhB,MAAA,MAAAiB,QAAA,WAAqDvB,EAAA,OAAAA,EAAA,OAAsBK,aAAajC,QAAA,OAAAoD,gBAAA,UAAyCxB,EAAA,KAAUE,YAAA,eAAAG,aAAwCoB,MAAA,UAAAJ,SAAA,WAAAK,IAAA,SAAqD7B,EAAAO,GAAA,KAAAJ,EAAA,OAAwBE,YAAA,SAAmBL,EAAAO,GAAA,UAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAyCE,YAAA,SAAmBL,EAAAO,GAAA,wGAAAP,EAAAO,GAAA,KAAAJ,EAAA,KAAqIE,YAAA,eAAAG,aAAwCoB,MAAA,UAAAJ,SAAA,WAAAM,MAAA,MAAAD,IAAA,OAAkEjB,OAAQmB,KAAA,aAAmBA,KAAA,iBAAkB,GAAA/B,EAAAO,GAAA,KAAAJ,EAAA,OAA8BK,aAAajC,QAAA,OAAAoC,aAAA,OAAAqB,kBAAA,YAAiE7B,EAAA,WAAgB8B,IAAA,OAAArB,OAAkBK,MAAAjB,EAAA1F,KAAA4H,cAAA,QAAAC,iBAAA,UAAgEhC,EAAA,gBAAqBS,OAAOI,MAAA,UAAgBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,iBAAA6G,SAAA,SAAAC,GAA2DpB,EAAAqB,KAAArB,EAAA1F,KAAA,mBAAA8G,IAA4CE,WAAA,4BAAqC,GAAAtB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCS,OAAOI,MAAA,WAAiBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,aAAA6G,SAAA,SAAAC,GAAuDpB,EAAAqB,KAAArB,EAAA1F,KAAA,eAAA8G,IAAwCE,WAAA,wBAAiC,GAAAtB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCS,OAAOI,MAAA,UAAgBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,kBAAA6G,SAAA,SAAAC,GAA4DpB,EAAAqB,KAAArB,EAAA1F,KAAA,oBAAA8G,IAA6CE,WAAA,6BAAsC,aAAAtB,EAAAuB,KAAAvB,EAAAO,GAAA,SAAAP,EAAA3F,OAAA8F,EAAA,OAAkEK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDX,EAAAM,GAAA,GAAAN,EAAAO,GAAA,KAAAJ,EAAA,OAAkCK,aAAajC,QAAA,OAAAoC,aAAA,UAAsCR,EAAA,OAAYE,YAAA,gBAA0BL,EAAAO,GAAA,qCAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAoEK,aAAaO,cAAA,UAAsBZ,EAAA,YAAiBS,OAAOI,MAAA,KAAYC,OAAQC,MAAAlB,EAAA1F,KAAA,YAAA6G,SAAA,SAAAC,GAAsDpB,EAAAqB,KAAArB,EAAA1F,KAAA,cAAA8G,IAAuCE,WAAA,sBAAgCtB,EAAAO,GAAA,OAAAP,EAAAO,GAAA,KAAAJ,EAAA,YAA2CS,OAAOI,MAAA,KAAYC,OAAQC,MAAAlB,EAAA1F,KAAA,YAAA6G,SAAA,SAAAC,GAAsDpB,EAAAqB,KAAArB,EAAA1F,KAAA,cAAA8G,IAAuCE,WAAA,sBAAgCtB,EAAAO,GAAA,eAAAP,EAAAuB,KAAAvB,EAAAO,GAAA,SAAAP,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,YAAAuF,EAAA,OAAuGK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,aAAuBL,EAAAO,GAAA,uDAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAsFK,aAAajC,QAAA,OAAAoC,aAAA,OAAAqB,kBAAA,YAAiE7B,EAAA,WAAgB8B,IAAA,OAAArB,OAAkBK,MAAAjB,EAAA1F,KAAA4H,cAAA,QAAAC,iBAAA,UAAgEhC,EAAA,gBAAqBS,OAAOI,MAAA,aAAmBb,EAAA,YAAiBS,OAAOwB,SAAA,IAAcnB,OAAQC,MAAAlB,EAAA1F,KAAA,sBAAA6G,SAAA,SAAAC,GAAgEpB,EAAAqB,KAAArB,EAAA1F,KAAA,wBAAA8G,IAAiDE,WAAA,iCAA0C,GAAAtB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCS,OAAOI,MAAA,YAAkBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,yBAAA6G,SAAA,SAAAC,GAAmEpB,EAAAqB,KAAArB,EAAA1F,KAAA,2BAAA8G,IAAoDE,WAAA,oCAA6C,aAAAtB,EAAAuB,KAAAvB,EAAAO,GAAA,KAC/7K,IAAAP,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,aACA,IAAAoF,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,YACAuF,EAAA,OAAkBK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,WAAAG,aAAoC6B,aAAA,YAAuBrC,EAAAO,GAAA,6CAAAP,EAAAuB,KAAAvB,EAAAO,GAAA,SAAAP,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,YAAAuF,EAAA,OAAqIK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,aAAuBL,EAAAO,GAAA,qFAAAP,EAAAuB,KAAAvB,EAAAO,GAAA,KAC3Y,IAAAP,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,aACA,IAAAoF,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,YACAuF,EAAA,OAAkBK,aAAaC,MAAA,QAAAC,OAAA,SAAAC,aAAA,WAAwDR,EAAA,OAAYE,YAAA,WAAAG,aAAoC6B,aAAA,YAAuBrC,EAAAO,GAAA,6BAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAA4DK,aAAajC,QAAA,OAAAoC,aAAA,OAAAqB,kBAAA,YAAiE7B,EAAA,WAAgB8B,IAAA,OAAArB,OAAkBK,MAAAjB,EAAA1F,KAAA4H,cAAA,QAAAC,iBAAA,UAAgEhC,EAAA,gBAAqBS,OAAOI,MAAA,aAAmBb,EAAA,YAAiBS,OAAOwB,SAAA,IAAcnB,OAAQC,MAAAlB,EAAA1F,KAAA,cAAA6G,SAAA,SAAAC,GAAwDpB,EAAAqB,KAAArB,EAAA1F,KAAA,gBAAA8G,IAAyCE,WAAA,yBAAkC,GAAAtB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCS,OAAOI,MAAA,WAAiBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,OAAA6G,SAAA,SAAAC,GAAiDpB,EAAAqB,KAAArB,EAAA1F,KAAA,SAAA8G,IAAkCE,WAAA,kBAA2B,GAAAtB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCS,OAAOI,MAAA,aAAmBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,kBAAA6G,SAAA,SAAAC,GAA4DpB,EAAAqB,KAAArB,EAAA1F,KAAA,oBAAA8G,IAA6CE,WAAA,6BAAsC,GAAAtB,EAAAO,GAAA,KAAAJ,EAAA,gBAAqCS,OAAOI,MAAA,cAAoBb,EAAA,YAAiBc,OAAOC,MAAAlB,EAAA1F,KAAA,SAAA6G,SAAA,SAAAC,GAAmDpB,EAAAqB,KAAArB,EAAA1F,KAAA,WAAA8G,IAAoCE,WAAA,oBAA6B,aAAAtB,EAAAuB,KAAAvB,EAAAO,GAAA,KAAAJ,EAAA,OAA+CK,aAAajC,QAAA,OAAAkC,MAAA,QAAAuB,kBAAA,SAAAtB,OAAA,SAAAC,aAAA,UAAmG,GAAAX,EAAA3F,OAAA8F,EAAA,OAA8BE,YAAA,eAAAiC,IAA+B1D,MAAAoB,EAAAhD,OAAiBgD,EAAAO,GAAA,SAAAP,EAAAuB,KAAAvB,EAAAO,GAAA,KAE58C,IAAAP,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,aACA,IAAAoF,EAAA3F,QAAA,GAAA2F,EAAA1F,KAAAM,YAEqEoF,EAAAuB,KAArEpB,EAAA,OAAoBE,YAAA,gBAAAiC,IAAgC1D,MAAAoB,EAAAtD,OAAiBsD,EAAAO,GAAA,2BAAAP,EAAAO,GAAA,KAAAJ,EAAA,OAAmEE,YAAA,gBAAAiC,IAAgC1D,MAAAoB,EAAA/C,YAAsB+C,EAAAO,GAAA,kBAE7KgC,iBADjB,WAAoC,IAAatC,EAAbnF,KAAaoF,eAA0BC,EAAvCrF,KAAuCsF,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,YAAsBF,EAAA,OAAAA,EAAA,OAAsBS,OAAO4B,IAAMC,EAAQ,QAAiBC,IAAA,QAAlK5H,KAA8KyF,GAAA,KAAAJ,EAAA,OAA0BE,YAAA,cAAxMvF,KAAgOyF,GAAA,eAAuB,WAAc,IAAaN,EAAbnF,KAAaoF,eAA0BC,EAAvCrF,KAAuCsF,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAiBE,YAAA,aAAhFvF,KAAuGyF,GAAA,6BAAAJ,EAAA,MAAvGrF,KAAuGyF,GAAA,0BAAAJ,EAAA,MAAvGrF,KAAuGyF,GAAA,2CCThZ,IAcAoC,EAdyBF,EAAQ,OAcjCG,CACE5I,EACA8F,GATF,EAVA,SAAA+C,GACEJ,EAAQ,SAaV,kBAEA,MAUeK,EAAA,QAAAH,EAAiB", "file": "js/8.5d19b62b87911335d67c.js", "sourcesContent": ["<!--  -->\n<template>\n  <div class=\"box\">\n    <div class=\"top-box\">\n      <div>\n        <img src=\"./img/title.png\" alt=\"\" />\n      </div>\n      <div class=\"top-title\">故障处理流程</div>\n    </div>\n    <div\n      style=\"width: 70%;margin: 0 auto;margin-top: 50px;\"\n      v-if=\"form.replaceFlag == 1\"\n    >\n      <el-steps :active=\"active\" finish-status=\"success\">\n        <el-step title=\"步骤一\"></el-step>\n        <el-step title=\"步骤二\"></el-step>\n        <el-step title=\"步骤三\"></el-step>\n        <el-step title=\"步骤四\"></el-step>\n        <el-step title=\"步骤五\"></el-step>\n        <el-step title=\"步骤六\"></el-step>\n        <el-step title=\"步骤七\"></el-step>\n      </el-steps>\n    </div>\n    <div style=\"width: 70%;margin: 0 auto;margin-top: 50px;\" v-else>\n      <el-steps :active=\"active\" finish-status=\"success\">\n        <el-step title=\"步骤一\"></el-step>\n        <el-step title=\"步骤二\"></el-step>\n        <el-step title=\"步骤三\"></el-step>\n        <el-step title=\"步骤四\"></el-step>\n        <el-step title=\"步骤五\"></el-step>\n      </el-steps>\n    </div>\n    <div\n      style=\"width: 510px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"active === 1\"\n    >\n      <div class=\"bt-title\">\n        项目经理向省分中心以及国家中心维护组报备\n      </div>\n      <div style=\"display: flex;margin-top: 20px;\">\n        <div class=\"label-title\">\n          报备组织：\n        </div>\n        <div style=\"margin-left: 10px;\">\n          <el-radio v-model=\"form.reportOrganiaztion\" label=\"1\">省中心</el-radio>\n          <el-radio v-model=\"form.reportOrganiaztion\" label=\"2\">省移动</el-radio>\n          <el-radio v-model=\"form.reportOrganiaztion\" label=\"3\">北京移动组</el-radio>\n        </div>\n      </div>\n    </div>\n    <div\n      style=\"width: 600px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"active === 2\"\n    >\n      <div class=\"bt-title\" style=\"position: relative;\">\n        协调厂家工程师到现场处理故障，项目经理作全程旁站陪同。\n        <el-popover placement=\"right\" width=\"200\" trigger=\"hover\">\n          <div>\n            <div style=\"display:flex;margin-bottom:10px\">\n              <i\n                class=\"el-icon-info\"\n                style=\"color:#409eef;    position: relative;\n    top: 2px;\"\n              ></i>\n              <div class=\"tszt\">提示</div>\n            </div>\n            <div class=\"smzt\">\n              项目经理去省移动公司，在思安设备上取得故障处理授权（二维码）。对于时限要求高的故障（2小时以内），直接去机房，现场录入故障处理授权。\n            </div>\n          </div>\n          <i\n            class=\"el-icon-info\"\n            style=\"color:#409eef;position: absolute;    right: 0px;top: 7px;\"\n            slot=\"reference\"\n          ></i>\n        </el-popover>\n      </div>\n\n      <div style=\"display: flex;margin-top: 20px;justify-content: center;\">\n        <el-form\n          ref=\"form\"\n          :model=\"form\"\n          label-width=\"120px\"\n          label-position=\"left\"\n        >\n          <el-form-item label=\"厂家姓名\">\n            <el-input v-model=\"form.manufacturerName\" ></el-input>\n          </el-form-item>\n          <el-form-item label=\"工程师姓名\">\n            <el-input v-model=\"form.engineerName\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"联系方式\">\n            <el-input v-model=\"form.manufacturerPhone\"></el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n    <div\n      style=\"width: 718px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"active === 3\"\n    >\n      <div class=\"bt-title\">\n        携带配件到现场,现场处理故障。<br />判断是否需要更换新配件，如果不需要更换配件。<br />例如重启设备或者重新插拔线缆接头等。处理后，试运行。\n      </div>\n      <div style=\"display: flex;margin-top: 20px;\">\n        <div class=\"label-title\">\n          配件是否需要更换：\n        </div>\n        <div style=\"margin-left: 10px;\">\n          <el-radio v-model=\"form.replaceFlag\" label=\"1\">是</el-radio>\n          <el-radio v-model=\"form.replaceFlag\" label=\"2\">否</el-radio>\n        </div>\n      </div>\n    </div>\n    <div\n      style=\"width: 718px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"active === 4 && form.replaceFlag == 1\"\n    >\n      <div class=\"bt-title\">\n        记录故障备件序列号和新配件序列号，更换故障配件，新配件试运行。\n      </div>\n      <div style=\"display: flex;margin-top: 20px;justify-content: center;\">\n        <el-form\n          ref=\"form\"\n          :model=\"form\"\n          label-width=\"160px\"\n          label-position=\"left\"\n        >\n          <el-form-item label=\"故障设备序列号\">\n            <el-input v-model=\"form.equipmentSerialNumber\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"新配件序列号\">\n            <el-input v-model=\"form.equipmentSerialNumberNew\"></el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n    <div\n      style=\"width: 718px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"\n        (active === 5 && form.replaceFlag == 1) ||\n          (active === 4 && form.replaceFlag == 2)\n      \"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        国家中心维护组确认故障恢复，运行正常。\n      </div>\n    </div>\n    <div\n      style=\"width: 718px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"active === 6 && form.replaceFlag == 1\"\n    >\n      <div class=\"bt-title\">\n        厂家工程师将故障配件（除核心设备及配件）返厂。核心设备及配件带回省移动公司接口人处，进入待处理库，等待接口人的统一处理\n      </div>\n    </div>\n    <div\n      style=\"width: 718px;margin: 0 auto;margin-top: 100px;\"\n      v-if=\"\n        (active === 7 && form.replaceFlag == 1) ||\n          (active === 5 && form.replaceFlag == 2)\n      \"\n    >\n      <div class=\"bt-title\" style=\"text-align: center;\">\n        故障处理单\n      </div>\n      <div style=\"display: flex;margin-top: 20px;justify-content: center;\">\n        <el-form\n          ref=\"form\"\n          :model=\"form\"\n          label-width=\"180px\"\n          label-position=\"left\"\n        >\n          <el-form-item label=\"故障设备编号：\">\n            <el-input v-model=\"form.equipmentCode\" disabled></el-input>\n          </el-form-item>\n          <el-form-item label=\"故障原因：\">\n            <el-input v-model=\"form.reason\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"故障处理时间：\">\n            <el-input v-model=\"form.faultHandlingTime\"></el-input>\n          </el-form-item>\n          <el-form-item label=\"故障处理负责人：\">\n            <el-input v-model=\"form.assignee\"></el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n    <div\n      style=\"display: flex;width: 500px;justify-content: center;\nmargin: 0 auto;margin-top: 60px;\"\n    >\n      <div class=\"buttonw btnc\" @click=\"syb\" v-if=\"active != 1\">上一步</div>\n      <div\n        class=\"buttonw btnc1\"\n        @click=\"xyb\"\n        v-if=\"\n          !(\n            (active === 7 && form.replaceFlag == 1) ||\n            (active === 5 && form.replaceFlag == 2)\n          )\n        \"\n      >\n        下一步\n      </div>\n      <div class=\"buttonw btnc2\" @click=\"dcbutton\">打印故障处理单</div>\n    </div>\n  </div>\n</template>\n\n<script>\n//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）\n//例如：import 《组件名称》 from '《组件路径》';\nimport {saveFaultHandling, exportFaultHandling, getInitFaultHandling} from \"../../../api/jfxj\";\nexport default {\n  name: \"\",\n  //import引入的组件需要注入到对象中才能使用\n  components: {},\n  props: {},\n  data() {\n    //这里存放数据\n    return {\n      active: 1,\n      form: {\n        id: \"\",\n        reportOrganiaztion: \"1\",\n        manufacturerName: \"\",\n        engineerName: \"\",\n        manufacturerPhone: \"\",\n        replaceFlag: \"2\",\n        equipmentSerialNumber: this.$route.query.equipmentSerialNumber,\n        equipmentSerialNumberNew: \"\",\n        maintenanceStatus:\"\",\n        equipmentCode: this.$route.query.equipmentCode,\n        reason: \"\",\n        faultHandlingTime: \"\",\n        assignee: \"\"\n      }\n    };\n  },\n  //监听属性 类似于data概念\n  computed: {},\n  //监控data中的数据变化\n  watch: {},\n  //方法集合\n  methods: {\n    async init() {\n      let data = await getInitFaultHandling(this.form);\n      if (data.code == 10000) {\n        if(data.data.id)\n        {\n          this.form = data.data;\n        }\n      }\n    },\n    async xyb() {\n      this.form.maintenanceStatus = this.active;\n      saveFaultHandling(this.form).then(res=>{\n          if (res.code == 10000) {\n            this.form.id = res.data;\n          }\n      })\n      this.active++;\n    },\n    syb() {\n      this.active--;\n    },\n    async dcbutton() {\n      this.form.maintenanceStatus = this.active;\n      saveFaultHandling(this.form).then(res=>{\n        if (res.code == 10000) {\n          this.form.id = res.data;\n          exportFaultHandling(this.form).then(res => {\n            this.dom_download(res, \"故障处理信息\" + \".xls\");\n            this.$message({\n              message: '打印成功',\n              type: 'success'\n            });\n          });\n        }\n      })\n    },\n    //处理下载流\n    dom_download(content, fileName) {\n      const blob = new Blob([content]) //创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象\n      //console.log(blob)\n      const url = window.URL.createObjectURL(blob) //URL.createObjectURL(object)表示生成一个File对象或Blob对象\n      let dom = document.createElement('a') //设置一个隐藏的a标签，href为输出流，设置download\n      console.log(\"dom\", dom);\n      dom.style.display = 'none'\n      dom.href = url\n      dom.setAttribute('download', fileName) //指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件\n      document.body.appendChild(dom)\n      dom.click()\n    },\n  },\n  //生命周期 - 创建完成（可以访问当前this实例）\n  created() {},\n  //生命周期 - 挂载完成（可以访问DOM元素）\n  mounted() {\n\n    this.init();\n    // 创建一个新的Date对象，获取当前日期和时间\n    var now = new Date();\n\n// 获取年、月、日\nvar year = now.getFullYear();\nvar month = String(now.getMonth() + 1).padStart(2, \"0\"); // 月份从0开始，需要加1，并且格式化为两位数\nvar day = String(now.getDate()).padStart(2, \"0\"); // 格式化为两位数\n\n// 组合成年-月-日的格式\nvar formattedDate = year + \"-\" + month + \"-\" + day;\nthis.form.faultHandlingTime = formattedDate;\n    this.form.equipmentCode = this.$route.query.equipmentCode\n    this.form.equipmentSerialNumber = this.$route.query.equipmentSerialNumber\n  },\n  //生命周期 - 创建之前\n  beforeCreate() {},\n  //生命周期 - 挂载之前\n  beforeMount() {},\n  //生命周期 - 更新之前\n  beforeUpdate() {},\n  //生命周期 - 更新之后\n  updated() {},\n  //生命周期 - 销毁之前\n  beforeDestroy() {},\n  //生命周期 - 销毁完成\n  destroyed() {},\n  //如果页面有keep-alive缓存功能，这个函数会触发\n  activated() {}\n};\n</script>\n<style scoped>\n.box {\n  width: 1580px;\n  margin: 0 auto;\n}\n.top-box {\n  width: 100%;\n  display: flex;\n  border-bottom: 1px solid #e5e5e5;\n  margin-top: 20px;\n}\n.top-title {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n  margin-left: 10px;\n}\n.bt-title {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n.label-title {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n.buttonw {\n  /* width: 72px;\n  height: 32px; */\n  padding: 0px 20px;\n  text-align: center;\n  line-height: 32px;\n  color: #fff;\n  border-radius: 4px;\n  cursor: pointer;\n}\n.btnc {\n  background-color: #3ecbfe;\n  margin-right: 20px;\n}\n.btnc1 {\n  background-color: #3e9efe;\n}\n.btnc2 {\n  background-color: #20bdd1;\n  margin-left: 20px;\n}\n.tszt {\n  font-family: KaiTi;\n  font-weight: 700;\n}\n\n.smzt {\n  font-size: 12px;\n}\n/deep/ .el-step__icon {\n  width: 36px;\n  height: 36px;\n}\n/deep/ .el-step.is-horizontal .el-step__line {\n  top: 18px;\n  left: 45px;\n  right: 12px;\n}\n/deep/ .el-step__head.is-process {\n  color: #fff;\n  border-color: #0077ff;\n}\n/deep/ .el-step__head.is-wait {\n  color: #fff;\n  border-color: #0077ff;\n}\n/deep/ .el-step__title.is-wait {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n/deep/ .el-step__title.is-process {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n/deep/ .el-step__icon.is-text {\n  border: 2px solid;\n  border-color: #0077ff;\n}\n/deep/ .el-step__head.is-success .is-text {\n  background-color: #0077ff;\n}\n/deep/ .el-step__head.is-success {\n  color: #fff;\n  border-color: #0077ff;\n}\n/deep/ .el-step__title.is-success {\n  font-family: SourceHanSansSC-Medium;\n  font-size: 22px;\n  color: #080808;\n  font-weight: 500;\n}\n/deep/ .el-input__inner {\n  width: 350px !important;\n  height: 32px;\n  border-radius: 4px;\n}\n/deep/ .el-form--label-left .el-form-item__label {\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-radio__label{\n  font-family: SourceHanSansSC-Regular;\n  font-size: 20px;\n  color: #080808;\n  font-weight: 400;\n}\n/deep/ .el-color-picker__icon, .el-input, .el-textarea{\n  font-size: 18px;\n}\n</style>\n\n\n\n// WEBPACK FOOTER //\n// src/renderer/view/ztqk/gzcllcbz.vue", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"box\"},[_vm._m(0),_vm._v(\" \"),(_vm.form.replaceFlag == 1)?_c('div',{staticStyle:{\"width\":\"70%\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('el-steps',{attrs:{\"active\":_vm.active,\"finish-status\":\"success\"}},[_c('el-step',{attrs:{\"title\":\"步骤一\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤二\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤三\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤四\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤五\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤六\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤七\"}})],1)],1):_c('div',{staticStyle:{\"width\":\"70%\",\"margin\":\"0 auto\",\"margin-top\":\"50px\"}},[_c('el-steps',{attrs:{\"active\":_vm.active,\"finish-status\":\"success\"}},[_c('el-step',{attrs:{\"title\":\"步骤一\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤二\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤三\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤四\"}}),_vm._v(\" \"),_c('el-step',{attrs:{\"title\":\"步骤五\"}})],1)],1),_vm._v(\" \"),(_vm.active === 1)?_c('div',{staticStyle:{\"width\":\"510px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\"},[_vm._v(\"\\n        项目经理向省分中心以及国家中心维护组报备\\n      \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"margin-top\":\"20px\"}},[_c('div',{staticClass:\"label-title\"},[_vm._v(\"\\n          报备组织：\\n        \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"margin-left\":\"10px\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.form.reportOrganiaztion),callback:function ($$v) {_vm.$set(_vm.form, \"reportOrganiaztion\", $$v)},expression:\"form.reportOrganiaztion\"}},[_vm._v(\"省中心\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"2\"},model:{value:(_vm.form.reportOrganiaztion),callback:function ($$v) {_vm.$set(_vm.form, \"reportOrganiaztion\", $$v)},expression:\"form.reportOrganiaztion\"}},[_vm._v(\"省移动\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"3\"},model:{value:(_vm.form.reportOrganiaztion),callback:function ($$v) {_vm.$set(_vm.form, \"reportOrganiaztion\", $$v)},expression:\"form.reportOrganiaztion\"}},[_vm._v(\"北京移动组\")])],1)])]):_vm._e(),_vm._v(\" \"),(_vm.active === 2)?_c('div',{staticStyle:{\"width\":\"600px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"position\":\"relative\"}},[_vm._v(\"\\n        协调厂家工程师到现场处理故障，项目经理作全程旁站陪同。\\n        \"),_c('el-popover',{attrs:{\"placement\":\"right\",\"width\":\"200\",\"trigger\":\"hover\"}},[_c('div',[_c('div',{staticStyle:{\"display\":\"flex\",\"margin-bottom\":\"10px\"}},[_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"relative\",\"top\":\"2px\"}}),_vm._v(\" \"),_c('div',{staticClass:\"tszt\"},[_vm._v(\"提示\")])]),_vm._v(\" \"),_c('div',{staticClass:\"smzt\"},[_vm._v(\"\\n              项目经理去省移动公司，在思安设备上取得故障处理授权（二维码）。对于时限要求高的故障（2小时以内），直接去机房，现场录入故障处理授权。\\n            \")])]),_vm._v(\" \"),_c('i',{staticClass:\"el-icon-info\",staticStyle:{\"color\":\"#409eef\",\"position\":\"absolute\",\"right\":\"0px\",\"top\":\"7px\"},attrs:{\"slot\":\"reference\"},slot:\"reference\"})])],1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"margin-top\":\"20px\",\"justify-content\":\"center\"}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"label-width\":\"120px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":\"厂家姓名\"}},[_c('el-input',{model:{value:(_vm.form.manufacturerName),callback:function ($$v) {_vm.$set(_vm.form, \"manufacturerName\", $$v)},expression:\"form.manufacturerName\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"工程师姓名\"}},[_c('el-input',{model:{value:(_vm.form.engineerName),callback:function ($$v) {_vm.$set(_vm.form, \"engineerName\", $$v)},expression:\"form.engineerName\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"联系方式\"}},[_c('el-input',{model:{value:(_vm.form.manufacturerPhone),callback:function ($$v) {_vm.$set(_vm.form, \"manufacturerPhone\", $$v)},expression:\"form.manufacturerPhone\"}})],1)],1)],1)]):_vm._e(),_vm._v(\" \"),(_vm.active === 3)?_c('div',{staticStyle:{\"width\":\"718px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_vm._m(1),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"margin-top\":\"20px\"}},[_c('div',{staticClass:\"label-title\"},[_vm._v(\"\\n          配件是否需要更换：\\n        \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"margin-left\":\"10px\"}},[_c('el-radio',{attrs:{\"label\":\"1\"},model:{value:(_vm.form.replaceFlag),callback:function ($$v) {_vm.$set(_vm.form, \"replaceFlag\", $$v)},expression:\"form.replaceFlag\"}},[_vm._v(\"是\")]),_vm._v(\" \"),_c('el-radio',{attrs:{\"label\":\"2\"},model:{value:(_vm.form.replaceFlag),callback:function ($$v) {_vm.$set(_vm.form, \"replaceFlag\", $$v)},expression:\"form.replaceFlag\"}},[_vm._v(\"否\")])],1)])]):_vm._e(),_vm._v(\" \"),(_vm.active === 4 && _vm.form.replaceFlag == 1)?_c('div',{staticStyle:{\"width\":\"718px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\"},[_vm._v(\"\\n        记录故障备件序列号和新配件序列号，更换故障配件，新配件试运行。\\n      \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"margin-top\":\"20px\",\"justify-content\":\"center\"}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"label-width\":\"160px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":\"故障设备序列号\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.equipmentSerialNumber),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentSerialNumber\", $$v)},expression:\"form.equipmentSerialNumber\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"新配件序列号\"}},[_c('el-input',{model:{value:(_vm.form.equipmentSerialNumberNew),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentSerialNumberNew\", $$v)},expression:\"form.equipmentSerialNumberNew\"}})],1)],1)],1)]):_vm._e(),_vm._v(\" \"),(\n        (_vm.active === 5 && _vm.form.replaceFlag == 1) ||\n          (_vm.active === 4 && _vm.form.replaceFlag == 2)\n      )?_c('div',{staticStyle:{\"width\":\"718px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        国家中心维护组确认故障恢复，运行正常。\\n      \")])]):_vm._e(),_vm._v(\" \"),(_vm.active === 6 && _vm.form.replaceFlag == 1)?_c('div',{staticStyle:{\"width\":\"718px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\"},[_vm._v(\"\\n        厂家工程师将故障配件（除核心设备及配件）返厂。核心设备及配件带回省移动公司接口人处，进入待处理库，等待接口人的统一处理\\n      \")])]):_vm._e(),_vm._v(\" \"),(\n        (_vm.active === 7 && _vm.form.replaceFlag == 1) ||\n          (_vm.active === 5 && _vm.form.replaceFlag == 2)\n      )?_c('div',{staticStyle:{\"width\":\"718px\",\"margin\":\"0 auto\",\"margin-top\":\"100px\"}},[_c('div',{staticClass:\"bt-title\",staticStyle:{\"text-align\":\"center\"}},[_vm._v(\"\\n        故障处理单\\n      \")]),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"margin-top\":\"20px\",\"justify-content\":\"center\"}},[_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"label-width\":\"180px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":\"故障设备编号：\"}},[_c('el-input',{attrs:{\"disabled\":\"\"},model:{value:(_vm.form.equipmentCode),callback:function ($$v) {_vm.$set(_vm.form, \"equipmentCode\", $$v)},expression:\"form.equipmentCode\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"故障原因：\"}},[_c('el-input',{model:{value:(_vm.form.reason),callback:function ($$v) {_vm.$set(_vm.form, \"reason\", $$v)},expression:\"form.reason\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"故障处理时间：\"}},[_c('el-input',{model:{value:(_vm.form.faultHandlingTime),callback:function ($$v) {_vm.$set(_vm.form, \"faultHandlingTime\", $$v)},expression:\"form.faultHandlingTime\"}})],1),_vm._v(\" \"),_c('el-form-item',{attrs:{\"label\":\"故障处理负责人：\"}},[_c('el-input',{model:{value:(_vm.form.assignee),callback:function ($$v) {_vm.$set(_vm.form, \"assignee\", $$v)},expression:\"form.assignee\"}})],1)],1)],1)]):_vm._e(),_vm._v(\" \"),_c('div',{staticStyle:{\"display\":\"flex\",\"width\":\"500px\",\"justify-content\":\"center\",\"margin\":\"0 auto\",\"margin-top\":\"60px\"}},[(_vm.active != 1)?_c('div',{staticClass:\"buttonw btnc\",on:{\"click\":_vm.syb}},[_vm._v(\"上一步\")]):_vm._e(),_vm._v(\" \"),(\n          !(\n            (_vm.active === 7 && _vm.form.replaceFlag == 1) ||\n            (_vm.active === 5 && _vm.form.replaceFlag == 2)\n          )\n        )?_c('div',{staticClass:\"buttonw btnc1\",on:{\"click\":_vm.xyb}},[_vm._v(\"\\n        下一步\\n      \")]):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"buttonw btnc2\",on:{\"click\":_vm.dcbutton}},[_vm._v(\"打印故障处理单\")])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"top-box\"},[_c('div',[_c('img',{attrs:{\"src\":require(\"./img/title.png\"),\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"top-title\"},[_vm._v(\"故障处理流程\")])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"bt-title\"},[_vm._v(\"\\n        携带配件到现场,现场处理故障。\"),_c('br'),_vm._v(\"判断是否需要更换新配件，如果不需要更换配件。\"),_c('br'),_vm._v(\"例如重启设备或者重新插拔线缆接头等。处理后，试运行。\\n      \")])}]\nvar esExports = { render: render, staticRenderFns: staticRenderFns }\nexport default esExports\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/vue-loader/lib/template-compiler?{\"id\":\"data-v-1f0a3926\",\"hasScoped\":true,\"transformToRequire\":{\"video\":[\"src\",\"poster\"],\"source\":\"src\",\"img\":\"src\",\"image\":\"xlink:href\"},\"buble\":{\"transforms\":{}}}!./node_modules/vue-loader/lib/selector.js?type=template&index=0!./src/renderer/view/ztqk/gzcllcbz.vue\n// module id = null\n// module chunks = ", "function injectStyle (ssrContext) {\n  require(\"!!../../../../node_modules/extract-text-webpack-plugin/dist/loader.js?{\\\"omit\\\":1,\\\"remove\\\":true,\\\"publicPath\\\":\\\"../\\\"}!vue-style-loader!css-loader?{\\\"sourceMap\\\":true}!../../../../node_modules/vue-loader/lib/style-compiler/index?{\\\"vue\\\":true,\\\"id\\\":\\\"data-v-1f0a3926\\\",\\\"scoped\\\":true,\\\"hasInlineConfig\\\":false}!../../../../node_modules/vue-loader/lib/selector?type=styles&index=0!./gzcllcbz.vue\")\n}\nvar normalizeComponent = require(\"!../../../../node_modules/vue-loader/lib/component-normalizer\")\n/* script */\nexport * from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./gzcllcbz.vue\"\nimport __vue_script__ from \"!!babel-loader!../../../../node_modules/vue-loader/lib/selector?type=script&index=0!./gzcllcbz.vue\"\n/* template */\nimport __vue_template__ from \"!!../../../../node_modules/vue-loader/lib/template-compiler/index?{\\\"id\\\":\\\"data-v-1f0a3926\\\",\\\"hasScoped\\\":true,\\\"transformToRequire\\\":{\\\"video\\\":[\\\"src\\\",\\\"poster\\\"],\\\"source\\\":\\\"src\\\",\\\"img\\\":\\\"src\\\",\\\"image\\\":\\\"xlink:href\\\"},\\\"buble\\\":{\\\"transforms\\\":{}}}!../../../../node_modules/vue-loader/lib/selector?type=template&index=0!./gzcllcbz.vue\"\n/* template functional */\nvar __vue_template_functional__ = false\n/* styles */\nvar __vue_styles__ = injectStyle\n/* scopeId */\nvar __vue_scopeId__ = \"data-v-1f0a3926\"\n/* moduleIdentifier (server only) */\nvar __vue_module_identifier__ = null\nvar Component = normalizeComponent(\n  __vue_script__,\n  __vue_template__,\n  __vue_template_functional__,\n  __vue_styles__,\n  __vue_scopeId__,\n  __vue_module_identifier__\n)\n\nexport default Component.exports\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/renderer/view/ztqk/gzcllcbz.vue\n// module id = null\n// module chunks = "], "sourceRoot": ""}