const FS = require('fs')

import Docxtemplater from 'docxtemplater'
import <PERSON>zzip from 'pizzip'

export const exportWord = (readFilePath, docParams) => {
  console.log('readFilePath', readFilePath)
  let content = FS.readFileSync(readFilePath)
  // console.log(content)
  let zip = new Pizzip(content)
  let doc
  try {
    doc = new Docxtemplater(zip)
  } catch(error) {
    console.log('docx error', error)
  }
  doc.setData(docParams)
  try {
    doc.render()
  } catch (error) {
    console.log('render error', error)
  }
  let buf = doc.getZip().generate({
    type: 'nodebuffer'
  })
  // console.log(buf)
  return buf
}

export const toSaveFile = (savePath, nodeBuffer) => {
  FS.writeFileSync(savePath, nodeBuffer)
}