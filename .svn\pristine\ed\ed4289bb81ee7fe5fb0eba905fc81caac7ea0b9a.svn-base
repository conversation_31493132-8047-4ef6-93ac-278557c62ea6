webpackJsonp([9],{"8mr5":function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=l("Xxa5"),i=l.n(a),n=l("exGp"),o=l.n(n),r=l("bOdI"),s=l.n(r),c=(l("l/JR"),l("XY/r")),m={name:"",components:{},props:{},data:function(){var e;return{page:1,pageSize:10,total:0,tableData:[{sbmc:"设备名称1",xh:"型号1",jgwz:"机柜位置1",xlh:"序列号1",gzqkms:"故障情况描述1",clff:"处理方法1",hfsj:"回复时间1"},{sbmc:"设备名称2",xh:"型号2",jgwz:"机柜位置2",xlh:"序列号2",gzqkms:"故障情况描述2",clff:"处理方法2",hfsj:"回复时间2"},{sbmc:"设备名称3",xh:"型号3",jgwz:"机柜位置3",xlh:"序列号3",gzqkms:"故障情况描述3",clff:"处理方法3",hfsj:"回复时间3"}],active:1,form:(e={jfmc:"",jfbh:"",jgmc:"",jgbh:"",sbmc:"",sbbh:"",temperature:"1",humidity:"1",cleanliness:"1",airConditioning:"1",powerEnvironmentSystem:"1",id:"",computerRoomId:"",computerRoomCode:"",computerRoomName:"",inspectionStartTime:"",inspectionEndTime:""},s()(e,"temperature","1"),s()(e,"humidity","1"),s()(e,"cleanliness","1"),s()(e,"airConditioning","1"),s()(e,"powerEnvironmentSystem","1"),e),sflist:[{label:"1",value:"正常"},{label:"2",value:"不正常"}],jglist:[],sblist:[],lxlist:[]}},computed:{abnormalData:function(){var e={form:{},jglist:[],sblist:[],lxlist:[]};return"2"===this.form.temperature&&(e.form.temperature="机房温度不正常"),"2"===this.form.humidity&&(e.form.humidity="机房湿度不正常"),"2"===this.form.cleanliness&&(e.form.cleanliness="机房清洁度不正常"),"2"===this.form.airConditioning&&(e.form.airConditioning="机房空调不正常"),"2"===this.form.powerEnvironmentSystem&&(e.form.powerEnvironmentSystem="动力环境系统不正常"),this.jglist.forEach(function(t){"2"!==t.temperature&&"2"!==t.humidity&&"2"!==t.cleanliness||e.jglist.push(t)}),this.sblist.forEach(function(t){"2"!==t.equipmentOnline&&"2"!==t.equipmentPower&&"2"!==t.equipmentNetwork&&"2"!==t.equipmentRunningSound&&"2"!==t.equipmentWarm&&"2"!==t.equipmentLoosen&&"2"!==t.equipmentDowntime&&"2"!==t.equipmentHardDisk&&"2"!==t.equipmentOhticalModule&&"2"!==t.equipmentPowerModule&&"2"!==t.equipmentFan&&"2"!==t.equipmentTemperature&&"2"!==t.equipmentPort&&"2"!==t.equipmentOpticalFiber&&"2"!==t.equipmentNetworkCable&&"2"!==t.equipmentLabel||e.sblist.push(t)}),this.lxlist.forEach(function(t){"2"!==t.cableDamaged&&"2"!==t.cableRibbon&&"2"!==t.cableJoint&&"2"!==t.cableLabel||e.lxlist.push(t)}),e}},watch:{},methods:{handleCurrentChange:function(e){this.page=e},handleSizeChange:function(e){this.pageSize=e},tableCellStyle:function(){return"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;"},tableHeaderCellStyle:function(){return"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;"},init:function(){var e=this;return o()(i.a.mark(function t(){var l;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(c.e)();case 2:1e4==(l=t.sent).code&&(l.data.temperature?(console.log(1111),e.form=l.data):(console.log(2222),e.form.temperature="1",e.form.humidity="1",e.form.cleanliness="1",e.form.airConditioning="1",e.form.powerEnvironmentSystem="1"));case 4:case"end":return t.stop()}},t,e)}))()},xyb:function(){var e=this;return o()(i.a.mark(function t(){var l;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(console.log(e.abnormalData),1!=e.active){t.next=7;break}return e.form.scanCode=e.$route.query.scanCode,t.next=5,Object(c.l)(e.form);case 5:1e4==(l=t.sent).code&&(e.form.id=l.data,e.queryCabinetByCondition(),e.queryEquipmentByCondition(),e.queryEquipmentByConditionlx());case 7:2==e.active&&(e.abnormalData.jglist.map(function(t){t.computerRoomInspectionId=e.form.id}),Object(c.k)({scanCode:e.$route.query.scanCode,inspectionCabinetList:e.abnormalData.jglist,computerRoomInspectionId:e.form.id}).then(function(e){})),3==e.active&&(e.abnormalData.sblist.map(function(t){t.computerRoomInspectionId=e.form.id}),Object(c.m)({scanCode:e.$route.query.scanCode,inspectionEquipmentList:e.abnormalData.sblist,computerRoomInspectionId:e.form.id}).then(function(e){})),4==e.active?(e.abnormalData.lxlist.map(function(t){t.computerRoomInspectionId=e.form.id}),Object(c.m)({scanCode:e.$route.query.scanCode,inspectionEquipmentList:e.abnormalData.lxlist,computerRoomInspectionId:e.form.id}).then(function(e){}),e.$router.push({path:"/xjlclb",query:{id:e.form.id,scanCode:e.$route.query.scanCode}})):e.active++;case 10:case"end":return t.stop()}},t,e)}))()},save:function(){var e=this;return o()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:Object(c.m)({inspectionEquipmentList:e.abnormalData.lxlist,computerRoomInspectionId:e.form.id,scanCode:e.$route.query.scanCode}).then(function(t){1e4==t.code&&e.$message({message:"提交成功",type:"success"})}),Object(c.c)({computerRoomInspectionId:e.form.id}).then(function(t){e.dom_download(t,"机房巡检信息.xls")});case 2:case"end":return t.stop()}},t,e)}))()},dom_download:function(e,t){var l=new Blob([e]),a=window.URL.createObjectURL(l),i=document.createElement("a");console.log("dom",i),i.style.display="none",i.href=a,i.setAttribute("download",t),document.body.appendChild(i),i.click()},queryCabinetByCondition:function(){var e=this;return o()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:Object(c.g)({cabinetCode:e.form.jgbh,cabinetName:e.form.jgmc,computerRoomInspectionId:e.form.id}).then(function(t){console.log(t),1e4==t.code&&(e.jglist=t.data)});case 1:case"end":return t.stop()}},t,e)}))()},queryEquipmentByCondition:function(){var e=this;return o()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:Object(c.h)({equipmentMainType:"1",cabinetCode:e.form.jgbh,cabinetName:e.form.jgmc,equipmentCode:e.form.sbbh,equipmentName:e.form.sbmc,computerRoomInspectionId:e.form.id}).then(function(t){1e4==t.code&&(e.sblist=t.data)});case 1:case"end":return t.stop()}},t,e)}))()},queryEquipmentByConditionlx:function(){var e=this;return o()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:Object(c.h)({equipmentMainType:"2",computerRoomInspectionId:e.form.id}).then(function(t){1e4==t.code&&(e.lxlist=t.data)});case 1:case"end":return t.stop()}},t,e)}))()},syb:function(){this.active--}},created:function(){},mounted:function(){this.init()},beforeCreate:function(){},beforeMount:function(){},beforeUpdate:function(){},updated:function(){},beforeDestroy:function(){},destroyed:function(){},activated:function(){}},u={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"box"},[e._m(0),e._v(" "),l("div",{staticStyle:{width:"70%",margin:"0 auto","margin-top":"50px"}},[l("el-steps",{attrs:{active:e.active,"finish-status":"success"}},[l("el-step",{attrs:{title:"1.机房环境"}}),e._v(" "),l("el-step",{attrs:{title:"2.机柜"}}),e._v(" "),l("el-step",{attrs:{title:"3.设备"}}),e._v(" "),l("el-step",{attrs:{title:"4.缆线"}})],1)],1),e._v(" "),1===e.active?l("div",{staticStyle:{width:"640px",margin:"0 auto","margin-top":"100px"}},[l("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[e._v("\n        1.机房环境\n      ")]),e._v(" "),l("div",{staticStyle:{display:"flex","justify-content":"center","margin-top":"20px"}},[l("el-form",{ref:"form",attrs:{model:e.form,"label-width":"260px","label-position":"left"}},[l("el-form-item",{staticStyle:{position:"relative"},attrs:{label:"机房温度是否正常"}},[l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),e._v(" "),l("div",{staticClass:"tszt"},[e._v("提示")])]),e._v(" "),l("div",{staticClass:"smzt"},[e._v("\n                  传感器自动获取\n                ")])]),e._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",top:"14px",left:"-90px"},attrs:{slot:"reference"},slot:"reference"})]),e._v(" "),l("el-radio-group",{model:{value:e.form.temperature,callback:function(t){e.$set(e.form,"temperature",t)},expression:"form.temperature"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{staticStyle:{position:"relative"},attrs:{label:"机房湿度是否正常"}},[l("el-popover",{attrs:{placement:"right",width:"200",trigger:"hover"}},[l("div",[l("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"relative",top:"2px"}}),e._v(" "),l("div",{staticClass:"tszt"},[e._v("提示")])]),e._v(" "),l("div",{staticClass:"smzt"},[e._v("\n                  传感器自动获取\n                ")])]),e._v(" "),l("i",{staticClass:"el-icon-info",staticStyle:{color:"#409eef",position:"absolute",top:"14px",left:"-90px"},attrs:{slot:"reference"},slot:"reference"})]),e._v(" "),l("el-radio-group",{model:{value:e.form.humidity,callback:function(t){e.$set(e.form,"humidity",t)},expression:"form.humidity"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"机房清洁度是否正常"}},[l("el-radio-group",{model:{value:e.form.cleanliness,callback:function(t){e.$set(e.form,"cleanliness",t)},expression:"form.cleanliness"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"机房空调是否正常"}},[l("el-radio-group",{model:{value:e.form.airConditioning,callback:function(t){e.$set(e.form,"airConditioning",t)},expression:"form.airConditioning"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"机房动力环境系统是否正常"}},[l("el-radio-group",{model:{value:e.form.powerEnvironmentSystem,callback:function(t){e.$set(e.form,"powerEnvironmentSystem",t)},expression:"form.powerEnvironmentSystem"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1)],1)]):e._e(),e._v(" "),2===e.active?l("div",{staticStyle:{width:"720px",margin:"0 auto","margin-top":"50px"}},[l("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[e._v("\n        2.机柜\n      ")]),e._v(" "),l("el-form",{ref:"form",staticClass:"inputcss",staticStyle:{"margin-top":"20px"},attrs:{model:e.form,"label-width":"100px","label-position":"left"}},[l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"机柜名称"}},[l("el-input",{model:{value:e.form.jgmc,callback:function(t){e.$set(e.form,"jgmc",t)},expression:"form.jgmc"}})],1),e._v(" "),l("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"机柜编号"}},[l("el-input",{model:{value:e.form.jgbh,callback:function(t){e.$set(e.form,"jgbh",t)},expression:"form.jgbh"}})],1),e._v(" "),l("div",{staticClass:"cxbtn",on:{click:e.queryCabinetByCondition}},[e._v("查询")])],1)]),e._v(" "),l("div",{staticStyle:{height:"270px","overflow-y":"auto"}},e._l(e.jglist,function(t){return l("div",{staticStyle:{display:"flex","align-items":"center","margin-top":"20px","border-bottom":"1px solid rgba(225,225,225,1)"}},[l("div",{staticStyle:{"font-family":"SourceHanSansSC-Bold","font-size":"20px",color:"#003396","font-weight":"700","margin-right":"42px",width:"120px"}},[e._v("\n            "+e._s(t.cabinetName)+"\n          ")]),e._v(" "),l("div",[l("el-form",{ref:"form",refInFor:!0,attrs:{model:e.form,"label-width":"220px","label-position":"left"}},[l("el-form-item",{attrs:{label:"机柜门是否常闭"}},[l("el-radio-group",{model:{value:t.temperature,callback:function(l){e.$set(t,"temperature",l)},expression:"item.temperature"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"机柜电力是否正常"}},[l("el-radio-group",{model:{value:t.humidity,callback:function(l){e.$set(t,"humidity",l)},expression:"item.humidity"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"机柜内是否有灰尘"}},[l("el-radio-group",{model:{value:t.cleanliness,callback:function(l){e.$set(t,"cleanliness",l)},expression:"item.cleanliness"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1)],1)])}),0)],1):e._e(),e._v(" "),3===e.active?l("div",{staticStyle:{width:"1600px",margin:"0 auto","margin-top":"50px"}},[l("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[e._v("\n        3.设备\n      ")]),e._v(" "),l("el-form",{ref:"form",staticClass:"inputcss",staticStyle:{"margin-top":"20px"},attrs:{model:e.form,"label-width":"100px","label-position":"left"}},[l("div",{staticStyle:{display:"flex"}},[l("el-form-item",{attrs:{label:"机柜名称"}},[l("el-input",{model:{value:e.form.jgmc,callback:function(t){e.$set(e.form,"jgmc",t)},expression:"form.jgmc"}})],1),e._v(" "),l("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"机柜编号"}},[l("el-input",{model:{value:e.form.jgbh,callback:function(t){e.$set(e.form,"jgbh",t)},expression:"form.jgbh"}})],1),e._v(" "),l("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"设备名称"}},[l("el-input",{model:{value:e.form.sbmc,callback:function(t){e.$set(e.form,"sbmc",t)},expression:"form.sbmc"}})],1),e._v(" "),l("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"设备编号"}},[l("el-input",{model:{value:e.form.sbbh,callback:function(t){e.$set(e.form,"sbbh",t)},expression:"form.sbbh"}})],1),e._v(" "),l("div",{staticClass:"cxbtn",on:{click:e.queryEquipmentByCondition}},[e._v("查询")])],1)]),e._v(" "),l("div",{staticStyle:{height:"386px","overflow-y":"auto"}},e._l(e.sblist,function(t){return l("div",{staticStyle:{display:"flex","align-items":"center","margin-top":"20px",border:"1px solid #c0c4cc","padding-left":"20px"}},[l("div",{staticStyle:{"font-family":"SourceHanSansSC-Bold","font-size":"20px",color:"#003396","font-weight":"700","margin-right":"42px",width:"180px","text-align":"center"}},[e._v("\n            "+e._s(t.equipmentName)+"\n          ")]),e._v(" "),l("div",{staticClass:"sb-box3"},[l("el-form",{ref:"form",refInFor:!0,attrs:{model:e.form,"label-width":"220px","label-position":"left"}},[l("div",{staticStyle:{display:"flex","justify-content":"space-between",width:"1330px"}},[l("el-form-item",{attrs:{label:"设备是否在线"}},[l("el-radio-group",{model:{value:t.equipmentOnline,callback:function(l){e.$set(t,"equipmentOnline",l)},expression:"item.equipmentOnline"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备电力是否正常"}},[l("el-radio-group",{model:{value:t.equipmentPower,callback:function(l){e.$set(t,"equipmentPower",l)},expression:"item.equipmentPower"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备运行声音是否正常"}},[l("el-radio-group",{model:{value:t.equipmentRunningSound,callback:function(l){e.$set(t,"equipmentRunningSound",l)},expression:"item.equipmentRunningSound"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1),e._v(" "),l("div",{staticStyle:{display:"flex","justify-content":"space-between",width:"1330px"}},[l("el-form-item",{attrs:{label:"设备是否有告警"}},[l("el-radio-group",{model:{value:t.equipmentWarm,callback:function(l){e.$set(t,"equipmentWarm",l)},expression:"item.equipmentWarm"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备是否有松动"}},[l("el-radio-group",{model:{value:t.equipmentLoosen,callback:function(l){e.$set(t,"equipmentLoosen",l)},expression:"item.equipmentLoosen"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备是否有宕机"}},[l("el-radio-group",{model:{value:t.equipmentDowntime,callback:function(l){e.$set(t,"equipmentDowntime",l)},expression:"item.equipmentDowntime"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1),e._v(" "),l("div",{staticStyle:{display:"flex","justify-content":"space-between",width:"1330px"}},[l("el-form-item",{attrs:{label:"服务器硬盘是否正常"}},[l("el-radio-group",{model:{value:t.equipmentHardDisk,callback:function(l){e.$set(t,"equipmentHardDisk",l)},expression:"item.equipmentHardDisk"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备光模块是否齐全"}},[l("el-radio-group",{model:{value:t.equipmentOhticalModule,callback:function(l){e.$set(t,"equipmentOhticalModule",l)},expression:"item.equipmentOhticalModule"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备电源模块是否正常"}},[l("el-radio-group",{model:{value:t.equipmentPowerModule,callback:function(l){e.$set(t,"equipmentPowerModule",l)},expression:"item.equipmentPowerModule"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1),e._v(" "),l("div",{staticStyle:{display:"flex",width:"1330px","justify-content":"space-between"}},[l("el-form-item",{attrs:{label:"设备风扇是否正常"}},[l("el-radio-group",{model:{value:t.equipmentFan,callback:function(l){e.$set(t,"equipmentFan",l)},expression:"item.equipmentFan"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备温度是否正常"}},[l("el-radio-group",{model:{value:t.equipmentTemperature,callback:function(l){e.$set(t,"equipmentTemperature",l)},expression:"item.equipmentTemperature"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"设备端口是否正常"}},[l("el-radio-group",{model:{value:t.equipmentPort,callback:function(l){e.$set(t,"equipmentPort",l)},expression:"item.equipmentPort"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1),e._v(" "),l("div",{staticStyle:{display:"flex",width:"1330px","justify-content":"space-between"}},[l("el-form-item",{attrs:{label:"设备光纤是否正常"}},[l("el-radio-group",{model:{value:t.equipmentOpticalFiber,callback:function(l){e.$set(t,"equipmentOpticalFiber",l)},expression:"item.equipmentOpticalFiber"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{staticStyle:{"margin-left":"17px"},attrs:{label:"设备网线是否正常"}},[l("el-radio-group",{model:{value:t.equipmentNetworkCable,callback:function(l){e.$set(t,"equipmentNetworkCable",l)},expression:"item.equipmentNetworkCable"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{staticStyle:{"margin-left":"17px"},attrs:{label:"设备标签是否正常"}},[l("el-radio-group",{model:{value:t.equipmentLabel,callback:function(l){e.$set(t,"equipmentLabel",l)},expression:"item.equipmentLabel"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1)])],1)])}),0)],1):e._e(),e._v(" "),4===e.active?l("div",{staticStyle:{width:"720px",margin:"0 auto","margin-top":"50px"}},[l("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[e._v("\n        4.缆线\n      ")]),e._v(" "),l("div",{staticStyle:{height:"410px","overflow-y":"auto"}},e._l(e.lxlist,function(t){return l("div",{staticStyle:{display:"flex","align-items":"center","margin-top":"20px","border-bottom":"1px solid rgba(225,225,225,1)"}},[l("div",{staticStyle:{"font-family":"SourceHanSansSC-Bold","font-size":"20px",color:"#003396","font-weight":"700","margin-right":"42px"}},[e._v("\n            "+e._s(t.equipmentName)+"\n          ")]),e._v(" "),l("div",[l("el-form",{ref:"form",refInFor:!0,attrs:{model:e.form,"label-width":"220px","label-position":"left"}},[l("el-form-item",{attrs:{label:"线缆有无破损断裂"}},[l("el-radio-group",{model:{value:t.cableDamaged,callback:function(l){e.$set(t,"cableDamaged",l)},expression:"item.cableDamaged"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"线缆扎带是否正常"}},[l("el-radio-group",{model:{value:t.cableRibbon,callback:function(l){e.$set(t,"cableRibbon",l)},expression:"item.cableRibbon"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"线缆接头是否有松动"}},[l("el-radio-group",{model:{value:t.cableJoint,callback:function(l){e.$set(t,"cableJoint",l)},expression:"item.cableJoint"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"线缆标签是否正常"}},[l("el-radio-group",{model:{value:t.cableLabel,callback:function(l){e.$set(t,"cableLabel",l)},expression:"item.cableLabel"}},e._l(e.sflist,function(t){return l("el-radio",{key:t.label,attrs:{label:t.label}},[e._v(e._s(t.value))])}),1)],1)],1)],1)])}),0)]):e._e(),e._v(" "),5===e.active?l("div",{staticStyle:{width:"1120px",margin:"0 auto","margin-top":"50px"}},[l("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[e._v("\n        巡检异常情况明细表\n      ")]),e._v(" "),e._m(1),e._v(" "),l("div",{staticStyle:{width:"100%","margin-top":"20px"}},[l("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"header-cell-style":e.tableHeaderCellStyle,"cell-style":e.tableCellStyle,"max-height":"800px"}},[l("el-table-column",{attrs:{prop:"sbmc",label:"设备名称",align:"center"}}),e._v(" "),l("el-table-column",{attrs:{prop:"xh",label:"型号",align:"center"}}),e._v(" "),l("el-table-column",{attrs:{prop:"jgwz",label:"机柜位置",align:"center"}}),e._v(" "),l("el-table-column",{attrs:{prop:"xlh",label:"序列号",align:"center"}}),e._v(" "),l("el-table-column",{attrs:{prop:"gzqkms",label:"故障情况描述",align:"center"}}),e._v(" "),l("el-table-column",{attrs:{prop:"clff",label:"处理方法",align:"center"}}),e._v(" "),l("el-table-column",{attrs:{prop:"hfsj",label:"回复时间",align:"center"}})],1),e._v(" "),l("div",{staticStyle:{"margin-top":"15px"}},[l("el-pagination",{attrs:{background:"","pager-count":5,"current-pageNo":e.page,"pageNo-sizes":[5,10,20,30],"pageNo-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"current-change":e.handleCurrentChange,"size-change":e.handleSizeChange}})],1)],1)]):e._e(),e._v(" "),l("div",{staticStyle:{display:"flex",width:"500px","justify-content":"center",margin:"0 auto","margin-top":"60px"}},[1!=e.active&&5!=e.active?l("div",{staticClass:"buttonw btnc",on:{click:e.syb}},[e._v("\n        上一步\n      ")]):e._e(),e._v(" "),4!=e.active||e.abnormalData.sblist.length?l("div",{staticClass:"buttonw btnc1",on:{click:e.xyb}},[e._v("\n        下一步\n      ")]):e._e(),e._v(" "),4!=e.active||e.abnormalData.sblist.length?e._e():l("div",{staticClass:"buttonw btnc1",on:{click:e.save}},[e._v("\n        提交并导出\n      ")])])])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"top-box"},[t("div",[t("img",{attrs:{src:l("hsSF"),alt:""}})]),this._v(" "),t("div",{staticClass:"top-title"},[this._v("巡检流程")])])},function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-top":"30px"}},[l("div",{staticClass:"fbtbox"},[l("div",{staticClass:"labels"},[e._v("巡检机房名称")]),e._v(" "),l("div",{staticClass:"values"},[e._v("NPJ122000111000000")])]),e._v(" "),l("div",{staticClass:"fbtbox"},[l("div",{staticClass:"labels"},[e._v("巡检时间")]),e._v(" "),l("div",{staticClass:"values"},[e._v("2024-10-12 11：00：00")])]),e._v(" "),l("div",{staticClass:"fbtbox"},[l("div",{staticClass:"labels"},[e._v("巡检人员")]),e._v(" "),l("div",{staticClass:"values"},[e._v("张三")])])])}]};var p=l("VU/8")(m,u,!1,function(e){l("ekiw")},"data-v-127d3c42",null);t.default=p.exports},ekiw:function(e,t){}});
//# sourceMappingURL=9.f145ec3b2d3c47d84f67.js.map