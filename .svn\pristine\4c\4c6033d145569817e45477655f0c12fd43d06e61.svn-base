import {createAPI, createFileAPI,createDown,createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//添加-载体复制
export const submitZtfz = data => createAPI(BASE_URL+"/ztgl-fz/submitZtfz", 'post',data)// 涉密等级
//载体复制分页查询
export const selectZtfzPage = data => createAPI(BASE_URL+"/ztgl-fz/selectZtfzPage", 'get',data)// 涉密等级
//根据记录id修改载体复制信息
export const updateZtfz = data => createAPI(BASE_URL+"/ztgl-fz/updateZtfz", 'post',data)// 涉密等级
//根据jlid查单个载体复制
export const getZtfzInfo = data => createAPI(BASE_URL+"/ztgl-fz/getZtfzInfo", 'get',data)// 涉密等级
//slid获取jlid
export const getJlid = data => createAPI(BASE_URL+"/ztgl-fz/getJlid", 'get',data)// 涉密等级
//添加-载体复制登记
export const submitZtfzdj = data => createAPI(BASE_URL+"/ztgl-fzdj/submitZtfzdj", 'post',data)// 涉密等级
//载体复制登记查询带分页
export const selectZtfzdjPage = data => createAPI(BASE_URL+"/ztgl-fzdj/selectZtfzdjPage", 'get',data)// 涉密等级
//删除载体复制记录
export const removeZtfz = data => createAPI(BASE_URL+"/ztgl-fz/removeZtfz", 'post',data)// 涉密等级

