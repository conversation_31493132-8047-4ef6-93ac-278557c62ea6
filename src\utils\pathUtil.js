// import {
//   path,
//   FS,
//   OSEnum,
//   environmentEnum,
//   defaultFjccjl,
//   defaultBackccjl,
//   defaultLogccjl,
//   defaultDatabaseZczpSavePath,
//   defaultDatabaseSystemSavePath,
//   defaultDatabaseDataMigrationSavePath
// } from '../constant/constant'

var path = require('path')
var FS = require('fs')
var OSEnum = ['Win32', 'linux or mac']
var environmentEnum = ['development', 'production']
var defaultFjccjl = [
  process.env.USERPROFILE + '\\secret keeper\\data\\fj\\',
  process.env.USERPROFILE + '/Secret keeper/data/fj/',
]
var defaultBackccjl = [
  process.env.USERPROFILE + '\\secret keeper\\data\\back\\',
  process.env.USERPROFILE + '/Secret keeper/data/back/',
]
var defaultLogccjl = [
  process.env.USERPROFILE + '\\secret keeper\\data\\logs\\',
  process.env.USERPROFILE + '/Secret keeper/data/logs/',
]
var defaultDatabaseZczpSavePath = [
  'C:\\hsoft\\data\\database\\zczp.json',
  '/home/<USER>/data/database/zczp.json',
]
var defaultDatabaseSystemSavePath = [
  'C:\\hsoft\\data\\database\\system.json',
  '/home/<USER>/data/database/system.json',
]
var defaultDatabaseDataMigrationSavePath = [
  'C:\\hsoft\\data\\database\\dataMigration.json',
  '/home/<USER>/data/database/dataMigration.json',
]

// const constant = require('../constant/constant')

/**
 * 工具类
 */
import { getUuid } from '../utils/getUuid'
// 异常处理器
import { errorProcessor } from './errorProcessor'

/**
 * 系统参数
 */
// import {
//   // 通过路径标识获取文件路径设置
//   selectWjxgListByCsbs,
//   // 插入文件路径设置表
//   insertWjxgList,
//   // 修改文件路径设置表
//   updateWjxgList,
// } from '../db/zczpSystem/zczpSysyemDb'

export function getOS() {
  console.log('OSEnum', OSEnum)
  if (navigator.platform === OSEnum[0]) {
    return OSEnum[0]
  }
  return OSEnum[1]
}

export function formatToPath(toPathParam) {
  if (getOS() === OSEnum[0]) {
    console.log(OSEnum[0])
    return toPathParam.slice(0, toPathParam.lastIndexOf('\\') + 1)
  }
  console.log(OSEnum[1])
  return toPathParam.slice(0, toPathParam.lastIndexOf('/') + 1)
}

export function getEnvironment() {
  if (process.env.NODE_ENV === environmentEnum[0]) {
    console.log(environmentEnum[0])
    return environmentEnum[0]
  }
  console.log(environmentEnum[1])
  return environmentEnum[1]
}

export function getDocMbPath() {
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../../static/scbg1.docx')
  } else {
    if (getOS() === OSEnum[0]) {
      return path.resolve(__dirname, '../../../static/scbg1.docx')
    } else {
      return path.resolve(__dirname, '../../static/scbg1.docx')
    }
  }
}
// 检查报告
export function getDocZczpMbPath() {
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../static/zczpbg.docx')
  } else {
    if (getOS() === OSEnum[0]) {
      return path.resolve(__dirname, './static/zczpbg.docx')
    } else {
      return path.resolve(__dirname, './static/zczpbg.docx')
    }
  }
}
// 定密事项综合统计表模板
export function getDocDmsxzhtjbMbPath() {
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../static/MB-dmsxzhtjb.xlsx')
  } else {
    if (getOS() === OSEnum[0]) {
      return path.resolve(__dirname, './static/MB-dmsxzhtjb.xlsx')
    } else {
      return path.resolve(__dirname, './static/MB-dmsxzhtjb.xlsx')
    }
  }
}
//现场考试
export function getDocXcPath() {
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../../static/scks.docx')
  } else {
    if (getOS() === OSEnum[0]) {
      return path.resolve(__dirname, '../../../static/scks.docx')
    } else {
      return path.resolve(__dirname, '../../static/scks.docx')
    }
  }
}

// 数据迁移配置文件路径(开发环境生成的配置文件路径)
export function getDataMigrationConfigPathDev() {
  // let dataMigrationPath = path.resolve(
  //   __dirname,
  //   '../../dataMigration/dataMigration.config.json'
  // )
  // console.log('dataMigrationPath', dataMigrationPath)
  // // 返回路径
  // return dataMigrationPath
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../static/dataMigration.config.json')
  } else {
    if (getOS() === OSEnum[0]) {
      return path.resolve(__dirname, './static/dataMigration.config.json')
    } else {
      return path.resolve(__dirname, './static/dataMigration.config.json')
    }
  }
}

// 报告管理生成路径
export function getDeportConfigPathDev(mark) {
  // 路径分隔符
  let separator
  if (getOS() == OSEnum[0]) {
    separator = '\\'
  } else {
    separator = '/'
  }
  // 绝对路径前缀
  let pathPrefix
  if (getEnvironment() === environmentEnum[0]) {
    pathPrefix = path.resolve(__dirname, '../../static/secretFile')
  } else {
    if (getOS() === OSEnum[0]) {
      pathPrefix = path.resolve(__dirname, './static/secretFile')
    } else {
      pathPrefix = path.resolve(__dirname, './static/secretFile')
    }
  }
  if(mark == 'all') {
    return pathPrefix
  }
  return pathPrefix + separator + mark
}
// 报告管理pdf明文文件
export function getPdfNormal(mark) {
  // 路径分隔符
  let separator
  if (getOS() == OSEnum[0]) {
    separator = '\\'
  } else {
    separator = '/'
  }
  // 绝对路径前缀
  let pathPrefix
  if (getEnvironment() === environmentEnum[0]) {
    pathPrefix = path.resolve(__dirname, '../../static/pdfFiles')
  } else {
    if (getOS() === OSEnum[0]) {
      pathPrefix = path.resolve(__dirname, './static/pdfFiles')
    } else {
      pathPrefix = path.resolve(__dirname, './static/pdfFiles')
    }
  }
  if(mark == 'all') {
    return pathPrefix
  }
  return pathPrefix + separator + mark
}

// 数据迁移配置文件路径
export function getDataMigrationConfigPath() {
  let dataMigrationPath
  if (getEnvironment() === environmentEnum[0]) {
    dataMigrationPath = path.resolve(
      __dirname,
      '../../dataMigration/dataMigration.config.json'
    )
  } else {
    if (getOS() === OSEnum[0]) {
      dataMigrationPath = path.resolve(
        __dirname,
        '../../../../dataMigration.config.json'
      )
    } else {
      dataMigrationPath = path.resolve(
        __dirname,
        '../../../../dataMigration.config.json'
      )
    }
  }
  console.log('dataMigrationPath', dataMigrationPath)
  if (!FS.existsSync(dataMigrationPath)) {
    // 创建目录
    createDirectory(dataMigrationPath)
    // 创建文件
    FS.writeFileSync(dataMigrationPath, JSON.stringify({}), {
      encoding: 'utf8',
    })
  }
  // 返回路径
  return dataMigrationPath
}

// 数据库文件路径
export function getZczpPath() {
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../zczp.json')
  } else {
    // if (getOS() === OSEnum[0]) {
    //   return path.resolve(__dirname, '../../../../zczp.json')
    // } else {
    //   return path.resolve(__dirname, '../../../../zczp.json')
    // }
    return 'C:\\hsoft\\data\\database\\zczp.json'
  }
}

// 获取数据库存储路径(zczp.json)
export function getDatabaseZczpSavePath() {
  let os = getOS()
  let resPath
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../zczp.json')
  }
  if (os === OSEnum[0]) {
    resPath = defaultDatabaseZczpSavePath[0]
  } else {
    resPath = defaultDatabaseZczpSavePath[1]
  }
  // 判断是否存在非法字符
  if (os === OSEnum[0]) {
    if (resPath.indexOf('/') != -1) {
      console.log('[windows][zczp]数据库路径配置中存在非法字符/，重置为默认')
      resPath = defaultDatabaseZczpSavePath[0]
    }
  } else {
    if (resPath.indexOf('\\') != -1) {
      console.log('[linux][zczp]数据库路径配置中存在非法字符\\，重置为默认')
      resPath = defaultDatabaseZczpSavePath[1]
    }
  }
  // 走一遍目录创建(保证本地会存在这个目录)
  createDirectory(resPath)
  // // 加上尾/或\\，业务层不用再考虑拼接什么分隔符了
  // if (os === OSEnum[0]) {
  //   resPath += '\\'
  // } else {
  //   resPath += '/'
  // }
  // resPath += 'zczp.json'
  // console.log(resPath)
  // 返回结果
  return resPath
}

// 获取数据库存储路径(system.json)
export function getDatabaseSystemFileSavePath() {
  let os = getOS()
  let resPath
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../system.json')
  }
  if (os === OSEnum[0]) {
    resPath = defaultDatabaseSystemSavePath[0]
  } else {
    resPath = defaultDatabaseSystemSavePath[1]
  }
  // 判断是否存在非法字符
  if (os === OSEnum[0]) {
    if (resPath.indexOf('/') != -1) {
      console.log('[windows][system]数据库路径配置中存在非法字符/，重置为默认')
      resPath = defaultDatabaseSystemSavePath[0]
    }
  } else {
    if (resPath.indexOf('\\') != -1) {
      console.log('[linux][system]数据库路径配置中存在非法字符\\，重置为默认')
      resPath = defaultDatabaseSystemSavePath[1]
    }
  }
  // 走一遍目录创建(保证本地会存在这个目录)
  createDirectory(resPath)
  // // 加上尾/或\\，业务层不用再考虑拼接什么分隔符了
  // if (os === OSEnum[0]) {
  //   resPath += '\\'
  // } else {
  //   resPath += '/'
  // }
  // resPath += 'zczp.json'
  // console.log(resPath)
  // 返回结果
  return resPath
}

// 获取数据库存储路径(dataMigration.json)
export function getDatabaseDataMigrationSavePath() {
  let os = getOS()
  let resPath
  if (getEnvironment() === environmentEnum[0]) {
    return path.resolve(__dirname, '../../dataMigration.json')
  }
  if (os === OSEnum[0]) {
    resPath = defaultDatabaseDataMigrationSavePath[0]
  } else {
    resPath = defaultDatabaseDataMigrationSavePath[1]
  }
  // 判断是否存在非法字符
  if (os === OSEnum[0]) {
    if (resPath.indexOf('/') != -1) {
      console.log(
        '[windows][dataMigration]数据库路径配置中存在非法字符/，重置为默认'
      )
      resPath = defaultDatabaseDataMigrationSavePath[0]
    }
  } else {
    if (resPath.indexOf('\\') != -1) {
      console.log(
        '[linux][dataMigration]数据库路径配置中存在非法字符\\，重置为默认'
      )
      resPath = defaultDatabaseDataMigrationSavePath[1]
    }
  }
  // 走一遍目录创建(保证本地会存在这个目录)
  createDirectory(resPath)
  // // 加上尾/或\\，业务层不用再考虑拼接什么分隔符了
  // if (os === OSEnum[0]) {
  //   resPath += '\\'
  // } else {
  //   resPath += '/'
  // }
  // resPath += 'zczp.json'
  // console.log(resPath)
  // 返回结果
  return resPath
}

// 获取附件存储路径
export function getFileSavePath() {
  let os = getOS()
  // 通过路径标识(csbs)获取附件存储路径
  let params = {
    csbs: 'fjcclj',
  }
  console.log('process.env.USERPROFILE', process.env.USERPROFILE)
  let fjccljObj = selectWjxgListByCsbs(params)
  // 校验系统设置中是否配置了附件存储路径
  if (!fjccljObj) {
    console.log('路径标识[fjcclj]未定义，系统将自动定义该路径未默认路径')
    fjccljObj = {
      csbs: 'fjcclj',
      cssm: '附件存储路径',
      fzh: '1',
      filesettingid: getUuid(),
      gxsj: new Date().getTime(),
    }
    if (os === OSEnum[0]) {
      fjccljObj.csz = defaultFjccjl[0]
    } else {
      fjccljObj.csz = defaultFjccjl[1]
    }
    // 插入数据库
    insertWjxgList(fjccljObj)
  }
  // 判断数据库中配置的存储路径是否是当前系统的合法路径
  let csz = fjccljObj.csz
  if (!csz) {
    // 更新为默认路径
    if (os === OSEnum[0]) {
      fjccljObj.csz = defaultFjccjl[0]
    } else {
      fjccljObj.csz = defaultFjccjl[1]
    }
    // 更新数据库
    updateWjxgList(fjccljObj)
  }
  // 判断是否存在非法字符
  csz = fjccljObj.csz
  if (os === OSEnum[0]) {
    if (csz.indexOf('/') != -1) {
      console.log('[windows]附件配置中存在非法字符/，重置为默认')
      fjccljObj.csz = defaultFjccjl[0]
      updateWjxgList(fjccljObj)
    }
  } else {
    if (csz.indexOf('\\') != -1) {
      console.log('[linux]附件配置中存在非法字符\\，重置为默认')
      fjccljObj.csz = defaultFjccjl[1]
      updateWjxgList(fjccljObj)
    }
  }
  // 走一遍目录创建(保证本地会存在这个目录)
  csz = fjccljObj.csz
  createDirectory(fjccljObj.csz)
  // 加上尾/或\\，业务层不用再考虑拼接什么分隔符了
  if (os === OSEnum[0]) {
    csz += '\\'
  } else {
    csz += '/'
  }
  // 返回结果
  return csz
  //
  // if (getOS() === OSEnum[0]) {
  //   if (getEnvironment() === environmentEnum[0]) {
  //     return path.resolve('H:\\xcsc\\fj')
  //   } else {
  //     return path.resolve(__dirname, '../../../fj')
  //   }
  // } else {
  //   return path.resolve('/var/local/xcsc/fj')
  // }
}

// 获取备份文件存储路径
export function getBackFileSavePath() {
  let os = getOS()
  // 通过路径标识(csbs)获取附件存储路径
  let params = {
    csbs: 'back',
  }
  console.log('process.env.USERPROFILE', process.env.USERPROFILE)
  let ccljObj = selectWjxgListByCsbs(params)
  // 校验系统设置中是否配置了附件存储路径
  if (!ccljObj) {
    console.log('路径标识[back]未定义，系统将自动定义该路径未默认路径')
    ccljObj = {
      csbs: 'back',
      cssm: '备份文件存储路径',
      fzh: '1',
      filesettingid: getUuid(),
      gxsj: new Date().getTime(),
    }
    if (os === OSEnum[0]) {
      ccljObj.csz = defaultBackccjl[0]
    } else {
      ccljObj.csz = defaultBackccjl[1]
    }
    // 插入数据库
    insertWjxgList(ccljObj)
  }
  // 判断数据库中配置的存储路径是否是当前系统的合法路径
  let csz = ccljObj.csz
  if (!csz) {
    // 更新为默认路径
    if (os === OSEnum[0]) {
      ccljObj.csz = defaultBackccjl[0]
    } else {
      ccljObj.csz = defaultBackccjl[1]
    }
    // 更新数据库
    updateWjxgList(ccljObj)
  }
  // 判断是否存在非法字符
  csz = ccljObj.csz
  if (os === OSEnum[0]) {
    if (csz.indexOf('/') != -1) {
      console.log('[windows]附件配置中存在非法字符/，重置为默认')
      ccljObj.csz = defaultBackccjl[0]
      updateWjxgList(ccljObj)
    }
  } else {
    if (csz.indexOf('\\') != -1) {
      console.log('[linux]附件配置中存在非法字符\\，重置为默认')
      ccljObj.csz = defaultBackccjl[1]
      updateWjxgList(ccljObj)
    }
  }
  // 走一遍目录创建(保证本地会存在这个目录)
  csz = ccljObj.csz
  createDirectory(ccljObj.csz)
  // 加上尾/或\\，业务层不用再考虑拼接什么分隔符了
  if (os === OSEnum[0]) {
    csz += '\\'
  } else {
    csz += '/'
  }
  // 返回结果
  return csz
}

// 返回给的文件路径的文件名
export function getFileNameByDirectory(path) {
  let indexChar = '/'
  if (getOS() === OSEnum[0]) {
    path = path.replace(/\//g, '\\')
    indexChar = '\\'
  } else {
    path = path.replace(/\\/g, '/')
    indexChar = '/'
  }
  console.log(path, indexChar)
  return path.substring(path.lastIndexOf(indexChar) + 1)
}

// 返回符合当前系统的路径
export function getDirectory(path) {
  let indexChar = '/'
  if (getOS() === OSEnum[0]) {
    path = path.replace(/\//g, '\\')
    indexChar = '\\'
  } else {
    path = path.replace(/\\/g, '/')
    indexChar = '/'
  }
  console.log(path, indexChar)
  return path.substring(0, path.lastIndexOf(indexChar))
}

// 返回符合当前系统的文件路径
export function getFileDirectory(path) {
  let indexChar = '/'
  if (getOS() === OSEnum[0]) {
    path = path.replace(/\//g, '\\')
    indexChar = '\\'
  } else {
    path = path.replace(/\\/g, '/')
    indexChar = '/'
  }
  console.log(path, indexChar)
  return path
}

// 返回log日志文件存储路径
export function getLogFileSavePath() {
  let os = getOS()
  // 通过路径标识(csbs)获取日志文件存储路径
  let params = {
    csbs: 'rzwjccjl',
  }
  console.log('process.env.USERPROFILE', process.env.USERPROFILE)
  let ccljObj = selectWjxgListByCsbs(params)
  // 校验系统设置中是否配置了附件存储路径
  if (!ccljObj) {
    console.log('路径标识[back]未定义，系统将自动定义该路径未默认路径')
    ccljObj = {
      csbs: 'rzwjccjl',
      cssm: '日志文件存储路径',
      fzh: '1',
      filesettingid: getUuid(),
      gxsj: new Date().getTime(),
    }
    if (os === OSEnum[0]) {
      ccljObj.csz = defaultLogccjl[0]
    } else {
      ccljObj.csz = defaultLogccjl[1]
    }
    // 插入数据库
    insertWjxgList(ccljObj)
  }
  // 判断数据库中配置的存储路径是否是当前系统的合法路径
  let csz = ccljObj.csz
  if (!csz) {
    // 更新为默认路径
    if (os === OSEnum[0]) {
      ccljObj.csz = defaultLogccjl[0]
    } else {
      ccljObj.csz = defaultLogccjl[1]
    }
    // 更新数据库
    updateWjxgList(ccljObj)
  }
  // 判断是否存在非法字符
  csz = ccljObj.csz
  if (os === OSEnum[0]) {
    if (csz.indexOf('/') != -1) {
      console.log('[windows]日志文件路径配置中存在非法字符/，重置为默认')
      ccljObj.csz = defaultLogccjl[0]
      updateWjxgList(ccljObj)
    }
  } else {
    if (csz.indexOf('\\') != -1) {
      console.log('[linux]日志文件路径配置中存在非法字符\\，重置为默认')
      ccljObj.csz = defaultLogccjl[1]
      updateWjxgList(ccljObj)
    }
  }
  // 走一遍目录创建(保证本地会存在这个目录)
  csz = ccljObj.csz
  createDirectory(ccljObj.csz)
  // 加上尾/或\\，业务层不用再考虑拼接什么分隔符了
  if (os === OSEnum[0]) {
    csz += '\\'
  } else {
    csz += '/'
  }
  // 返回结果
  return csz
}

// 创建目录（如果给的文件路径，则自动忽略文件，只创建路径）
export function createDirectory(path) {
  if (!path) {
    console.log('path异常', path)
    return
  }
  let pathArr
  let separator
  if (getOS() == OSEnum[0]) {
    separator = '\\'
  } else {
    separator = '/'
  }
  pathArr = path.split(separator)
  // console.log('pathArr', pathArr)
  // 获取最后一个拆分的字符串，判断是否应该丢弃
  let last = pathArr[pathArr.length - 1]
  if (last == '' || last.indexOf('.') != -1) {
    pathArr.length -= 1
  }
  // console.log('pathArr', pathArr)
  let cumulativePath = ''
  pathArr.some((item) => {
    cumulativePath += item + separator
    console.log(cumulativePath, FS.existsSync(cumulativePath))
    if (FS.existsSync(cumulativePath)) {
      console.log(cumulativePath, '存在')
    } else {
      console.log(cumulativePath, '不存在')
      try {
        FS.mkdirSync(cumulativePath)
      } catch (error) {
        // console.log(error)
        // 发生异常，结束循环
        throw errorProcessor(
          error,
          '请使用更高权限运行程序或手动创建目录：\n' + path
        )
      }
    }
  })
}

// 移动旧目录下的文件到新目录
export const moveFilesByDirectory = (oldPath, newPath) => {
  // 判断新目录是否为空目录，空目录才进行移动，否则抛出异常
  let files = FS.readdirSync(newPath)
  console.log('files', files)
  if (files && files.length > 0) {
    throw new Error('请选择空目录进行修改')
  }
  // 判断系统，获取文件系统分隔符
  let separator
  if (getOS() == OSEnum[0]) {
    separator = '\\'
  } else {
    separator = '/'
  }
  try {
    recursiveCopyFiles(oldPath, newPath, separator)
  } catch (error) {
    throw errorProcessor(error)
  }
}

// 递归复制文件
export const recursiveCopyFiles = (oldPath, newPath, separator) => {
  // 新旧目录是否存在，存在不处理，不存在则新建
  createDirectory(oldPath)
  //
  createDirectory(newPath)
  let files = FS.readdirSync(oldPath)
  let lstat
  let oldPathFull
  let newPathFull
  files.forEach((fileName) => {
    console.log(fileName)
    oldPathFull = oldPath + separator + fileName
    newPathFull = newPath + separator + fileName
    //
    lstat = FS.lstatSync(oldPathFull)
    if (lstat.isFile()) {
      // 复制文件
      FS.copyFileSync(oldPathFull, newPathFull)
    }
    if (lstat.isDirectory()) {
      // 递归
      recursiveCopyFiles(oldPathFull, newPathFull, separator)
    }
  })
}

// 删除目录下文件
export const deleteFilesByDirectory = (path) => {
  // 判断系统，获取文件系统分隔符
  let separator
  if (getOS() == OSEnum[0]) {
    separator = '\\'
  } else {
    separator = '/'
  }
  try {
    recursiveDeleteFiles(path, separator)
  } catch (error) {
    console.error(error)
    throw errorProcessor(error, '自动文件清理失败，请手动清理')
  }
}

// 递归删除目录下文件
export const recursiveDeleteFiles = (path, separator) => {
  if (FS.existsSync(path)) {
    let files = FS.readdirSync(path)
    if (files && files.length <= 0) {
      // 空目录，删除
      FS.rmdirSync(path)
    } else {
      let pathFull
      let lstat
      files.forEach((fileName) => {
        pathFull = path + separator + fileName
        lstat = FS.lstatSync(pathFull)
        if (lstat.isFile()) {
          FS.unlinkSync(pathFull)
        }
        if (lstat.isDirectory()) {
          files = FS.readdirSync(pathFull)
          console.log(pathFull, files)
          if (files && files.length <= 0) {
            // 空目录，删除
            FS.rmdirSync(pathFull)
          } else {
            recursiveDeleteFiles(pathFull, separator)
          }
        }
      })
      FS.rmdirSync(path)
    }
  }
}
