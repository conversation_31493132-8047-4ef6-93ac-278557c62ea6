import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//服务类型查询
export const getFwlxList = data => createAPI(BASE_URL+"/api/select/getFwlxList", 'get',data)
//新增流程
export const gzlJbxxAdd = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlJbxx/add", 'post',data)
//修改流程
export const gzlJbxxUpdate = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlJbxx/update", 'post',data)
//删除流程
export const gzlJbxxDelete = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlJbxx/delete", 'post',data)
//流程查询
export const gzlJbxxFindPageList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlJbxx/findPageList", 'get',data)

