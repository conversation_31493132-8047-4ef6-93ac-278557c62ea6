import FS from 'fs'

// import { Loading } from 'element-ui'

import {
  getBackFileSavePath,
  getFileDirectory,
  createDirectory,
  getFileNameByDirectory,
  // 安装目录配置文件位置
  getDataMigrationConfigPath,
  // 开发阶段生成的配置文件位置
  getDataMigrationConfigPathDev,
  getZczpPath,
} from '../utils/pathUtil'

import { errorProcessor } from './errorProcessor'

/**
 * 数据迁移工具类
 * 抛出捕获的任何异常
 * 返回处理结果对象，对象属性说明：
 * code：100-失败 200-成功 300-当前迁移配置文件已迁移完成
 * message: 失败或成功的消息
 * 迁移成功将清空迁移配置文件（安装目录下的dataMigration.config.json）(放入一对花括号{})
 * 内容是一对花括号则不会进行迁移
 *
 * 启动时校验开发环境生成的配置文件是否已执行过数据迁移
 * 已执行：不进行后续逻辑
 * 未执行：将开发环境的迁移文件内容移到安装环境根目录下的配置文件中然后执行
 *
 * 期望流程是安装目录下的配置文件更改后依然能够执行迁移，故优先判断安装目录下的迁移文件是否为{}，
 * 然后再判断开发环境生成的配置文件是否已被执行
 *
 * 迁移成功在开发环境生成的配置文件中打上已执行的标识
 */
export const dataMigration = (_this) => {
  //
  let resObj = {}
  try {
    let configPath = getDataMigrationConfigPath()
    console.log('安装目录下的配置文件位置', configPath)
    let configStr = FS.readFileSync(configPath, { encoding: 'utf8' })
    // console.log('安装目录下的配置文件读取结果', configStr)
    //
    let configJsonObj = JSON.parse(configStr)
    // 生成配置文件目录
    let configPathDev
    //
    // if (configStr == '{}' || configJsonObj.isExec) {
    if (configJsonObj.isExec) {
      console.log('当前迁移配置文件已迁移完成')
      resObj.code = 300
      resObj.message = '当前迁移配置文件已迁移完成'
      return resObj
    } else {
      configPathDev = getDataMigrationConfigPathDev()
      console.log('配置文件位置（生成）', configPath)
      let configStrDev = FS.readFileSync(configPathDev, { encoding: 'utf8' })
      console.log('配置文件读取结果（生成）', configStrDev)
      let configJsonDev = JSON.parse(configStrDev)
      console.log(
        '配置文件读取结果（生成）',
        configJsonDev,
        configJsonDev.isExec
      )
      // if (configJsonDev.isExec) {
      //   console.log('当前迁移配置文件(开发生成)已迁移完成')
      //   resObj.code = 300
      //   resObj.message = '当前迁移配置文件已迁移完成'
      //   return resObj
      // } else {
      //   console.log('当前迁移配置文件(开发生成)未迁移', configStrDev)
      //   // 将开发环境下生成的配置文件内容复制到安装环境配置文件中
      //   FS.writeFileSync(configPath, configStrDev)
      //   configStr = configStrDev
      // }
      FS.writeFileSync(configPath, configStrDev)
      configStr = configStrDev
      configJsonObj = JSON.parse(configStr)
    }

    // 获取源文件即目标文件
    let zczpJsonPath = getZczpPath()
    let zczpStr = FS.readFileSync(zczpJsonPath, { encoding: 'utf8' })
    let zczpJsonObj = JSON.parse(zczpStr)
    /**
     * 备份源文件
     * */
    let backPath = getBackFileSavePath()
    // 加上一层json的文件夹
    backPath +=
      'json\\' +
      '备份数据库文件-' +
      new Date().getTime() +
      '-' +
      getFileNameByDirectory(zczpJsonPath)
    console.log('backPath', backPath)
    // 路径系统适配
    backPath = getFileDirectory(backPath)
    // 创建路径（因为多加了一个json的文件夹）
    createDirectory(backPath)
    console.log('backPath', backPath, zczpJsonObj)
    FS.writeFileSync(backPath, JSON.stringify(zczpJsonObj), {
      encoding: 'utf8',
    })
    /**
     * 数据处理
     */
    configJsonObj.options.forEach((item) => {
      if (item.type == 1) {
        // 新增表
        zczpJsonObj = addTable(zczpJsonObj, item.option)
        console.log('新增表完成', zczpJsonObj)
      }
      if (item.type == 2) {
        // 删除表
        zczpJsonObj = deleteTable(zczpJsonObj, item.option)
        console.log('删除表完成', zczpJsonObj)
      }
      if (item.type == 3) {
        // 更新表
        zczpJsonObj = updateTable(zczpJsonObj, item.option)
        console.log('修改表完成', zczpJsonObj)
      }
      if (item.type == 4) {
        // 新增表字段
        zczpJsonObj = addTableField(zczpJsonObj, item.option)
        console.log('新增表字段完成', zczpJsonObj)
      }
      if (item.type == 5) {
        // 删除表字段
        zczpJsonObj = deleteTableField(zczpJsonObj, item.option)
        console.log('删除表字段完成', zczpJsonObj)
      }
      if (item.type == 6) {
        // 修改表字段
        zczpJsonObj = updateTableField(zczpJsonObj, item.option)
        console.log('修改表字段完成', zczpJsonObj)
      }
      if (item.type == 7) {
        // 新增表数据
        zczpJsonObj = addTableRecord(zczpJsonObj, item.option)
        console.log('新增表数据完成', zczpJsonObj)
      }
      if (item.type == 8) {
        // 删除表数据
        zczpJsonObj = deleteTableRecord(zczpJsonObj, item.option)
        console.log('删除表数据完成', zczpJsonObj)
      }
    })
    // 写回zczp.json文件
    // console.log('zczpJsonObj', zczpJsonObj)
    FS.writeFileSync(zczpJsonPath, JSON.stringify(zczpJsonObj), {
      encoding: 'utf8',
    })
    // 安装目录下的迁移文件打上已迁移标识
    console.log('安装目录下的迁移文件打上已迁移标识', configPath)
    configJsonObj.isExec = true
    FS.writeFileSync(configPath, JSON.stringify(configJsonObj), {
      encoding: 'utf8',
    })
    // // 开发目录下的迁移文件内容打上已迁移标识(标识无法打上，使用安装目录下的配置文件打标识)
    // console.log('开发目录下的迁移文件内容打上已迁移标识', configPathDev, configJsonObj)
    // configJsonObj.isExec = true
    // FS.writeFileSync(configPathDev, JSON.stringify(configJsonObj), {
    //   encoding: 'utf8',
    // })
  } catch (error) {
    error = errorProcessor(error)
    resObj.code = 100
    resObj.message = error.message
    return resObj
  }
  resObj.code = 200
  resObj.message = '数据迁移成功，重启后生效'
  return resObj
}

// 新增表
export const addTable = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableAddObj = optionJsonObj
  let tableName
  let fieldList
  if (tableAddObj) {
    tableName = tableAddObj.tableName
    fieldList = tableAddObj.fieldList
  }
  /**数据校验**/
  // 表名校验
  if (tableName === undefined || tableName == '') {
    throw new Error('[新增]表表名为空')
    return
  }
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[新增]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  // 判断目标文件是否存在该表
  if (toJsonObj[tableName]) {
    // 提示已有表
    // throw new Error('[新增]目标文件中已存在[' + tableName + ']表，请确认迁移配置文件')
    return toJsonObj
  }
  // 插入表及字段
  let params = {}
  fieldList.forEach((item) => {
    params[item.fieldName] = item.fieldDefaultValue
  })
  let tableItemList = []
  if(Object.keys(params).length > 0) {
    tableItemList.push(params)
  }
  toJsonObj[tableName] = tableItemList
  return toJsonObj
}

// 删除表
export const deleteTable = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableDeleteList = optionJsonObj
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[删除]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  tableDeleteList.forEach((item) => {
    if (toJsonObj[item]) {
      Reflect.deleteProperty(toJsonObj, item)
    }
  })
  //
  return toJsonObj
}

// 修改表
export const updateTable = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let toJsonTables = optionJsonObj
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[修改]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  toJsonTables.forEach((item) => {
    if (
      toJsonObj[item.tableName] &&
      item.tableNameNew &&
      item.tableNameNew != ''
    ) {
      toJsonObj[item.tableNameNew] = toJsonObj[item.tableName]
      Reflect.deleteProperty(toJsonObj, item.tableName)
    }
  })
  return toJsonObj
}

// 新增表字段
export const addTableField = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableAddFieldObj = optionJsonObj
  let tableName
  let fieldList
  if (tableAddFieldObj) {
    tableName = tableAddFieldObj.tableName
    fieldList = tableAddFieldObj.fieldList
  }
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[新增][字段]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  // 表名校验
  if (!tableName || tableName == '') {
    throw new Error('[新增][字段]未检测到需要操作的表')
  }
  //
  toJsonObj[tableName].forEach((tableItem, tableIndex) => {
    fieldList.forEach((item) => {
      if (tableItem[item.fieldName]) {
        // 什么也不用做
      } else {
        if (!item.fieldDefaultValue) {
          tableItem[item.fieldName] = ''
        } else {
          tableItem[item.fieldName] = item.fieldDefaultValue
        }
      }
    })
  })
  return toJsonObj
}

// 删除表字段
export const deleteTableField = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableDeleteFieldObj = optionJsonObj
  let deleteFieldList
  let tableName
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[删除][字段]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  // 删除数据校验
  if (!tableDeleteFieldObj) {
    throw new Error('[删除][字段]配置数据异常')
  }
  deleteFieldList = tableDeleteFieldObj.deleteFieldList
  tableName = tableDeleteFieldObj.tableName
  // 准备删除字段
  if (toJsonObj[tableName]) {
    toJsonObj[tableName].forEach((item, index) => {
      Object.keys(item).forEach((key) => {
        if (deleteFieldList.indexOf(key) != -1) {
          Reflect.deleteProperty(item, key)
        }
      })
    })
  }
  return toJsonObj
}

// 修改表字段
export const updateTableField = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableUpdateFieldObj = optionJsonObj
  let fieldList
  let tableName
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[修改][字段]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  // 修改数据校验
  if (!tableUpdateFieldObj) {
    throw new Error('[修改][字段]配置数据异常')
  }
  fieldList = tableUpdateFieldObj.fieldList
  tableName = tableUpdateFieldObj.tableName
  //
  toJsonObj[tableName].forEach((item, index) => {
    fieldList.forEach((field) => {
      if (
        item[field.fieldName] &&
        field.fieldNameNew &&
        field.fieldNameNew != ''
      ) {
        item[field.fieldNameNew] = item[field.fieldName]
        Reflect.deleteProperty(item, field.fieldName)
      }
    })
  })
  return toJsonObj
}

// 新增表数据
export const addTableRecord = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableAddFieldObj = optionJsonObj
  let tableName
  let fieldList
  if (tableAddFieldObj) {
    tableName = tableAddFieldObj.tableName
    fieldList = tableAddFieldObj.fieldList
  }
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[新增][数据]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  // 表名校验
  if (!tableName || tableName == '') {
    throw new Error('[新增][数据]未检测到需要操作的表')
  }
  // 加工表格配置数据为对象数据，同时筛选出以id结尾的数据（主键防重复）
  let record = {}
  let reg = /id$/
  let idFieldName
  let idFieldValue
  fieldList.forEach((item) => {
    switch (item.fieldName) {
      case 'yhlx':
        record[item.fieldName] = parseInt(item.fieldDefaultValue)
        break
      case 'cjsj':
        record[item.fieldName] = parseInt(item.fieldDefaultValue)
        break
      case 'gxsj':
        record[item.fieldName] = parseInt(item.fieldDefaultValue)
        break
      default:
        record[item.fieldName] = item.fieldDefaultValue
        break
    }
    if (reg.test(item.fieldName)) {
      idFieldName = item.fieldName
      idFieldValue = item.fieldDefaultValue
    }
  })
  //
  let checkIdExistList = toJsonObj[tableName].filter((item) => {
    if (item[idFieldName] == idFieldValue) {
      return item
    }
  })
  if (checkIdExistList && checkIdExistList.length > 0) {
    console.log(
      '[新增][数据]id为[' +
        idFieldName +
        ']，值为[' +
        idFieldValue +
        ']的数据已存在，已跳过本次新增数据'
    )
  } else {
    if(Object.keys(record).length > 0) {
      toJsonObj[tableName].push(record)
    }
  }
  return toJsonObj
}

// 删除表数据
export const deleteTableRecord = (jsonObj, optionJsonObj) => {
  let toJsonObj = jsonObj
  let tableDeleteRecordObj = optionJsonObj
  let tableName
  let fieldName
  let fieldDefaultValue
  if (tableDeleteRecordObj) {
    tableName = tableDeleteRecordObj.tableName
    fieldName = tableDeleteRecordObj.fieldName
    fieldDefaultValue = tableDeleteRecordObj.fieldDefaultValue
  }
  // 目标文件校验
  if (!toJsonObj) {
    throw new Error(
      '[删除][数据]目标文件数据异常，可能原因：不是一个json对象文件，请保证来源文件中至少有一对英文花括号{}'
    )
  }
  // 表名校验
  if (!tableName || tableName == '') {
    throw new Error('[删除][数据]未检测到需要操作的表')
  }
  if (!fieldName || fieldName == '') {
    throw new Error('[删除][数据]未检测到需要操作的表字段名称')
  }
  if (!fieldDefaultValue || fieldDefaultValue == '') {
    throw new Error('[删除][数据]未检测到需要操作的表字段值')
  }
  let newTableList = toJsonObj[tableName].filter((item) => {
    if (item[fieldName] != fieldDefaultValue) {
      return item
    }
  })
  // 覆盖原数据
  toJsonObj[tableName] = newTableList
  //
  return toJsonObj
}
