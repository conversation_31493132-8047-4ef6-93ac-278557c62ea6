import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、添加涉密设备-责任人变更
export const addSbglZrrbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglZrrbg/addSbglZrrbg", 'post',data)
//2、修改涉密设备-责任人变更
export const updateSbglZrrbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglZrrbg/updateSbglZrrbg", 'post',data)
//3、删除涉密设备-责任人变更
export const deleteSbglZrrbg = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglZrrbg/deleteSbglZrrbg", 'get',data)
//4、分页查询涉密设备-责任人变更
export const selectSbglZrrbgPage = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglZrrbg/selectSbglZrrbgPage", 'get',data)
//5、通过jlid查询涉密设备-责任人变更
export const selectSbglZrrbgByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglZrrbg/selectSbglZrrbgByJlid", 'get',data)
//5、通过jlid查询涉密设备-责任人变更
export const selectJlidBySlidZrr = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglZrrbg/selectJlidBySlid", 'get',data)
//2、添加涉密设备责任人变更审批登记表
export const addSbglZrrbgd = data => createAPI(BASE_URL+"/SbglZrrbgdj/addSbglZrrbgdj", 'post',data)
//4、分页查询涉密设备责任人变更审批登记表
export const selectSbglZrrbgdjPage = data => createAPI(BASE_URL+"/SbglZrrbgdj/selectSbglZrrbgdjPage", 'get',data)
