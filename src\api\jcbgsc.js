import {createAPI, createFileAPI,createDown, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//添加文件
export const saveWd = data => createAPI(BASE_URL+"/jcbg-wd/saveWd", 'post',data)
//获取文件列表
export const getAllWd = data => createAPI(BASE_URL+"/jcbg-wd/getAllWd", 'get',data)
//批量删除文件
export const removeBatch = data => createAPI(BASE_URL+"/jcbg-wd/removeBatch", 'post',data)
//HTML
export const getHtml = data => createAPI(BASE_URL+"/jcbg-wd/getHtml", 'get',data)
//XML
export const getXml = data => createAPI(BASE_URL+"/jcbg-wd/getXml", 'get',data)