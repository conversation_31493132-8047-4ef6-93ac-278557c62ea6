const state = {
  main: 0,
  // // el-aside 是否已显示
  // elAsideIsShow: false,
  // el-aside里的菜单内容
  elAsideMenuList: [],
  // el-main 区域顶部 tag 标签集合
  tagList: [],
  /**
   * 需关闭的tag标签路由
   * 当为 allTagWithOutFirst 时关闭除
  */
  closeTag: '',
  // 需要显示的顶部菜单集合
  showHeaderMenuList: []
}

const mutations = {
  DECREMENT_MAIN_COUNTER (state) {
    state.main--
  },
  INCREMENT_MAIN_COUNTER (state) {
    state.main++
  },
  // // 更新 el-aside 的显示状态
  // changeElAsideShow(state, bool) {
  //   state.elAsideIsShow = bool
  // },
  // 更新 el-aside 的菜单内容
  changeElAsideMenuList(state, menuList) {
    state.elAsideMenuList = menuList
  },
  // 更新 需要显示的顶部菜单集合
  changeElHeaderMenuList(state, menuList) {
    state.showHeaderMenuList = menuList
  },
  // 获取 el-aside 的菜单内容
  getElAsideMenuList(state) {
    return state.elAsideMenuList
  },
  // 更新 closeTag 的需关闭tag值
  changeCloseTag(state, closeTagPath) {
    state.closeTag = closeTagPath
  }
  // 将元素 tagList 内容
}

const actions = {
  someAsyncTask ({ commit }) {
    // do something async
    commit('INCREMENT_MAIN_COUNTER')
  }
}

export default {
  state,
  mutations,
  actions
}
