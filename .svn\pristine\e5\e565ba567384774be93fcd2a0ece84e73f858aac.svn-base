<template>
  <div class="base-container">
    <!-- 查询条件以及操作按钮start -->
    <div class="mhcx">
      <el-form :inline="true" :model="params" size="medium" class="fl">
        <template v-for="(item, index) in columns">
          <!-- 普通输入框start -->
          <template v-if="item.type == 'searchInput'">
            <el-form-item :label="item.name" class="elFormLabel">
              <el-input class="widthw" v-model="params[item.value]" clearable :placeholder="item.placeholder"></el-input>
            </el-form-item>
          </template>
          <!-- 普通输入框end -->
          <!-- 时间范围选择器start -->
          <template v-else-if="item.type == 'dataRange'">
            <el-form-item :label="item.name" class="elFormLabel">
              <el-date-picker v-model="params[item.value]" type="daterange" :range-separator="item.rangeSeparator"
                style="width:294px;" :start-placeholder="item.startPlaceholder" :end-placeholder="item.endPlaceholder"
                :format="item.format" :value-format="item.format">
              </el-date-picker>
            </el-form-item>
          </template>
          <!-- 时间范围选择器end -->
          <!-- 部门选择器cascader----start -->
          <!-- <template v-else-if="item.type == 'cascader'">
            <el-form-item :label="item.name" class="elFormLabel">
              <el-cascader v-model="params[item.value]" :options="selectOptions" :props="regionParams" filterable clearable
            ref="cascaderArr" @change="bmSelectChange"></el-cascader>
            </el-form-item>
          </template> -->
          <!-- 部门选择器cascader----end -->
          <!-- 操作按钮start -->
          <template v-else-if="item.type == 'button'">
            <el-form-item>
              <el-button @click="handleEvent(item)" :type="item.mold" :icon="item.icon" :disabled="item.disabled">{{
                item.name }}</el-button>
            </el-form-item>
          </template>
          <!-- 操作按钮end -->
        </template>
      </el-form>
    </div>
    <!-- 查询条件以及操作按钮end -->
  </div>
</template>
<script>

export default {
  components: {},
  props: {
    params: {
      type: Object,
      require: false,
      default: {}
    },
    columns: {
      type: Array,
      require: false,
      default: []
    },
    // selectOptions: {
    //   type: Array,
    //   require: false,
    //   default: []
    // },
    // regionParams: {
    //   type: Object,
    //   require: false,
    //   default: {}
    // }

    
    
  },
  data() {
    return {

    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    handleEvent(item) {
      let params = JSON.parse(JSON.stringify(this.params))
      this.$emit('handleBtn', params,item)
    }

  },
  watch: {

  }
}

</script>
  
<style scoped>
/deep/.mhcx .el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}
.widthw {
  width: 6vw;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.elFormLabel {
  font-weight: 700;
}
</style>
  