import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//添加设备报废登记记录
export const saveSbglSbbfdj = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_Sbbfdj/saveSbglSbbfdj", 'post',data)
//通过jlid查询设备报废登记记录
export const selectByIdSbglSbbfdj = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_Sbbfdj/selectByIdSbglSbbfdj", 'get',data)
//分页条件查询设备报废登记记录
export const selectPageSbglSbbfdj = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_Sbbfdj/selectPageSbglSbbfdj", 'get',data)

// 添加设备报废信息
export const saveSbglSbbf = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/saveSbglSbbf", 'post',data)
// 2、通过slid删除设备报废记录
export const deleteSbglSbbf = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/deleteSbglSbbf", 'post',data)
// 根据jlid修改设备报废记录信息
export const updateSbglSbbf = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/updateSbglSbbf", 'post',data)
// 通过jlid查询设备报废记录
export const selectByIdSbglSbbf = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/selectByIdSbglSbbf", 'get',data)
// 分页条件查询设备报废记录
export const selectPageSbglSbbf = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/selectPageSbglSbbf", 'get',data)

// 通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
// 根据原jlid查询设备清单
export const getSbqdListByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/getSbqdListByYjlid", 'get',data)
// 设备清单批量添加
export const savaSbqdBatch = data => createAPI(BASE_URL+"/sbgl/sbqd/savaSbqdBatch", 'post',data)
// 根据原jlid删除原jlid下的设备清单
export const deleteSbqdByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/deleteSbqdByYjlid", 'post',data)

// 通过slid查询设备报废记录
export const selectBySlidSbglSbbf = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/selectBySlidSbglSbbf", 'get',data)
// 通过slid查询jlid
export const selectSlidByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbbf/selectSlidByJlid", 'get',data)