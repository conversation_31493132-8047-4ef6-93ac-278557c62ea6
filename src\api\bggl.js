import {createAPI, createFileAPI, createUploadAPI,createDownloadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//获取uuid
export const getUuid = data => createAPI(BASE_URL+"/bggl/wd/getUUID", 'get',data)
//添加文档信息
export const getSaveFile = data => createAPI(BASE_URL+"/bggl/wd/saveWd", 'post',data)
//获取文档信息
export const getAllWdList = data => createAPI(BASE_URL+"/bggl/wd/getAllWd", 'get',data)
//删除文档信息
export const getDeleteFile = data => createAPI(BASE_URL+"/bggl/wd/removeBatch", 'post',data)
//获取会员口令
export const getHyklDatas = data => createAPI(BASE_URL+"/bggl/hykl/getkl", 'get',data)
//修改会员口令
export const reviseHyKl = data => createAPI(BASE_URL+"/bggl/hykl/updateHykl", 'post',data)
//获取pdf
export const getPdf = data => createAPI(BASE_URL+"/jcbg-wd/getPdf", 'get',data)
//获取pdf
export const getHtmlFile = data => createDownloadAPI(BASE_URL+"/jcbg-wd/getHtmlFile", 'get',data)
// //查询人员信息
// export const getAllYhxx = data => createAPI(BASE_URL+"/rygl/yhxx/getAllYhxx", 'get',data)
// //修改国家秘密事项
// export const updateGjmmsx = data => createAPI(BASE_URL+"/dmgl/gjmmsx/updateGjmmsx", 'post',data)
// //删除国家秘密事项
// export const removeGjmmsx = data => createAPI(BASE_URL+"/dmgl/gjmmsx/removeGjmmsx", 'post',data)


