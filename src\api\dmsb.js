import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、添加涉密设备-设备定密
export const addSbglDm = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/addSbglDm", 'post',data)
//4、分页查询涉密设备-设备定密
export const selectSbglDmPage = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/selectSbglDmPage", 'get',data)
//3、删除涉密设备-设备定密
export const deleteSbglDm = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/deleteSbglDm", 'get',data)
//5、通过jlid查询涉密设备-设备定密  
export const selectSbglDmByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/selectSbglDmByJlid", 'get',data)
//2、修改涉密设备-设备定密
export const updateSbglDm = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/updateSbglDm", 'post',data)
//通过jlid查询涉密设备-设备定密的slid
export const selectSbglDmBySlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/selectSbglDmBySlid", 'get',data)
//通过slid查询涉密设备-设备定密的jlid
export const selectJlidBySliddmsb = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglDm/selectJlidBySlid", 'get',data)
//1、添加涉密设备定密审批登记表
export const addSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/addSbglDmdj", 'post',data)
//4、分页查询涉密设备定密审批登记表
export const selectSbglDmdjPage = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdjPage", 'get',data)
//6.通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
//6.通过类型和责任部门获取设备定密审批登记表
export const getDxsbPage = data => createAPI(BASE_URL+"/sbgl/smjsj/getDxsbPage", 'get',data)
export const getZgBfDxsbPage = data => createAPI(BASE_URL+"/sbgl/smjsj/getZgBfDxsbPage", 'get',data)
