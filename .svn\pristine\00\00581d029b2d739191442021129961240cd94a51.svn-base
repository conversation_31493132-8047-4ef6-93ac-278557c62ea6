webpackJsonp([5],{"C4/f":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Xxa5"),o=a.n(n),i=a("exGp"),l=a.n(i),s=a("XY/r"),r={name:"",components:{},props:{},data:function(){return{page:1,pageSize:10,total:0,tableData:[]}},computed:{},watch:{},methods:{listInspectionIssueDetails:function(){var e=this;return l()(o.a.mark(function t(){return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:Object(s.o)({pageNum:e.page,pageSize:e.pageSize,computerRoomInspectionId:e.$route.query.id}).then(function(t){console.log(t),1e4==t.code&&(e.tableData=t.data.records,e.total=t.data.total)});case 1:case"end":return t.stop()}},t,e)}))()},dcbtn:function(){var e=this;return l()(o.a.mark(function t(){return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:Object(s.m)({scanCode:e.$route.query.scanCode,inspectionEquipmentList:e.tableData,computerRoomInspectionId:e.$route.query.id}).then(function(t){1e4==t.code&&Object(s.c)({computerRoomInspectionId:e.$route.query.id}).then(function(t){e.dom_download(t,"机房巡检信息.xls"),e.$message({message:"提交成功",type:"success"})})});case 1:case"end":return t.stop()}},t,e)}))()},dom_download:function(e,t){var a=new Blob([e]),n=window.URL.createObjectURL(a),o=document.createElement("a");console.log("dom",o),o.style.display="none",o.href=n,o.setAttribute("download",t),document.body.appendChild(o),o.click()},handleCurrentChange:function(e){this.page=e},handleSizeChange:function(e){this.pageSize=e},tableCellStyle:function(){return"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;"},tableHeaderCellStyle:function(){return"font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;"}},created:function(){},mounted:function(){console.log(this.$route.query.id),this.listInspectionIssueDetails()},beforeCreate:function(){},beforeMount:function(){},beforeUpdate:function(){},updated:function(){},beforeDestroy:function(){},destroyed:function(){},activated:function(){}},c={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"box"},[e._m(0),e._v(" "),a("div",{staticStyle:{width:"1500px",margin:"0 auto","margin-top":"50px"}},[a("div",{staticClass:"bt-title",staticStyle:{"text-align":"center"}},[e._v("\n        巡检异常情况明细表\n      ")]),e._v(" "),a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-top":"30px"}},[a("div",{staticClass:"fbtbox"},[a("div",{staticClass:"labels"},[e._v("巡检机房名称")]),e._v(" "),a("div",{staticClass:"values"},[e._v(e._s(e.tableData[0].computerRoomName))])]),e._v(" "),a("div",{staticClass:"fbtbox"},[a("div",{staticClass:"labels"},[e._v("巡检时间")]),e._v(" "),a("div",{staticClass:"values"},[e._v(e._s(e.tableData[0].inspectionTime))])]),e._v(" "),a("div",{staticClass:"fbtbox"},[a("div",{staticClass:"labels"},[e._v("巡检人员")]),e._v(" "),a("div",{staticClass:"values"},[e._v(e._s(e.tableData[0].createByName))])])]),e._v(" "),a("div",{staticStyle:{width:"100%","margin-top":"20px"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"header-cell-style":e.tableHeaderCellStyle,"cell-style":e.tableCellStyle,"max-height":"800px"}},[a("el-table-column",{attrs:{prop:"equipmentName",label:"设备名称",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"equipmentModel",label:"型号",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"equipmentTypeName",label:"设备类型",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"equipmentSerialNumber",label:"序列号",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"description",label:"故障情况描述",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.description,callback:function(a){e.$set(t.row,"description",a)},expression:"scope.row.description"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"treatment",label:"处理方法",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.treatment,callback:function(a){e.$set(t.row,"treatment",a)},expression:"scope.row.treatment"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"recoveryTime",label:"恢复时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期",format:"yyyy-MM-dd日","value-format":"yyyy-MM-dd"},model:{value:t.row.recoveryTime,callback:function(a){e.$set(t.row,"recoveryTime",a)},expression:"scope.row.recoveryTime"}})]}}])})],1),e._v(" "),a("div",{staticStyle:{"margin-top":"15px"}},[a("el-pagination",{attrs:{background:"","pager-count":5,"current-pageNo":e.page,"pageNo-sizes":[5,10,20,30],"pageNo-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"current-change":e.handleCurrentChange,"size-change":e.handleSizeChange}})],1)],1)]),e._v(" "),a("div",{staticStyle:{display:"flex",width:"500px","justify-content":"center",margin:"0 auto","margin-top":"60px"}},[a("div",{staticClass:"buttonw btnc1",on:{click:e.dcbtn}},[e._v("提交并导出")])])])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"top-box"},[t("div",[t("img",{attrs:{src:a("hsSF"),alt:""}})]),this._v(" "),t("div",{staticClass:"top-title"},[this._v("巡检流程")])])}]};var u=a("VU/8")(r,c,!1,function(e){a("LDPY")},"data-v-7acb935d",null);t.default=u.exports},LDPY:function(e,t){}});
//# sourceMappingURL=5.b30667cbdab2e6d929eb.js.map