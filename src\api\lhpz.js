import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//查询流程配置列表
export const gzlLchjFindList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/findList", 'post',data)
//新增环节
export const gzlLchjAdd = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/add", 'post',data)
//新增目标环节配置
export const gzlLchjAddMbhjxx = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/addMbhjxx", 'post',data)
//查询可选择目标环节列表
export const gzlLchjFindMbhjList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/findMbhjList", 'post',data)
//编辑环节
export const gzlLchjUpdate = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/update", 'post',data)
//按钮配置查询列表
export const gzlLchjFindHjanList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/findHjanList", 'post',data)
//编辑按钮
export const gzlLchjAddHjan = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/addHjan", 'post',data)
//删除环节
export const gzlLchjDelete = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/delete", 'post',data)
//添加处理目标
export const gzlLchjAddClmbxx = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/addClmbxx", 'post',data)
//查询目标环节列表
export const gzlLchjFindMbhjxxList = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/findMbhjxxList", 'post',data)
//查询处理目标
export const gzlLchjFindGwlb = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/findGwlb", 'post',data)
//删除目标环节列表
export const gzlLchjDeleteMbhj = data => createAPI(BASE_URL+"/api/gzl_01_01/gzlLchj/deleteMbhj", 'post',data)

