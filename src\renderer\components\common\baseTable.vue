<template>
  <div class="table_content">
    <el-table class="tb-container table" :height="tableHeight" :data="tableData" border
      :header-cell-style="headerCellStyle" stripe @select="selectList" @select-all="selectList">
      <!--选择多选-->
      <el-table-column v-if="showSelection" type="selection" :width="selectionWidth" align="center"></el-table-column>
      <!--选择单选-->
      <el-table-column v-if="showSingleSelection" align="center" :width="selectionWidth">
        <template slot-scope="scope">
          <el-radio v-model="radioIdSelect" :label="scope.row" :key="valueKey"
            @change.native="handleSelectionChange(scope.$index, scope.row)">{{ "" }}</el-radio>
        </template>
      </el-table-column>
      <!--序号-->
      <el-table-column v-if="showIndex" type="index" label="序号" width="60" align="center"></el-table-column>
      <template v-for="(column, index) in columns">
        <!-- 文本 -->
        <el-table-column :key="index" :label="column.name" v-if="column.scopeType === 'text'" :prop="column.prop"
          :show-overflow-tooltip="column.showOverflowTooltip">
          <template slot-scope="scope">
            {{
      column.formatter ? column.formatter(scope.row[column.prop], column, scope.row[column.prop]) : scope.row[column.prop]
    }}
          </template>
        </el-table-column>
      </template>
      <!-- 操作列 -->
      <el-table-column align="left" v-bind="handleColumnProp" :label="handleColumnProp.label"
        v-if="handleColumn.length">
        <template slot-scope="scope">
          <div class="handle-column-wrap">
            <template v-for="(item, index) in handleColumn">
              <el-button v-if="item.show" size="medium" type="text"
                @click="operate(scope.row, item.formatter ? item.formatter(scope.row, item) : item.name)">
                {{ item.formatter ? item.formatter(scope.row, item) : item.name }}
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination class="paginationClass" v-if="showPagination" background @current-change="handleCurrentChange"
      @size-change="handleSizeChange" :pager-count="5" :current-page="currentPage" :page-sizes="[5, 10, 20, 30]"
      :page-size="pageSize" layout="total, prev, pager, sizes,next, jumper" :total="totalCount">
    </el-pagination>
  </div>
</template>
<script>

export default {
  components: {},
  props: {
    tableHeight: {
      type: String,
      default: 'calc(100% - 125px)'
    },
    // table数据列表
    tableData: {
      type: Array,
      require: true,
      default: []
    },
    // 是否展示序号
    showIndex: {
      type: Boolean,
      default: true
    },
    // 是否可以复选
    showSelection: {
      type: Boolean,
      default: false
    },
    // 是否可以单选
    showSingleSelection: {
      type: Boolean,
      default: false
    },
    // // 单选选中的数据
    // radioIdSelect: {
    //   type: String,
    //   default: ''
    // },
    // 复选列的宽度
    selectionWidth: {
      type: String,
      default: '50'
    },
    // table项
    columns: {
      type: Array,
      require: true,
      default: []
    },
    // 操作列信息 
    handleColumnProp: {
      type: Object,
      default: () => ({
        label: '操作'
      })
    },
    // 操作按钮
    handleColumn: {
      type: Array,
      default: () => []
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: false
    },
    // 当前页
    currentPage: {
      type: Number,
      default: 1
    },
    // 每页条数
    pageSize: {
      type: Number,
      default: 10
    },
    // 总数
    totalCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // table 行样式
      headerCellStyle: {
        background: '#EEF7FF',
        color: '#4D91F8'
      },
      radioIdSelect: '',
      valueKey: 1,
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    // 操作方法
    operate(row, item) {
      this.$emit('operateBtn', row, item)
    },
    //列表选中的值
    selectList(row) {
      this.$emit('selectBtn', row)
    },
    // 分页
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val)
    },
    // 分页
    handleSizeChange(val) {
      this.$emit('handleSizeChange', val)
    },
    handleSelectionChange(index, row) {
      this.radioIdSelect = row
      console.log(index, '11111111');
      console.log(row, '11111111');
      this.$emit('handleSelectionChange', index, row)
    }

  },
  watch: {

  }
}

</script>

<style scoped>
.fl {
  float: left;
}

.fr {
  float: right;
}

.table_content {
  height: 100%;
}

.table_content>>>.el-table__body-wrapper {
  height: calc(100% - 50px);
  overflow-y: scroll;
}

.tb-container {
  width: 100%;
  border: 1px solid #EBEEF5;
  /* height: calc(100% - 125px); */
}

.paginationClass {
  margin-top: 20px;
}
</style>