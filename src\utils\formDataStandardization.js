/**
 * 表单数据标准化处理工具类
 * [特别注意]采用针对单一数据无限扩充方式，即一个方法只能对某个数据进行标准化
 */

import { getDateTime } from './utils'
import { dateFormat } from './moment'

// 数据类型数组
export const typeArr = [
  '[object Undefined]',
  '[object Number]',
  '[object String]',
  '[object Array]',
  '[object Date]',
]

/**
 * 公用方法
 * 对指定字段进行标准化
 * Obj: 需要标准化的对象数据
 * fieldName: 需要标准化的字段
 * type: 需要标准化的字段的期望类型
 * valType: 需要标准化的字段值的期望类型（例如：值为数组，该类型即为数组内元素的期望类型等）
 * defaultVal: 需要标准化的字段的默认值
 *
 * 注意：
 * 如果存在默认值，则直接替换成默认值，故在标准化的时候 defaultVal 需要注意类型的设置
 * 因为有数字类型0的存在，判断是否有值需使用类型判断
 *
 * 规则：
 * 1是否存在默认值
 * 1-1是：直接赋值为默认值
 * 1-2否：字段是否有值
 *     1-2-1有：判断字段类型与期望类型 type 是否相同
 *       1-2-1-1相同：判断是否有字段值的期望类型 valType
 *         1-2-1-1-1有：判断字段值的类型与字段值的期望类型 valType 是否相同
 *           1-2-1-1-1-1相同：
 *             不做处理
 *           1-2-1-1-1-2不同：
 *             将值转为对应字段值期望类型 valType
 *         1-2-1-1-2无：
 *           不做处理
 *       1-2-1-2不同：判断是否有字段值期望类型 valType
 *         1-2-1-2-1有：判断当前字段值类型是否与字段期望类型 valType 相同
 *           1-2-1-2-1-1相同：
 *             直接加工为字段期望类型 type
 *           1-2-1-2-1-2不同：
 *             将当前字段的值转成字段值期望类型 valType，然后加工成字段期望类型 type
 *         1-2-1-2-2无：
 *           将当前值加工为字段期望类型 type
 *     1-2-2无：
 *       不做处理(没有值且没有默认值)
 */
export const standardField = (obj, fieldName, type, valType, defaultVal) => {
  let fieldType = Object.prototype.toString.call(obj[fieldName])
  let defaultValType = Object.prototype.toString.call(defaultVal)
  // 1是否存在默认值
  if (defaultValType != typeArr[0]) {
    obj[fieldName] = defaultVal
  } else {
    // 1-2字段是否有值
    if (fieldType != typeArr[0]) {
      // 判断字段类型与期望类型 type 是否相同
      if(fieldType == type) {
        // 1-2-1-1判断是否有字段值的期望类型 valType
        if(valType) {
          // 1-2-1-1-1判断字段值的类型与字段值的期望类型 valType 是否相同
          if(fieldType == valType) {
            // 1-2-1-1-1-1[不做处理]
            console.log('[字段类型与期望类型相同]['+fieldType+']['+valType+']不做处理')
          } else {
            // 1-2-1-1-1-2[处理]将值转为对应字段值期望类型 valType
            console.log('[字段类型与期望类型相同]将值['+fieldType+']转为对应字段值期望类型['+valType+']')
          }
        } else {
          // 1-2-1-1-2[不做处理]
          console.log('[字段类型与期望类型相同]['+fieldType+']['+valType+']不做处理')
        }
      } else {
        // 1-2-1-2判断是否有字段值期望类型 valType
        if(valType) {
          if(fieldType == valType) {
            // 1-2-1-2-1-1[处理]直接加工为字段期望类型 type
            console.log('[字段类型与期望类型不同]将值['+fieldType+']转为对应字段期望类型['+type+']')
            // 标准化字段值的期望类型
            standardFieleType(obj, fieldName, type)
          } else {
            // 1-2-1-2-1-2[处理]将当前字段的值转成字段值期望类型 valType，然后加工成字段期望类型 type
            console.log('[字段类型与期望类型不同]将值类型转为字段值期望类型['+valType+']，并将字段转为类型['+type+']')
            // 标准化字段值的期望类型
            stanardValType(obj, fieldName, valType)
            console.log('aaa', JSON.parse(JSON.stringify(obj)))
            standardFieleType(obj, fieldName, type)
          }
        } else {
          // 1-2-1-2-2[处理]将当前值加工为字段期望类型 type
          console.log('[字段类型与期望类型不同]将值转为字段期望类型['+type+']')
        }
      }
    } else {
      // 1-2-2[不做处理](没有值且没有默认值)
      console.log('[类型不同]['+fieldType+']['+valType+']不做处理(没有值且没有默认值)')
    }
  }
}

/**
 * 标准化字段值的期望类型
*/
export const stanardValType = (obj, fieldName, valType) => {
  let fieldType = Object.prototype.toString.call(obj[fieldName])
  // 数字类型
  if(fieldType == typeArr[1]) {
    console.log('数字类型')
    if(valType == typeArr[1]) {
      // 不用处理
    } else if(valType == typeArr[2]) {
      obj[fieldName] += ''
    } else if(valType == typeArr[3]) {
      // (暂时没想好怎么处理)
    } else if(valType == typeArr[4]) {
      try {
        obj[fieldName] = new Date(obj[fieldName])
      } catch (error) {
        console.error(error)
        // 不做多余的处理
      }
    } else {
      console.warn('未定义字段值期望类型['+valType+']')
    }
  }
  // 字符串类型
  if(fieldType == typeArr[2]) {
    console.log('字符串类型')
    if(valType == typeArr[1]) {
      try {
        // parseInt或出现给定字符串 123adff，然后parseInt后得到 123 的情况，实际是希望像直接给定 adff，parseInt后得到NaN
        let reg = /\D/g
        let matchArr = obj[fieldName].match(reg)
        if(matchArr && matchArr.length > 0) {
          // 存在非数字字符，不做处理
        } else {
          console.log('不存在非数字字符')
          obj[fieldName] = parseInt(obj[fieldName])
        }
      } catch (error) {
        console.error(error)
      }
    } else if(valType == typeArr[2]) {
      // 不用处理
    } else if(valType == typeArr[3]) {
      // (暂时没想好怎么处理)
      console.log('(暂时没想好怎么处理)')
    } else if(valType == typeArr[4]) {
      try {
        obj[fieldName] = new Date(getDateTime(obj[fieldName]))
      } catch (error) {
        console.error(error)
        // 不做多余的处理
      }
    } else {
      console.warn('未定义字段值期望类型['+valType+']')
    }
  }
  // 数组类型（暂未想好怎么处理）
  // 日期类型
  if(fieldType == typeArr[4]) {
    console.log('日期类型')
    if(valType == typeArr[1]) {
      try {
        obj[fieldName] = new Date(obj[fieldName]).getTime()
      } catch (error) {
        console.error(error)
      }
    } else if(valType == typeArr[2]) {
      //
      try {
        obj[fieldName] = dateFormat(obj[fieldName])
      } catch (error) {
        console.error(error)
      }
    } else if(valType == typeArr[3]) {
      // (暂时没想好怎么处理)
    } else if(valType == typeArr[4]) {
      // 不用处理
    } else {
      console.warn('未定义字段值期望类型['+valType+']')
    }
  }
}

/**
 * 标准化字段的期望类型
*/
export const standardFieleType = (obj, fieldName, type) => {
  // 数组类型
  console.log('数组类型')
  if(type == typeArr[3]) {
    let tempArr = []
    tempArr = obj[fieldName].split(/[/,]/)
    obj[fieldName] = tempArr
  }
}

/**
 * 涉密人员表数据标准化
 * 参数为 Object
 */
export const smryDataStandard = (params) => {
  // let str = '123'
  // console.log(str, parseInt(str))
  // let reg = /\D/g
  // console.log(str, str.match(reg))
  // return
  if (!params) {
    return
  }
  // standardField(params, 'aaa', typeArr[2], typeArr[2], 'aaa-value')
  standardField(params, 'gwmc', typeArr[3], typeArr[2])
  console.log('params', params)
}
