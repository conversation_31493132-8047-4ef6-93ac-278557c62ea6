// import Vue from 'vue';
// import Vuex from 'vuex';
// // 定义方法，操作state的数据
// import modules from './modules'
// Vue.use(Vuex)

// const store = new Vuex.Store({
//     state: {
//         //公共
//         comm: {
//             loading: false,
//             login: {
//                 memberId: '',
//                 userData: ''
//             },
//             indexConf: {
//                 isTab: false, //是否显示tab页
//                 isBack: false, //是否显示返回
//                 title: '' //标题
//             }
//         }
//     },
//     mutations: {
//         /*
//          * 修改header的信息
//          *比如是否有回退按钮，标题显示内容
//          * */
//         changeIndexConf: (state, data) => {

//             Object.assign(state.comm.indexConf, data);
//         }
//     },
//     actions: {     
//     },
//     getter: {
//     }
// });
// export default new Vuex.Store({
//     modules,
//     // plugins: [
//     //   createPersistedState(),
//     //   // createSharedMutations() // 多进程共享vuex状态插件
//     // ],
//     strict: process.env.NODE_ENV !== 'production'
//   })
import Vue from 'vue'
import Vuex from 'vuex'
import createVuexAlong from "vuex-along";
// import { createPersistedState, createSharedMutations } from 'vuex-electron'

// 定义方法，操作state的数据
import modules from './modules'

Vue.use(Vuex)

export default new Vuex.Store({
  modules,
  plugins: [
    createVuexAlong({
      // 设置保存的集合名字，避免同站点下的多项目数据冲突
      name: "hello-vuex-along",
      local: {
        list: ["ma"],
        // 过滤模块 ma 数据， 将其他的存入 localStorage
        isFilter: true,
      }
      // session: {
      //   // 保存模块 ma 中的 a1 到 sessionStorage
      //   list: ["ma.a1"],
      // },
    }),
    // createPersistedState(),
    // createSharedMutations() // 多进程共享vuex状态插件
  ],
  strict: process.env.NODE_ENV !== 'production'
})

