<!--  -->
<template>
  <div style="width: 100%; height: 100%" class="" v-loading="pageLoading">
    <div class="ewm-box">
      <img src="./img/ewm.png" alt="" />
    </div>
    <div style="display: flex; justify-content: center">
      <el-input
        ref="inputField"
        v-model="value"
        placeholder="请扫描工单条形码"
        class="smerm-box"
        @keyup.enter.native="handleEnter"
        type="text"
      >
        请扫描工单条形码
      </el-input>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getScanCodeResult, getOpneLock } from "../../../api/shma.js";
export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      value: "", //输入框内容
      pageLoading: false, //页面加载loading状态
      password: "", //密码明文
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    value: {
      handler: function (val, oldV) {
        
        //监听登录的密码输入框，将密文显示为*
        this.value = this.value.replace(/./g, "*");
        if (oldV.length > val.length) {
          //进行删除操作   this.password就是定义在data中用来存实际密码的变量
          this.password = this.password.substring(
            0,
            this.password.length - 1
          );
        } else {
          this.password += val.replace(/[*]/g, "");
        }
      },
      deep: true,
    },
  },
  //方法集合
  methods: {
    async handleEnter() {
      this.pageLoading = true;
      getScanCodeResult({
        scanCode: this.password,
      }).then(async (res) => {
        if (res.code == 10000) {
          if (res.data.operation == 1) {
            let data = await getOpneLock();
            if (data.code == 10000 && data.data.status == 'ok') {
              let messages = data.data.point + '开锁成功！'
              this.$message.success(messages);
              this.$router.push({
                path: "/xjlc",
                query: {
                  scanCode: this.password,
                },
              });
            }
            else {
                this.$message.error("开锁失败");
            }
          }
          if (res.data.operation == 2) {
            this.$router.push({
              path: "/sbqyxx",
              query: {
                equipmentId: res.data.equipmentId,
                relocationCabinetCode: res.data.relocationCabinetCode,
                relocationCabinetName: res.data.relocationCabinetName,
                relocationComputerRoomName: res.data.relocationComputerRoomName,
                relocationInstitution: res.data.relocationInstitution,
                relocationLocation: res.data.relocationLocation,
                flag: res.data.flag,
                area: res.data.area,
                scanCode: this.password,
              },
            });
          }
          if (res.data.operation == 3) {
            this.$router.push({
              path: "/sbxhxx",
              query: {
                equipmentId: res.data.equipmentId,
                relocationCabinetName: res.data.relocationCabinetName,
                relocationComputerRoomName: res.data.relocationComputerRoomName,
                relocationInstitution: res.data.relocationInstitution,
                relocationLocation: res.data.relocationLocation,
                area: res.data.area,
                scanCode: this.password,
              },
            });
          }
          if (res.data.operation == 4) {
            this.$router.push({
              path: "/gzcllc",
              query: {
                cabinetCode: res.data.cabinetCode,
                equipmentId: res.data.equipmentId,
                equipmentName: res.data.equipmentName,
                equipmentSerialNumber: res.data.equipmentSerialNumber,
                scanCode: this.password,
              },
            });
          }
        } else {
          this.$message.error(res.message);
        }
        this.pageLoading = false;
        this.password = "";
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.$refs.inputField.focus();
  },
  //生命周期 - 创建之前
  beforeCreate() {},
  //生命周期 - 挂载之前
  beforeMount() {},
  //生命周期 - 更新之前
  beforeUpdate() {},
  //生命周期 - 更新之后
  updated() {},
  //生命周期 - 销毁之前
  beforeDestroy() {},
  //生命周期 - 销毁完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {},
};
</script>
<style scoped>
.ewm-box {
  width: 348px;
  height: 348px;
  background: url("./img/ewmk.png");
  background-size: cover;
  margin: 0 auto;
  margin-top: 194px;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}
.smerm-box {
  background-image: linear-gradient(268deg, #3393e6 0%, #175ac9 99%);
  box-shadow: 0px 2px 20px 0px rgba(26, 95, 204, 0.25);
  border-radius: 25px;
  width: 342px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #ffffff;
  font-weight: 500;
  margin: 0 auto;
  margin-top: 40px;
}
/deep/ .el-loading-parent--relative {
  position: static;
}
/deep/ .el-input__inner {
  background-color: transparent;
  border: none;
  text-align: center;
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #ffffff;
  font-weight: 500;
}
.smerm-box ::placeholder {
  color: #fff;
}
</style>
