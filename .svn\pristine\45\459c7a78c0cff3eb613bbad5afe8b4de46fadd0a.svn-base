import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//添加设备销毁登记信息
export const saveSbglSbxhdj = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_Sbxhdj/saveSbglSbxhdj", 'post',data)
//通过jlid查询设备销毁登记记录
export const selectByIdSbglSbxhdj = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_Sbxhdj/selectByIdSbglSbxhdj", 'get',data)
//分页条件查询设备销毁登记记录
export const selectPageSbglSbxhdj = data => createAPI(BASE_URL+"/api/gzl_01_01/sbgl_Sbxhdj/selectPageSbglSbxhdj", 'get',data)

// 添加设备销毁信息
export const saveSbglSbxh = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/saveSbglSbxh", 'post',data)
// 2、通过slid删除设备销毁信息
export const deleteSbglSbxh = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/deleteSbglSbxh", 'post',data)
// 根据jlid修改设备销毁信息
export const updateSbglSbxh = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/updateSbglSbxh", 'post',data)
// 通过jlid查询设备销毁记录
export const selectByIdSbglSbxh = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/selectByIdSbglSbxh", 'get',data)
// 通过slid查询设备销毁记录
export const selectBySlidSbglSbxh = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/selectBySlidSbglSbxh", 'get',data)

// 通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
// 根据原jlid查询设备清单
export const getSbqdListByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/getSbqdListByYjlid", 'get',data)
// 设备清单批量添加
export const savaSbqdBatch = data => createAPI(BASE_URL+"/sbgl/sbqd/savaSbqdBatch", 'post',data)
// 根据原jlid删除原jlid下的设备清单
export const deleteSbqdByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/deleteSbqdByYjlid", 'post',data)

// 分页条件查询设备销毁记录
export const selectPageSbglSbxh = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/selectPageSbglSbxh", 'get',data)
// 通过slid查询jlid
export const selectSlidByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/selectSlidByJlid", 'get',data)
export const selectSlidShByJlid = data => createAPI(BASE_URL+"/api/gzl_01_01/SbglSbxh/selectSlidByJlid", 'get',data)