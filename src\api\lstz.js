import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
// 获取涉密岗位变更全部数据
export const getGwbg = data => createAPI(BASE_URL+"/rygl/mjbg/getAllMjbg", 'get',data)
// 获取离岗离职全部数据
export const getLglz = data => createAPI(BASE_URL+"/rygl/lghz/getAllLzlg", 'get',data)
// 获取涉密场所全部数据
export const getCsgl = data => createAPI(BASE_URL+"/csgl/csdj/getAllCsdjList", 'get',data)
// 获取场所变更全部数据
export const getCsbg = data => createAPI(BASE_URL+"/csgl/csbgdj/getAllCsbgdj", 'get',data)
// 获取涉密计算机全部数据
export const getSmjsj = data => createAPI(BASE_URL+"/sbgl/smjsj/getAllSmjsj", 'get',data)
// 获取非涉密计算机全部数据
export const getFsmjsj = data => createAPI(BASE_URL+"/sbgl/fmjsj/getAllFmjsj", 'get',data)
// 获取涉密移动存储介质全部数据
export const getSmydccjz = data => createAPI(BASE_URL+"/sbgl/ydccjz/getAllYdccjz", 'get',data)
// 获取账涉密办公自动化设备全部数据
export const getSmbgzdhsb = data => createAPI(BASE_URL+"/sbgl/smxxsb/getAllSmxxsb", 'get',data)
// 获取非涉密办公自动化设备全部数据
export const getFsmbgzdhsb = data => createAPI(BASE_URL+"/sbgl/fmxxsb/getAllFmxxsb", 'get',data)



// 添加涉密岗位历史台账
export const addSmgwglHistory = data => createAPI(BASE_URL+"/lstz/rygl/gwdj/saveSmgw", 'post',data)
// 添加在岗涉密人员历史台账
export const addSmryHistory = data => createAPI(BASE_URL+"/lstz/rygl/yhxx/saveSmry", 'post',data)
// 添加涉密岗位变更历史台账
export const addRymjbghzHistory = data => createAPI(BASE_URL+"/lstz/rygl/mjbg/saveMjbg", 'post',data)
// 添加离职离岗历史台账
export const addLglzHistory = data => createAPI(BASE_URL+"/lstz/rygl/lghz/saveLzlg", 'post',data)
// 添加场所管理历史台账
export const addSmcsglHistory = data => createAPI(BASE_URL+"/lstz/csgl/csdj/saveCsdj", 'post',data)
// 添加场所变更历史台账
export const addCsbgHistory = data => createAPI(BASE_URL+"/lstz/csgl/csbgdj/saveCsbg", 'post',data)
// 添加涉密计算机历史台账
export const addSmjsjHistory = data => createAPI(BASE_URL+"/lstz/sbgl/smjsj/saveSmjsj", 'post',data)
// 添加非涉密计算机历史台账
export const addFsmjsjHistory = data => createAPI(BASE_URL+"/lstz/sbgl/fmjsj/saveFmjsj", 'post',data)
// 添加涉密移动存储介质历史台账
export const addSmydccjzHistory = data => createAPI(BASE_URL+"/lstz/sbgl/ydccjz/saveYdccjz", 'post',data)
// 添加账涉密办公自动化设备历史台账
export const addBgzdhsbHistory = data => createAPI(BASE_URL+"/lstz/sbgl/smxxsb/saveSmxxsb", 'post',data)
// 添加非涉密办公自动化设备历史台账
export const addfBgzdhsbHistory = data => createAPI(BASE_URL+"/lstz/sbgl/fmxxsb/saveFmxxsb", 'post',data)


// 查询全部历史台账涉密岗位信息
export const getSmgwHistory = data => createAPI(BASE_URL+"/lstz/rygl/gwdj/getGwxxList", 'get',data)
export const getSmryHistory = data => createAPI(BASE_URL+"/lstz/rygl/yhxx/getYhxxList", 'get',data)
export const getRymjbghzHistory = data => createAPI(BASE_URL+"/lstz/rygl/mjbg/getMjbgList", 'get',data)
export const getLglzHistory = data => createAPI(BASE_URL+"/lstz/rygl/lghz/getLzlgList", 'get',data)
export const getSmcsglHistory = data => createAPI(BASE_URL+"/lstz/csgl/csdj/getCsglList", 'get',data)
export const getSmCsbgHistory = data => createAPI(BASE_URL+"/lstz/csgl/csbgdj/getCsbgList", 'get',data)


// 删除涉密岗位历史台账
export const removeSmgwHistory = data => createAPI(BASE_URL+"/lstz/rygl/gwdj/removeSmgw", 'post',data)
export const removeSmryHistory = data => createAPI(BASE_URL+"/lstz/rygl/yhxx/removeSmry", 'post',data)
export const removeRymjbghzHistory = data => createAPI(BASE_URL+"/lstz/rygl/mjbg/removeMjbg", 'post',data)
export const removeLglzHistory = data => createAPI(BASE_URL+"/lstz/rygl/lghz/removeLzlg", 'post',data)
export const removeCsglHistory = data => createAPI(BASE_URL+"/lstz/csgl/csdj/removeCsdj", 'post',data)
export const removeCsbgHistory = data => createAPI(BASE_URL+"/lstz/csgl/csbgdj/removeCsbgdj", 'post',data)


// 查询历史数据带分页
export const getSmgwHistoryPage = data => createAPI(BASE_URL+"/lstz/rygl/gwdj/getGwxxPage", 'get',data)
export const getSmryHistoryPage = data => createAPI(BASE_URL+"/lstz/rygl/yhxx/getYhxxPage", 'get',data)
export const getGwbgHistoryPage = data => createAPI(BASE_URL+"/lstz/rygl/mjbg/getMjbgPage", 'get',data)
export const getLzlgHistoryPage = data => createAPI(BASE_URL+"/lstz/rygl/lghz/getLzlgPage", 'get',data)
export const getCsglHistoryPage = data => createAPI(BASE_URL+"/lstz/csgl/csdj/getCsdjxxPage", 'get',data)
export const getCsbgHistoryPage = data => createAPI(BASE_URL+"/lstz/csgl/csbgdj/getCsbgxxPage", 'get',data)
export const getSmjsjHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/smjsj/getSmjsjPage", 'get',data)
export const getFmjsjHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/fmjsj/getFmjsjPage", 'get',data)
export const getYdccjzHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/ydccjz/getYdccjzPage", 'get',data)
export const getSmbgzdhHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/smxxsb/getSmxxsbPage", 'get',data)
export const getFmbgzdhHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/fmxxsb/getFmxxsbPage", 'get',data)
export const getSmwlsbHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/smwlsb/getSmwlsbPage", 'get',data)
export const getFmwlsbHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/fmwlsb/getFmwlsbPage", 'get',data)
export const getAqcpHistoryPage = data => createAPI(BASE_URL+"/lstz/sbgl/xxsb/getAqcpPage", 'get',data)
export const getSmztHistoryPage = data => createAPI(BASE_URL+"/lstz/ztgl/zt/getZtPage", 'get',data)
export const getDmzrrHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/dmzrr/getDmzrrPage", 'get',data)
export const getDmsqHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/dmsq/getDmsqPage", 'get',data)
export const getGjmmsxHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/gjmmsx/getGjmmsxPage", 'get',data)
export const getLsDmsxfjList = data => createAPI(BASE_URL+"/lstz/dmgl/dmsxylbfj/getDmsxfjList", 'get',data)
export const getDmpxHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/dmpx/getDmpxPage", 'get',data)
export const getNdtjHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/ndtj/getNdtjPage", 'get',data)
export const getBmqsxqdqkHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/bmqsxqdqk/getBmqsxqdqkPage", 'get',data)
export const getZfcgxmqkHistoryPage = data => createAPI(BASE_URL+"/lstz/dmgl/smzfcgxmqk/getSmzfcgxmqkPage", 'get',data)

// 一键生成历史台账
export const scSmryHistoryDatas = data => createAPI(BASE_URL+"/dbgz/createSmryTz", 'post',data)
export const scSmcsHistoryDatas = data => createAPI(BASE_URL+"/dbgz/createSmcsTz", 'post',data)
export const scSmsbHistoryDatas = data => createAPI(BASE_URL+"/dbgz/createSmsbTz", 'post',data)
export const scSmztHistoryDatas = data => createAPI(BASE_URL+"/dbgz/createSmztTz", 'post',data)
export const scSmsxHistoryDatas = data => createAPI(BASE_URL+"/dbgz/createSmsxTz", 'post',data)
// 更新待办工作状态
export const reviseDbgzStatus = data => createAPI(BASE_URL+"/dbgz/updateDbgzzt", 'post',data)







