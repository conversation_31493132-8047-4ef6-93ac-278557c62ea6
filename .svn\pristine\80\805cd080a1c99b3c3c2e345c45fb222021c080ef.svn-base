<!--  -->
<template>
  <div class="box">
    <div class="top-box">
      <div>
        <img src="./img/title.png" alt="" />
      </div>
      <div class="top-title">故障处理流程</div>
    </div>
    <div>
      <el-form class="inputcss" ref="form" :model="form" label-width="100px" label-position="left"
               style="margin-top: 20px;">
        <div style="display: flex;">
          <el-form-item label="设备名称" style="margin-left: 20px;">
            <el-input v-model="form.sbmc"></el-input>
          </el-form-item>
          <el-form-item label="设备编号" style="margin-left: 20px;">
            <el-input v-model="form.sbbh"></el-input>
          </el-form-item>
          <div class="cxbtn" @click="getequipmentByRoomList">查询</div>
        </div>
      </el-form>
      <!-- <div class="buttonw btnc1" @click="savetj()">提交</div> -->
    </div>
    <div style="width: 100%;margin-top: 20px;">
      <el-table
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :header-cell-style="tableHeaderCellStyle"
        :cell-style="tableCellStyle"
        max-height="800px"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column type="index" label="序号" width="60" align="center">
        </el-table-column>
        <el-table-column prop="cabinetCode" label="机柜编号" align="center">
        </el-table-column>
        <el-table-column prop="equipmentCode" label="设备编号" align="center">
        </el-table-column>
        <el-table-column prop="equipmentName" label="设备名称" align="center">
        </el-table-column>
        <!-- <el-table-column prop="gzyy" label="故障原因" align="center">
        </el-table-column> -->
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <div
              style="display: flex;align-items: center;justify-content: center;"
            >
              <div class="btncz1" @click="handleEdit(scope.$index, scope.row)">
                处理
              </div>
              <!-- <div
                class="btncz1"
                style="margin-left: 20px;"
                @click="handleDelete(scope.$index, scope.row)"
              >
                忽略
              </div> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 15px">
                <el-pagination
                  background
                  @current-change="handleCurrentChange"
                  @size-change="handleSizeChange"
                  :pager-count="5"
                  :current-pageNo="page"
                  :pageNo-sizes="[5, 10, 20, 30]"
                  :pageNo-size="pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                >
                </el-pagination>
              </div>
    </div>
  </div>
</template>

<script>
import { init } from 'echarts';
import {getequipmentByRoom} from "../../../api/jfxj";

//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  name: "",
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {},
  data() {
    //这里存放数据
    return {
      form: {
        sbmc: "",
        sbbh: "",
      },
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: [

      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {

    handleCurrentChange(){},
    handleSizeChange(){},
    tableCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #333333;font-weight: 400;";
    },
    tableHeaderCellStyle() {
      return "font-family: SourceHanSansSC-Normal;font-size: 16px;color: #1766D1;font-weight: 400;background: #D7ECFF;";
    },
    handleSelectionChange(val) {},
    handleEdit(index, row) {
      this.$router.push({
        path: "/gzcllcbz",
        query: {
          equipmentCode: row.equipmentCode,
          equipmentSerialNumber: row.equipmentSerialNumber,
        }
      });
    },
    handleDelete(index, row) {},
    async init(){
      const params = {
        scanCode: this.$route.query.scanCode,
        flag: 3,
        pageNum: this.page,
        pageSize: this.pageSize,
      };
      const response = await getequipmentByRoom(params);
      const { code, data } = response;
      if (code === 10000) {
        this.tableData = data.records;
        this.total = data.total;
      }
    },
    async getequipmentByRoomList() {
        const params = {
          scanCode: this.$route.query.scanCode,
          flag: 3,
          equipmentName: this.form.sbmc,
          equipmentCode: this.form.sbbh,
          pageNum: this.page,
          pageSize: this.pageSize,
        };
        const response = await getequipmentByRoom(params);
        const { code, data } = response;
        if (code === 10000) {
          this.tableData = data.records;
          this.total = data.total;
        }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.init()
  },
  //生命周期 - 创建之前
  beforeCreate() {},
  //生命周期 - 挂载之前
  beforeMount() {},
  //生命周期 - 更新之前
  beforeUpdate() {},
  //生命周期 - 更新之后
  updated() {},
  //生命周期 - 销毁之前
  beforeDestroy() {},
  //生命周期 - 销毁完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {}
};
</script>
<style scoped>
.box {
  width: 1580px;
  margin: 0 auto;
}

.cxbtn {
  width: 72px;
  height: 32px;
  background: #3e9efe;
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0.07px;
  font-weight: 400;
  text-align: center;
  line-height: 32px;
  margin-left: 20px;
  margin-top: 5px;
}
.top-box {
  width: 100%;
  display: flex;
  border-bottom: 1px solid #e5e5e5;
  margin-top: 20px;
}
.top-title {
  font-family: SourceHanSansSC-Medium;
  font-size: 22px;
  color: #080808;
  font-weight: 500;
  margin-left: 10px;
}
.mg20 {
  margin-right: 20px;
}
.buttonw {
  width: 72px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #fff;
  border-radius: 4px;
}
.btnc1 {
  background-color: #3e9efe;
}
.btnc2 {
  background-color: #3ecafe;
  margin-left: 20px;
}
.btncz1 {
  font-family: SourceHanSansSC-Normal;
  font-size: 16px;
  color: #1766d1;
  font-weight: 400;
  cursor: pointer;
}
/deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover:nth-child(even)
  > td {
  background-color: #dce8fb !important;
}
/deep/
  .el-table--enable-row-hover
  .el-table__body
  tr:hover:nth-child(odd)
  > td {
  background-color: #dce8fb !important;
}
/deep/ .el-table__body tr:nth-child(even) {
  background-color: #dce8fb; /* 偶数行（斑马线）的默认背景色 */
}
/deep/ .el-pagination__total{
  font-size: 16px !important;
}
/deep/ .el-pagination__jump{
  font-size: 16px !important;
}
</style>
