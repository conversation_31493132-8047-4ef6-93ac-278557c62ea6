import {createAPI, createFileAPI,createDown, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1.添加载体签收登记记录
export const addZtqsdj = data => createAPI(BASE_URL+"/ztgl/qscddj/addZtqsdj", 'post',data)
//删除载体签收登记记录
export const removeZtQsdj = data => createAPI(BASE_URL+"/ztgl/qscddj/removeZtQsdj", 'post',data)
//修改载体签收登记记录
export const updateZtqsdjByJlid = data => createAPI(BASE_URL+"/ztgl/qscddj/updateZtqsdjByJlid", 'post',data)
//查询载体签收登记带分页
export const selectZtQsdjPage = data => createAPI(BASE_URL+"/ztgl/qscddj/selectZtQsdjPage", 'get',data)
//载体签收登记导出
export const exportZtqsExcel = data => createDown(BASE_URL+"/ztgl/qscddj/exportZtqsExcel", 'get',data)

export const getJlidBySlid = data => createAPI(BASE_URL+"/ZtglWfcddj/getJlidBySlid", 'get',data)

export const getQsdjByJlid = data => createAPI(BASE_URL+"/ztgl/qscddj/getQsdjByJlid", 'get',data)
export const updateZtglWfcddj = data => createAPI(BASE_URL+"/ZtglWfcddj/updateZtglWfcddj", 'post',data)
