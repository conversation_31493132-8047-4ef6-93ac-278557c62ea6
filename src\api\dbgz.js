import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
// 待办工作
export const getDbgzStatus = data => createAPI(BASE_URL+"/dbgz/getDbgzzt", 'get',data)
//获取uuid
export const getTzInfoDatas = data => createAPI(BASE_URL+"/dmb/dbgz/getTzInfo", 'get',data)
//判断当前日期是否在待办工作生成日期区间
export const getSfDbgzRqqj = data => createAPI(BASE_URL+"/dbgz/getSfDbgzRqqj", 'get',data)
// 判断当前日期是否在年度涉密人员上报日期区间
export const getSfDbgzSmryRqqj = data => createAPI(BASE_URL+"/dbgz/getSfDbgzSmryRqqj", 'get',data)
// 判断当前日期是否在年度定密事项上报日期区间
export const getSfDbgzDmsxRqqj = data => createAPI(BASE_URL+"/dbgz/getSfDbgzDmsxRqqj", 'get',data)


