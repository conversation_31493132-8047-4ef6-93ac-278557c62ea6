import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''

// 通过类型和责任部门获取设备定密审批登记表
export const selectSbglDmdj = data => createAPI(BASE_URL+"/SbglDmdj/selectSbglDmdj", 'get',data)
// 根据原jlid查询设备清单
export const getSbqdListByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/getSbqdListByYjlid", 'get',data)
// 设备清单批量添加
export const savaSbqdBatch = data => createAPI(BASE_URL+"/sbgl/sbqd/savaSbqdBatch", 'post',data)
// 根据原jlid删除原jlid下的设备清单
export const deleteSbqdByYjlid = data => createAPI(BASE_URL+"/sbgl/sbqd/deleteSbqdByYjlid", 'post',data)
//日常工作
//设备超期借用提交
export const submitSbCqjy = data => createAPI(BASE_URL+"/sbgl_sbcqjy/submitSbCqjy", 'post',data)
//查询设备超期借用-分页
export const selectSbCqjyPage = data => createAPI(BASE_URL+"/sbgl_sbcqjy/selectSbCqjyPage", 'get',data)
//根据jlid查询设备超期借用
export const getSbCqjyByJlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getSbCqjyByJlid", 'get',data)
//根据slid查询设备超期借用
export const getSbCqjyBySlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getSbCqjyBySlid", 'get',data)
//删除设备超期借用
export const deleteSbCqjy = data => createAPI(BASE_URL+"/sbgl_sbcqjy/deleteSbCqjy", 'post',data)
//修改设备超期借用
export const updateSbCqjy = data => createAPI(BASE_URL+"/sbgl_sbcqjy/updateSbCqjy", 'post',data)
//通过slid查询携带外出/借阅表的jlid
export const getSbXdJyJlidBySlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getSbXdJyJlidBySlid", 'get',data)
//通过slid查询超期借用表的jlid
export const getSbCqjyJlidBySlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getSbCqjyJlidBySlid", 'get',data)
//通过分类、类型、bmbh、时间查询带分页
export const getWghSb = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getWghSb", 'get',data)

// //通过slid获取外出携带，借阅的记录id
// export const getSbXdJyJlidBySlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/getSbXdJyJlidBySlid", 'get',data)

export const updateSbXdJydjByYjlid = data => createAPI(BASE_URL+"/sbgl_sbcqjy/updateSbXdJydjByYjlid", 'post',data)



