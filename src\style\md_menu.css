/**锚点菜单**/
.md-menu {
  background: rgba(255, 255, 255, 0.9);
  position: fixed;
  right: 0;
  bottom: 30px;
  /* width: 400px; */
  height: 600px;
  z-index: 999;
  border-radius: 10px 0 0 10px;
  display: flex;
}
.md-menu .md-left {
  height: 100%;
  width: 10px;
  background: #66b1ff;
  border-radius: 10px 0 0 10px;
  /* border-left: 7px dotted white; */
  box-sizing: border-box;
}
.md-menu .md-left:hover {
  cursor: pointer;
}
.md-menu .md-right {
  /* flex: 1; */
  width: 500px;
  overflow-y: scroll;
  border-top: 5px solid rgb(243, 243, 243);
  border-left: 5px solid rgba(255, 255, 255, 0);
  border-right: 5px solid rgba(255, 255, 255, 0);
  border-bottom: 5px solid rgb(243, 243, 243);
  padding: 10px 5px;
  box-sizing: border-box;
}
.md-menu .md-right .md-article a {
  text-decoration: none;
  font-size: 13px;
  color: #909090;
}
.md-menu .md-right .md-article a:hover {
  text-decoration: underline;
}
.md-menu .md-right .md-article .md-article-article {
  padding: 10px 5px;
}
.md-menu .md-right .md-article .md-article-article:hover {
  background: #f1f1f1;
}
.md-menu .md-right-margin-div {
  /* width: 1em;
  background: #eeeeee; */
  background: #D9EAFC;
  width: 20px;
}
