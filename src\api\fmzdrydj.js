import {createAPI, createFileAPI, createUploadAPI,BASE_URL} from './request'
// var BASE_URL = '/api'
// var BASE_URL = ''
//1、添加非密重点人员
export const addZdry = data => createAPI(BASE_URL+"/rygl-zdry/addZdry", 'post',data)
//2.修改非密重点人员
export const updateZdry = data => createAPI(BASE_URL+"/rygl-zdry/updateZdry", 'post',data)
//1、添加非密重点人员家庭成员
export const addZdryJtcy = data => createAPI(BASE_URL+"/rygl_zdry_jtcy/addZdryJtcy", 'post',data)
//3、产看非重点人员分页
export const selectZdryPage = data => createAPI(BASE_URL+"/rygl-zdry/selectZdryPage", 'get',data)
//5.删除非重点人员
export const deleteZdry = data => createAPI(BASE_URL+"/rygl-zdry/deleteZdry", 'post',data)
//5.删除非重点人员
export const deleteZdryJtcy = data => createAPI(BASE_URL+"/rygl_zdry_jtcy/deleteZdryJtcy", 'post',data)
//3.产看非重点人员根据rwid
export const selectZdryByRwid = data => createAPI(BASE_URL+"/rygl-zdry/selectZdryByRwid", 'get',data)
//3.根据rwid查看非密重点人员家庭成员
export const selectZdryJtcy = data => createAPI(BASE_URL+"/rygl_zdry_jtcy/selectZdryJtcy", 'get',data)
//1.添加非密重点人员审批
export const addZdryglRysc = data => createAPI(BASE_URL+"/api/gzl_01_01/zdrygl_rysc/addZdryglRysc", 'post',data)
//1.添加非密重点人员家庭成员审批
export const addZdryglRyscJtcy = data => createAPI(BASE_URL+"/zdrygl-rysc-jtcy/addZdryglRyscJtcy", 'post',data)
//2.查看非密重点人员审批分页
export const selectZdryglRyscPage = data => createAPI(BASE_URL+"/api/gzl_01_01/zdrygl_rysc/selectZdryglRyscPage", 'get',data)
//3.查看非密重点人员审批
export const selectZdryglRyscByRwid = data => createAPI(BASE_URL+"/api/gzl_01_01/zdrygl_rysc/selectZdryglRyscByRwid", 'get',data)
//3.查看非密重点人员审批
export const selectZdryglRyscByLcslid = data => createAPI(BASE_URL+"/api/gzl_01_01/zdrygl_rysc/selectZdryglRyscByLcslid", 'get',data)
//3.查看非密重点人员审批
export const updateZdryglRysc = data => createAPI(BASE_URL+"/api/gzl_01_01/zdrygl_rysc/updateZdryglRysc", 'post',data)
//查看非密重点人员是否在审批
export const selectFmrySfzsp = data => createAPI(BASE_URL+"/api/gzl_01_01/zdrygl_rysc/selectFmrySfzsp", 'get',data)
//8.删除全部非密重点人员
export const deleteAllZdry = data => createAPI(BASE_URL+"/rygl-zdry/deleteAllZdry", 'post',data)




