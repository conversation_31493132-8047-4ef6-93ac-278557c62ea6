/**
 * 异常状态码字典
 */
const errorDictionary = {
  EPERM: {
    code: 'EPERM',
    type: '权限问题',
    mark: '操作不被允许,权限不够',
    solvtion: '',
  },
  ENOENT: {
    code: 'ENOENT',
    type: '路径问题',
    mark: '文件不存在或已删除',
    solvtion: '',
  },
  ENOTDIR: {
    code: 'ENOTDIR',
    type: '路径问题',
    mark: '路径存在，但不是一个目录',
    solvtion: '',
  },
  ETIMEDOUT: {
    code: 'ETIMEDOUT',
    type: '请求问题',
    mark: '请求超时',
    solvtion: '',
  },
  EPIPE: {
    code: 'EPIPE',
    type: '流问题',
    mark: '管道损坏，写入流意外关闭',
    solvtion: '',
  },
  ENOTEMPTY: {
    code: 'ENOTEMPTY',
    type: '路径问题',
    mark: '操作的目标是一个非空的目录，而要求的是一个空目录',
    solvtion: '',
  },
  EMFILE: {
    code: 'EMFILE',
    type: '系统问题',
    mark: '打开文件过多，已超过系统运行最大数量',
    solvtion: '',
  },
  EISDIR: {
    code: 'EISDIR',
    type: '路径问题',
    mark: '要求文件路径，但给定目录路径',
    solvtion: '',
  },
  EEXIST: {
    code: 'EEXIST',
    type: '文件问题',
    mark: '目标文件已存在',
    solvtion: '',
  },
  ECONNRESET: {
    code: 'ECONNRESET',
    type: '请求问题',
    mark: '连接被重置',
    solvtion: '',
  },
  ECONNREFUSED: {
    code: 'ECONNREFUSED',
    type: '请求问题',
    mark: '连接被拒绝',
    solvtion: '',
  },
  EADDRINUSE: {
    code: 'EADDRINUSE',
    type: '地址问题',
    mark: '地址已被使用',
    solvtion: '',
  },
  EACCES: {
    code: 'EACCES',
    type: '权限问题',
    mark: '拒绝访问，暂无改目录或文件的访问权限',
    solvtion: '',
  },
}
/**
 * 错误信息处理器
 * 接收nodejs Error类
 * 返回nodejs Error类
 */
export const errorProcessor = (nodeError, solvtion) => {
  let code = nodeError.code
  //
  let resDictionary = errorDictionary[code]
  if (!resDictionary) {
    console.log('无法翻译，字典未收录，返回原始ERROE对象', nodeError)
    return nodeError
  }
  resDictionary.solvtion = solvtion
  nodeError.message = JSON.stringify(resDictionary)
  return nodeError
}
